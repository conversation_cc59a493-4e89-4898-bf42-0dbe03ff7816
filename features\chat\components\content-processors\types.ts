export interface ContentProcessorContext {
  code: string;
  lang?: string;
  theme?: string;
  isStreaming?: boolean;
}

export interface ProcessedContent {
  html: string;
  artifacts?: Array<{
    type: string;
    content: string;
    title: string;
  }>;
  skipNextProcessors?: boolean;
}

export interface ContentProcessor {
  canProcess(ctx: ContentProcessorContext): boolean;
  process(ctx: ContentProcessorContext): ProcessedContent;
  initialize(ctx: ContentProcessorContext): void;
}

export interface ContentProcessorRegistry {
  register(processor: ContentProcessor): void;
  process(ctx: ContentProcessorContext): ProcessedContent;
}
