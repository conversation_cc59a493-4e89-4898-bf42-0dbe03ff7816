'use client';

import React from 'react';
import { useForm } from '@tanstack/react-form';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { SheetFooter } from '@/components/ui/sheet';
import { IToolData } from '@/types/model';
import {
  useUpdateTool,
  IToolUpdatePayload,
} from '@/features/workspace/api/useUpdateTool';

interface ToolFormProps {
  onOpenChange: (open: boolean) => void;
  tool: IToolData;
}

// Zod schema for validation
const toolFormSchema = z.object({
  name: z.string().min(3, 'Name must be at least 3 characters'),
  description: z.string().optional(),
});

export default function ToolForm({ onOpenChange, tool }: ToolFormProps) {
  // --- Mutation Hooks ---
  const handleSuccess = () => {
    onOpenChange(false); // Close sheet on success
  };

  const { mutate: updateToolMutate, isPending: isUpdating } = useUpdateTool({
    onSuccessCallback: handleSuccess,
  });

  // --- Form Setup ---
  const form = useForm({
    defaultValues: {
      name: tool.name,
      description: tool.description || '',
    },
    onSubmit: async ({ value }) => {
      // Prepare payload
      const payload: IToolUpdatePayload = {
        name: value.name,
        description: value.description || '',
      };

      updateToolMutate({ toolId: tool.id, payload });
    },
  });

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        e.stopPropagation();
        form.handleSubmit();
      }}
    >
      <div className="grid gap-6 p-4">
        <form.Field
          name="name"
          validators={{ onChange: toolFormSchema.shape.name }}
        >
          {(field) => (
            <div className="grid gap-2">
              <Label htmlFor={field.name}>
                Name <span className="text-destructive">*</span>
              </Label>
              <Input
                id={field.name}
                name={field.name}
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="Tool name"
              />
              {field.state.meta.isTouched &&
              field.state.meta.errors.length > 0 ? (
                <p className="text-destructive text-xs">
                  {field.state.meta.errors[0]?.message}
                </p>
              ) : null}
            </div>
          )}
        </form.Field>

        {/* Description Field */}
        <form.Field name="description">
          {(field) => (
            <div className="grid gap-2">
              <Label htmlFor={field.name}>Description</Label>
              <Textarea
                id={field.name}
                name={field.name}
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="Provide a description"
                className="min-h-[100px] break-words"
                outline
              />
              {field.state.meta.isTouched &&
              field.state.meta.errors.length > 0 ? (
                <p className="text-destructive text-xs">
                  {field.state.meta.errors.join(', ')}
                </p>
              ) : null}
            </div>
          )}
        </form.Field>
      </div>

      {/* Footer with Buttons */}
      <SheetFooter className="flex flex-row items-center justify-between gap-2 p-4">
        <div className="flex items-center gap-2"></div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            type="button"
            onClick={() => onOpenChange(false)}
          >
            Cancel
          </Button>
          <form.Subscribe selector={(state) => [state.canSubmit]}>
            {([canSubmit]) => (
              <Button type="submit" disabled={!canSubmit || isUpdating}>
                {isUpdating ? 'Updating...' : 'Update'}
              </Button>
            )}
          </form.Subscribe>
        </div>
      </SheetFooter>
    </form>
  );
}
