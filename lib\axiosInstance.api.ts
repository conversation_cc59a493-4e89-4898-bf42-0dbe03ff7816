import axios from 'axios';

// Please don't change the timeout here, you can setup
// timeout for each API request, instead of the global setup
const axiosInstanceApi = axios.create({
  baseURL: `${process.env.CHAT_SERVICE_URL}/api`, // Get base URL from env
  timeout: 60 * 1000, // 1 minutes timeout
  headers: {
    Accept: 'application/json',
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
  },
});

export default axiosInstanceApi;
