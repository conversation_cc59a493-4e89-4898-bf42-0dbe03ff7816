import { useMutation } from '@tanstack/react-query';
import axiosInstanceUi from '@/lib/axiosInstance.ui';
import { ISupportTicketData, ISupportTicketResponse } from '@/types/support';
import { toast } from 'sonner';
import { API_CONFIG } from '@/config/api';

// Function to create a support ticket via POST request
const createSupportTicket = async (
  ticketData: ISupportTicketData
): Promise<ISupportTicketResponse> => {
  const response = await axiosInstanceUi.post<ISupportTicketResponse>(
    API_CONFIG.support.ticket,
    ticketData
  );
  return response.data;
};

interface UseCreateSupportTicketOptions {
  onSuccessCallback?: () => void;
  onErrorCallback?: (error: Error) => void;
}

export function useCreateSupportTicket(
  options?: UseCreateSupportTicketOptions
) {
  return useMutation({
    mutationFn: createSupportTicket,
    onSuccess: (data) => {
      toast.success('Support ticket submitted successfully');
      if (options?.onSuccessCallback) {
        options.onSuccessCallback();
      }
    },
    onError: (error: Error) => {
      toast.error(`Failed to submit support ticket: ${error.message}`);
      if (options?.onErrorCallback) {
        options.onErrorCallback(error);
      }
    },
  });
}
