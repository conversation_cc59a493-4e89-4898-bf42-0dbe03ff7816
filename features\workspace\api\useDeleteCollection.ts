import { useMutation, useQueryClient, QueryKey } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import axiosInstanceUi from '@/lib/axiosInstance.ui';
import { API_CONFIG } from '@/config/api';
import { toast } from 'sonner';

// Function to delete a collection by its ID
const deleteCollectionById = async (collectionId: string): Promise<void> => {
  await axiosInstanceUi.delete(`${API_CONFIG.workspace.collection}/${collectionId}`);
};

// Define the query key for the collection list
const collectionListQueryKey: QueryKey = [API_CONFIG.workspace.collection];

export function useDeleteCollection({
  onSuccessCallback,
}: {
  onSuccessCallback?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  const router = useRouter();

  return useMutation<void, Error, string>({
    mutationFn: (collectionId) => deleteCollectionById(collectionId),
    onSuccess: (_, collectionId) => {
      console.log(`Collection with ID ${collectionId} deleted successfully.`);
      toast.success('Collection deleted successfully!');

      // Invalidate the collection list query to refresh the UI
      queryClient.invalidateQueries({ queryKey: collectionListQueryKey });

      // Remove the specific collection query data from the cache
      queryClient.removeQueries({
        queryKey: [API_CONFIG.workspace.collection, collectionId],
      });

      // Call the success callback if provided
      onSuccessCallback?.();
    },
    onError: (error) => {
      console.error('Error deleting collection:', error);
      toast.error(`Failed to delete collection: ${error.message}`);
    },
  });
}
