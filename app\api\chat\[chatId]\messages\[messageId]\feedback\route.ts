import { NextRequest, NextResponse } from 'next/server';
import axiosInstanceApi from '@/lib/axiosInstance.api';

export async function POST(
  request: NextRequest,
  props: { params: Promise<{ chatId: string; messageId: string }> }
) {
  const authorization = request.headers.get('Authorization');
  const { chatId, messageId } = await props.params;

  // Construct the target path dynamically using the chatId and messageId
  const targetPath = `/chat/${chatId}/messages/${messageId}/feedback`;

  // Check if required IDs are present
  if (!chatId || !messageId) {
    return NextResponse.json(
      { message: 'Chat ID and Message ID are required' },
      { status: 400 }
    );
  }

  try {
    // Extract the feedback data from the request body
    const feedbackData = await request.json();

    console.log(
      `[API Route Proxy] Forwarding POST ${targetPath} with Auth: ${!!authorization}`
    );

    // Forward the POST request to the backend API
    const apiResponse = await axiosInstanceApi.post(targetPath, feedbackData, {
      headers: {
        'Content-Type': 'application/json',
        ...(authorization && { Authorization: authorization }),
      },
    });

    // Return the response from the backend service
    return NextResponse.json(apiResponse.data, { status: apiResponse.status });
  } catch (error: any) {
    if (error.response?.status === 404) {
      return NextResponse.json(
        { message: error.message },
        { status: error.response?.status }
      );
    }

    console.error(
      `[API Route Proxy Error] POST ${targetPath}:`,
      error.response?.data || error.message
    );

    // Handle JSON parsing errors specifically
    if (error instanceof SyntaxError) {
      return NextResponse.json(
        { message: 'Invalid JSON payload' },
        { status: 400 }
      );
    }

    const status = error.response?.status || 500;
    const message = error.response?.data?.message || 'Proxy error';
    return NextResponse.json({ message }, { status });
  }
}
