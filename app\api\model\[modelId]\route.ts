import { NextRequest, NextResponse } from 'next/server';
import axiosInstanceApi from '@/lib/axiosInstance.api';

export async function GET(
  request: NextRequest,
  props: { params: Promise<{ modelId: string }> }
) {
  try {
    const authorization = request.headers.get('Authorization');
    const { modelId } = await props.params;
    const apiResponse = await axiosInstanceApi.get(`/model/${modelId}`, {
      headers: {
        ...(authorization && { Authorization: authorization }),
      },
    });

    return NextResponse.json(apiResponse.data, { status: apiResponse.status });
  } catch (error: any) {
    console.error(error);
    const status = error.response?.status || 500;
    const message = error.response?.data?.message || 'Internal Server Error';
    return NextResponse.json({ message }, { status });
  }
}

export async function PATCH(
  request: NextRequest,
  props: { params: Promise<{ modelId: string }> }
) {
  try {
    const authorization = request.headers.get('Authorization');
    const { modelId } = await props.params;
    const body = await request.json();
    const apiResponse = await axiosInstanceApi.patch(
      `/model/${modelId}`,
      body,
      {
        headers: {
          ...(authorization && { Authorization: authorization }),
        },
      }
    );

    return NextResponse.json(apiResponse.data, { status: apiResponse.status });
  } catch (error: any) {
    console.error(error);
    const status = error.response?.status || 500;
    const message = error.response?.data?.message || 'Internal Server Error';
    return NextResponse.json({ message }, { status });
  }
}

export async function DELETE(
  request: NextRequest,
  props: { params: Promise<{ modelId: string }> }
) {
  try {
    const authorization = request.headers.get('Authorization');
    const { modelId } = await props.params;

    if (!modelId) {
      return NextResponse.json(
        { message: 'Model ID is required for deletion' },
        { status: 400 }
      );
    }

    const apiResponse = await axiosInstanceApi.delete(`/model/${modelId}`, {
      headers: {
        ...(authorization && { Authorization: authorization }),
      },
    });

    return NextResponse.json(apiResponse.data, { status: apiResponse.status });
  } catch (error: any) {
    console.error('Error deleting model:', error);
    const status = error.response?.status || 500;
    const message = error.response?.data?.message || 'Error deleting model';
    return NextResponse.json({ message }, { status });
  }
}
