import Cookies from 'js-cookie';

export const WS_SERVICE_URL =
  process.env.NEXT_PUBLIC_WS_SERVICE_URL || 'ws://localhost:80';

/**
 * Creates a WebSocket URL with the base WS_SERVICE_URL and the given path and parameters
 * @param path The path to append to the base URL
 * @param params Optional query parameters
 * @returns The complete WebSocket URL
 */
export const getWebSocketUrl = (
  path: string,
  params?: Record<string, string>
): string => {
  const token = Cookies.get('token') || '';
  const baseQueryString = `token=${token}`;
  const queryString = params
    ? Object.entries(params)
        .map(
          ([key, value]) =>
            `${encodeURIComponent(key)}=${encodeURIComponent(value)}`
        )
        .join('&')
    : '';

  return `${WS_SERVICE_URL}${path}?${[baseQueryString, queryString].join('&')}`;
};
