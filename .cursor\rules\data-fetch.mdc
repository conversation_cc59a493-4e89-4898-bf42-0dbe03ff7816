---
description: 
globs: 
alwaysApply: true
---
Rule Name: Data Fetching & Loading States (Client-Side)
Description: 
1.  **Library:** Use React Query (`@tanstack/react-query`) for client-side data fetching, caching, and state management related to server data.
2.  **Encapsulation:** Abstract data fetching logic into custom hooks (e.g., `useFetchChats`, `useFetchModels`) placed in the `hooks/` directory. These hooks should contain the `useQuery` call and the associated asynchronous fetch function.
3.  **Fetch Functions:** The actual data fetching functions called by `useQuery` should utilize the client-side Axios instance (`@/lib/axiosInstance.ui.ts`) to ensure automatic auth token attachment.
4.  **State Handling:** Components consuming these custom hooks **must** handle the returned states:
    *   `isPending`: Show a loading indicator (e.g., `<LoadingSpinner />`, skeleton UI) while data is being fetched.
    *   `error`: Display an appropriate error message or UI state if fetching fails.
    *   `data`: Render the UI using the fetched data once available and there's no error.
5.  **No Early Returns:** **Crucially**, do not use early `return` statements based on `isPending` or `error` *before* all top-level hooks in the component have been called. Handle these states within the main JSX return structure to comply with the Rules of Hooks.
6.  **Query Keys:** Use descriptive and unique array-based `queryKey`s within the hooks (e.g., `['chats']`, `['models', modelId]`) for proper caching and invalidation.