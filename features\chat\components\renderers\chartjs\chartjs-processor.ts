import {
  ContentProcessor,
  ContentProcessorContext,
  ProcessedContent,
} from '../../content-processors/types';
import { processChartJsCodeBlock } from '.';

export class ChartJsProcessor implements ContentProcessor {
  /**
   * Chart.js Initialization
   * Chart.js doesn’t require global initialization like Mermaid.
   * This method exists for interface compatibility only.
   */
  initialize(ctx: ContentProcessorContext): void {
    // Chart.js uses instance-based setup, no global or theme-based configuration needed.
  }

  canProcess(ctx: ContentProcessorContext): boolean {
    // Check if the content contains Chart.js configuration patterns
    return (
      !!ctx.code &&
      (ctx.code.includes('new Chart(') ||
        (ctx.lang === 'js' &&
          ctx.code.includes('type:') &&
          (ctx.code.includes('data:') || ctx.code.includes('options:'))))
    );
  }

  process(ctx: ContentProcessorContext): ProcessedContent {
    const chartHtml = processChartJsCodeBlock(ctx.code, ctx.lang);

    if (chartHtml) {
      return {
        html: chartHtml,
        skipNextProcessors: true,
      };
    }

    return {
      html: '',
      skipNextProcessors: false,
    };
  }
}
