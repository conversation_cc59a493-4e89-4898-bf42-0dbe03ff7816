import { renderNivoCharts } from './nivo-renderer';
import { NivoRenderOptions } from './types';

export function setupNivoContentProcessor() {
  const styleId = 'nivo-chart-hide-style';
  let existingStyle = document.getElementById(styleId);

  if (!existingStyle) {
    const style = document.createElement('style');
    style.id = styleId;
    style.textContent = `
      nivochart, nivoChart, NIVOCHART {
        display: none !important;
        visibility: hidden !important;
      }
      .nivo-chart-container {
        display: block !important;
        visibility: visible !important;
      }
    `;
    document.head.appendChild(style);
  }
}

export function processNivoCharts(
  options: NivoRenderOptions
): (() => void) | undefined {
  const { container, theme, isStreaming, messageId } = options;

  if (!container) return;

  try {
    return renderNivoCharts({
      container,
      theme,
      isStreaming,
      messageId,
    });
  } catch (err) {
    console.error('Error rendering Nivo charts:', err);
  }
}
