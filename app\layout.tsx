import type { Metadata, Viewport } from 'next';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from 'next/font/google';
import Script from 'next/script';
import ClientProviders from '@/providers/ClientProviders';
import { ThemeProvider } from '@/components/theme-provider';
import { Toaster } from '@/components/ui/sonner';

import '../css/globals.css';
import SidebarContextProvider from '@/contexts/SidebarContext';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
};

export const metadata: Metadata = {
  title: 'Chuck',
  description: 'Built with love by Mawer',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      {/* Body can remain in the Server Component layout */}
      <body
        className={`${geistSans.variable} ${geistMono.variable} flex min-h-screen antialiased`}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          enableSystem
          disableTransitionOnChange
        >
          <SidebarContextProvider>
            <Toaster />
            <ClientProviders>{children}</ClientProviders>
          </SidebarContextProvider>
        </ThemeProvider>
        <Script
          src={
            process.env.NEXT_PUBLIC_RYBBIT_SCRIPT_URL ||
            'https://rybbit-dashboard.engine.mawer.com/api/script.js'
          }
          data-site-id={process.env.NEXT_PUBLIC_RYBBIT_SITE_ID || '3'}
          strategy="afterInteractive"
          defer
        />
      </body>
    </html>
  );
}
