'use client';

import React from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
// No chart container needed for pie chart
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer } from 'recharts';
import { Skeleton } from '@/components/ui/skeleton';
import { IDashboardData } from '@/types/dashboard';
import { Badge } from '@/components/ui/badge';
import { ThumbsDown, ThumbsUp } from 'lucide-react';

interface FeedbackChartProps {
  data: IDashboardData;
  isLoading?: boolean;
}

export default function FeedbackChart({
  data,
  isLoading = false,
}: FeedbackChartProps) {
  const chartData = [
    { name: 'Positive', value: data.totalPos, color: 'var(--chart-3)' },
    { name: 'Negative', value: data.totalNeg, color: 'var(--chart-4)' },
  ];

  // Get top reasons by combining positive and negative reasons
  const allReasons = {
    ...data.totalRes.positive,
    ...data.totalRes.negative,
  };

  const sortedReasons = Object.entries(allReasons)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 5);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Feedback</CardTitle>
        <CardDescription>User feedback distribution</CardDescription>
      </CardHeader>
      <CardContent className="flex flex-col gap-4">
        {isLoading ? (
          <div className="flex h-[200px] w-full items-center justify-center">
            <Skeleton className="h-[200px] w-[200px] rounded-full" />
          </div>
        ) : (
          <div className="flex flex-col items-center">
            <div className="h-[300px] w-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={chartData}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={80}
                    paddingAngle={10}
                    dataKey="value"
                    label={({ name, percent }) =>
                      `${(percent * 100).toFixed(0)}%`
                    }
                    labelLine={false}
                  >
                    {chartData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                </PieChart>
              </ResponsiveContainer>
            </div>

            <div className="mt-4 flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Badge
                  variant="outline"
                  className="bg-[var(--chart-3)] text-white"
                >
                  <ThumbsUp className="h-3 w-3" />
                  {data.totalPos}
                </Badge>
              </div>
              <div className="flex items-center gap-2">
                <Badge
                  variant="outline"
                  className="bg-[var(--chart-4)] text-white"
                >
                  <ThumbsDown className="h-3 w-3" />
                  {data.totalNeg}
                </Badge>
              </div>
            </div>

            {sortedReasons.length > 0 && (
              <div className="mt-6 w-full">
                <h4 className="mb-2 text-sm font-medium">Top Reasons</h4>
                <div className="grid gap-2">
                  {sortedReasons.map(([reason, count]) => (
                    <div
                      key={reason}
                      className="bg-muted flex items-center justify-between rounded-md p-2 text-xs"
                    >
                      <span className="capitalize">
                        {reason.replace(/_/g, ' ')}
                      </span>
                      <span className="font-medium">{count}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
