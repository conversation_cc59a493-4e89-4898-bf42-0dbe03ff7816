import { useQuery } from '@tanstack/react-query';
import axiosInstanceUi from '@/lib/axiosInstance.ui';
import { IModelVersionsResponse } from '@/types/model';

// Function to fetch model versions by model ID
const fetchModelVersions = async (
  modelId: string,
  limit: number = 10
): Promise<IModelVersionsResponse> => {
  const response = await axiosInstanceUi.get<IModelVersionsResponse>(
    `/model/${modelId}/versions?limit=${limit}&page=1`
  );

  if (!response.data) {
    throw new Error('Model versions not found or empty response');
  }

  return response.data;
};

// Hook to fetch model versions
export function useFetchModelVersions(modelId: string | null, limit: number = 10) {
  return useQuery<IModelVersionsResponse, Error>({
    queryKey: ['model-versions', modelId, limit],
    queryFn: () => fetchModelVersions(modelId!, limit),
    enabled: !!modelId, // Only run query if modelId is provided
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}
