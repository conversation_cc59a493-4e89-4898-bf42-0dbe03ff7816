import {
  useMutation,
  useQueryClient,
  QueryKey, // Import QueryKey type
} from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import axiosInstanceUi from '@/lib/axiosInstance.ui';
import { API_CONFIG } from '@/config/api';

// Define the structure returned by the POST request (likely the new chat object)
interface Chat {
  id: string;
  name: string;
}

// Define the payload type for the mutation function
interface CreateChatPayload {
  message: string;
}

// Function to create a new chat via POST request
export const createNewChat = async (
  chatId: string,
  payload: CreateChatPayload
): Promise<Chat> => {
  // Use the client-side instance that includes the auth token
  // Assuming the backend expects { message: string } to create the chat and first message
  const response = await axiosInstanceUi.post<Chat>(
    `${API_CONFIG.chat.chat}/${chatId}`, // Endpoint for creating a new chat
    payload
  );
  if (!response.data) {
    throw new Error('Failed to create chat or empty response received.');
  }
  return response.data; // Return the newly created chat object
};

// Define the query key for the chat list (consistent with useFetchChats)

export function useCreateChat() {
  const queryClient = useQueryClient();
  const router = useRouter();
  return useMutation<Chat, Error, CreateChatPayload & { chatId: string }>({
    mutationFn: ({ chatId, ...payload }) => createNewChat(chatId, payload),
    onSuccess: (newChat) => {
      console.log('New chat created successfully:', newChat);
      const chatListQueryKey: QueryKey = [API_CONFIG.chat.chat];

      // 1. Invalidate the chat list query to refresh the sidebar
      queryClient.invalidateQueries({ queryKey: chatListQueryKey });

      // 2. Optionally, pre-populate the cache for the new chat query
      //    This can make the navigation feel faster if the create endpoint
      //    returns the full chat object including the first message.
      // queryClient.setQueryData([API_CONFIG.chat.chat, newChat.id], newChat);

      // 3. Redirect the user to the newly created chat page
      //   router.push(`/c/${newChat.id}`);
    },
    onError: (error) => {
      // TODO: Add user-facing error handling (e.g., toast notification)
      console.error('Error creating new chat:', error);
    },
    // Optional: You might want to implement onMutate for optimistic updates
    // if the backend doesn't immediately return the AI's response.
  });
}
