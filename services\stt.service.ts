import * as SpeechSDK from 'microsoft-cognitiveservices-speech-sdk';

export class STTService {
  private onRecognizing: (
    sender: Speech<PERSON>K.Recognizer,
    event: SpeechSDK.SpeechRecognitionEventArgs
  ) => void;
  private onRecognized: (
    sender: Speech<PERSON><PERSON>.Recognizer,
    event: SpeechSDK.SpeechRecognitionEventArgs
  ) => void;
  private recognizer: SpeechSDK.SpeechRecognizer;

  constructor(
    token: string,
    region: string,
    onRecognizing: (
      sender: SpeechSDK.Recognizer,
      event: SpeechSDK.SpeechRecognitionEventArgs
    ) => void,
    onRecognized: (
      sender: SpeechSDK.Recognizer,
      event: SpeechSDK.SpeechRecognitionEventArgs
    ) => void
  ) {
    this.onRecognizing = onRecognizing;
    this.onRecognized = onRecognized;

    const speechConfig = SpeechSDK.SpeechConfig.fromAuthorizationToken(
      token,
      region
    );
    speechConfig.setProfanity(SpeechSDK.ProfanityOption.Raw);
    speechConfig.speechRecognitionLanguage = 'en-US';
    const audioConfig = SpeechSDK.AudioConfig.fromDefaultMicrophoneInput();

    this.recognizer = new SpeechSDK.SpeechRecognizer(speechConfig, audioConfig);
    this.recognizer.recognizing = this.onRecognizing;
    this.recognizer.recognized = this.onRecognized;

    this.recognizer.canceled = (s, e) => {
      console.error(e);
    };
  }

  public start() {
    this.recognizer?.startContinuousRecognitionAsync(
      () => {
        console.log('Successfully started continuous recognition');
      },
      (err) => {
        console.error(err);
      }
    );
  }

  public stop() {
    this.recognizer?.stopContinuousRecognitionAsync();
  }

  public dispose() {
    if (this.recognizer) {
      this.recognizer.stopContinuousRecognitionAsync(() => {
        this.recognizer.close();
      });
    }
  }
}
