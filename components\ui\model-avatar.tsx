'use client';

import React from 'react';
import Image from 'next/image';
import { cn } from '@/lib/utils';

interface ModelAvatarProps {
  image?: string | null;
  name: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

/**
 * ModelAvatar component displays either an image or a square with initials
 * @param image - Optional image URL (base64 string)
 * @param name - Model name (used for alt text and to generate initials)
 * @param size - Size variant: 'sm' (24px), 'md' (32px), or 'lg' (40px)
 * @param className - Additional CSS classes
 */
export function ModelAvatar({
  image,
  name,
  size = 'md',
  className,
}: ModelAvatarProps) {
  // Get initials from the model name (max 3 characters)
  const getInitials = (name: string) => {
    // Filter out non-alphanumeric characters and split by spaces
    const filteredName = name.replace(/[^a-zA-Z0-9\s]/g, '');

    // Split by spaces and get first letter of each word
    const initials = filteredName
      .split(' ')
      .filter((word) => word.length > 0) // Skip empty words
      .map((word) => {
        // Get the first letter that is a valid English letter
        for (let i = 0; i < word.length; i++) {
          const char = word.charAt(i);
          if (/[a-zA-Z]/.test(char)) {
            return char;
          }
        }
        return '';
      })
      .join('')
      .toUpperCase();

    // If no valid initials were found, use 'M' for Model
    if (!initials) {
      return 'M';
    }

    // Limit to 3 characters
    return initials.substring(0, 2);
  };

  // Size mappings
  const sizeClasses = {
    sm: 'h-6 w-6 text-xs',
    md: 'h-8 w-8 text-sm',
    lg: 'h-10 w-10 text-base',
  };

  return (
    <>
      {image ? (
        <div
          className={cn(
            'relative overflow-hidden rounded-md',
            sizeClasses[size],
            className
          )}
        >
          <Image src={image} alt={name} fill className="object-cover" />
        </div>
      ) : (
        <div
          className={cn(
            'bg-primary/10 text-primary flex items-center justify-center rounded-md font-medium',
            sizeClasses[size],
            className
          )}
        >
          {getInitials(name)}
        </div>
      )}
    </>
  );
}
