# Testing with Vitest

This project uses <PERSON><PERSON><PERSON> for unit testing. Vitest is a Vite-based test runner that provides a fast and modern testing experience.

## Running Tests

To run the tests, you can use the following commands:

```bash
# Run all tests once (exits after completion)
pnpm test

# Run tests in watch mode (tests will re-run when files change)
pnpm test:watch

# Run tests with coverage report
pnpm test:coverage

# Run tests with JUnit reporter for CI/CD pipelines
pnpm test:ci

# Run tests with UI for better development experience
pnpm test:ui
```

## CI/CD Integration

For CI/CD pipelines, use the `test` or `test:ci` command, which will run the tests once and exit with the appropriate status code. The `test:ci` command generates a JUnit XML report that can be consumed by most CI/CD systems.

### Azure DevOps Pipeline

Example Azure Pipeline YAML configuration:

```yaml
steps:
  - task: NodeTool@0
    inputs:
      versionSpec: '20.x'
    displayName: 'Install Node.js'

  - script: |
      npm install -g pnpm
      pnpm install
    displayName: 'Install dependencies'

  - script: |
      pnpm test:ci
    displayName: 'Run tests'

  - task: PublishTestResults@2
    inputs:
      testResultsFormat: 'JUnit'
      testResultsFiles: '**/test-results.xml'
      mergeTestResults: true
      testRunTitle: 'Vitest Tests'
    condition: succeededOrFailed()
    displayName: 'Publish test results'
```

### GitHub Actions

A GitHub Actions workflow file is included in `.github/workflows/test.yml`. This workflow will run tests on push to main and on pull requests.

```yaml
name: Run Tests

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Setup pnpm
        uses: pnpm/action-setup@v3
        with:
          version: 10
          run_install: false

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - name: Setup pnpm cache
        uses: actions/cache@v4
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install dependencies
        run: pnpm install

      - name: Run tests
        run: pnpm test:ci
```

## Test Structure

Tests are organized alongside the code they test:

- For hooks: `hooks/__tests__/[hook-name].test.tsx`
- For components: `components/__tests__/[component-name].test.tsx` or within feature directories
- For utilities: `lib/__tests__/[utility-name].test.ts`

## Writing Tests

### Testing Hooks

For testing hooks, we use `@testing-library/react-hooks`. Here's an example:

```tsx
import { renderHook } from '@testing-library/react';
import { useMyHook } from '../useMyHook';

describe('useMyHook', () => {
  it('should return the expected value', () => {
    const { result } = renderHook(() => useMyHook());
    expect(result.current).toBe(expectedValue);
  });
});
```

### Testing Components

For testing components, we use `@testing-library/react`. Here's an example:

```tsx
import { render, screen } from '@testing-library/react';
import { MyComponent } from '../MyComponent';

describe('MyComponent', () => {
  it('should render correctly', () => {
    render(<MyComponent />);
    expect(screen.getByText('Expected Text')).toBeInTheDocument();
  });
});
```

### Mocking Dependencies

For mocking dependencies, we use Vitest's mocking capabilities:

```tsx
import { vi } from 'vitest';

// Mock a module
vi.mock('@/path/to/module', () => ({
  useModule: vi.fn().mockReturnValue({ data: 'mocked data' }),
}));
```

## Configuration

The Vitest configuration is in `vitest.config.ts`. It includes:

- JSDOM for DOM testing
- Path aliases that match the project's configuration
- React plugin for JSX support

## Best Practices

1. Write tests for critical business logic
2. Keep tests focused and small
3. Use descriptive test names
4. Mock external dependencies
5. Test both success and failure cases
6. Aim for high coverage of critical paths
