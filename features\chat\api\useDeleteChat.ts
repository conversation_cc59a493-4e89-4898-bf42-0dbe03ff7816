import { useMutation, useQueryClient, QueryKey } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import axiosInstanceUi from '@/lib/axiosInstance.ui';
import { API_CONFIG } from '@/config/api';
import { toast } from 'sonner'; // Import toast for notifications

// Function to delete a chat by its ID
// DELETE requests often don't return a body on success (e.g., 204 No Content)
// We expect Axios to resolve successfully or throw an error on failure.
const deleteChatById = async (chatId: string): Promise<void> => {
  // Use the client-side instance that includes the auth token
  await axiosInstanceUi.delete(
    `${API_CONFIG.chat.chat}/${chatId}` // DELETE endpoint for the specific chat
  );
  // No return needed if successful (or handle specific status codes if backend sends them)
};

// Define the input type for the mutation (just the chat ID)
interface DeleteChatInput {
  chatId: string;
}

export function useDeleteChat() {
  const queryClient = useQueryClient();
  const router = useRouter();

  return useMutation<void, Error, DeleteChatInput>({
    mutationFn: ({ chatId }) => deleteChatById(chatId),
    onSuccess: (data, variables) => {
      // data is void in this case
      const deletedChatId = variables.chatId;
      console.log(`Chat with ID ${deletedChatId} deleted successfully.`);
      toast.success('Chat deleted successfully!');

      const chatListQueryKey: QueryKey = [API_CONFIG.chat.chat];
      const specificChatQueryKey: QueryKey = [
        API_CONFIG.chat.chat,
        deletedChatId,
      ];

      // 3. Redirect the user, e.g., to the base chat page or dashboard
      router.push('/c'); // Adjust the target route as needed

      // 2. Remove the specific chat query data from the cache immediately
      //    This prevents navigating back to a non-existent chat state.
      queryClient.removeQueries({ queryKey: specificChatQueryKey });

      // 1. Invalidate the chat list query to refresh the sidebar
      queryClient.invalidateQueries({ queryKey: chatListQueryKey });
    },
    onError: (error) => {
      // Error handling is likely covered by the global QueryClient onError,
      // but you can add specific handling here if needed.
      console.error('Error deleting chat:', error);
      toast.error('Failed to delete chat.'); // Add specific error toast
    },
  });
}
