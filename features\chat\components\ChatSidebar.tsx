import * as React from 'react';
import { usePathname } from 'next/navigation';
import ChatSidebarLink from './ChatSidebarLink';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Pencil,
  Ellipsis,
  Trash2, // Delete icon
  ChevronDown,
  ChevronRight,
  Search,
  X,
  Check,
  Pin,
} from 'lucide-react';
import { UI_CONFIG } from '@/config/ui';
import { useFetchChats } from '@/features/chat/api/useFetchChats';
import { Skeleton } from '@/components/ui/skeleton';
import { Input } from '@/components/ui/input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { useChuckContext } from '@/contexts/ChuckContext';
import { useDeleteChat } from '@/features/chat/api/useDeleteChat';
import { useUpdateChatById } from '@/features/chat/api/useUpdateChatById';
import { useToggleChatPin } from '@/features/chat/api/useToggleChatPin';
import { useSidebar } from '@/contexts/SidebarContext';
import { IChat, IChatGroup } from '@/types/chat';

// Define active classes for chat history links
const chatLinkActiveClasses = 'bg-accent text-accent-foreground';

export default function ChatSidebar() {
  const { setStore } = useChuckContext();
  const { store: sidebarStore } = useSidebar();
  const { data: chats, isPending, error } = useFetchChats();
  const pathname = usePathname();
  const [chatToDelete, setChatToDelete] = React.useState<string | null>(null);
  const [searchQuery, setSearchQuery] = React.useState<string>('');
  const [filteredChats, setFilteredChats] = React.useState<IChatGroup[] | null>(
    null
  );
  const [isSearchExpanded, setIsSearchExpanded] =
    React.useState<boolean>(false);
  const [chatToRename, setChatToRename] = React.useState<string | null>(null);
  const [newChatName, setNewChatName] = React.useState<string>('');

  const deleteChatMutation = useDeleteChat();
  const updateChatMutation = useUpdateChatById();
  const toggleChatPinMutation = useToggleChatPin();

  const currentChatId = pathname.startsWith('/c/')
    ? pathname.split('/')[2]
    : null;

  // Filter chats based on search query
  React.useEffect(() => {
    if (!chats || !searchQuery.trim() || !isSearchExpanded) {
      setFilteredChats(null);
      return;
    }

    const filtered = chats
      .map((group) => {
        // Filter chats in each group that match the search query
        const filteredData = group.data.filter((chat) =>
          chat.name.toLowerCase().includes(searchQuery.toLowerCase())
        );

        // Return a new group with the filtered data
        return {
          ...group,
          data: filteredData,
        };
      })
      .filter((group) => group.data.length > 0); // Remove empty groups

    setFilteredChats(filtered);
  }, [chats, searchQuery, isSearchExpanded]);

  // Function to clear search
  const handleClearSearch = () => {
    setSearchQuery('');
    setIsSearchExpanded(false);
  };

  // Function to start renaming a chat
  const handleStartRename = (chatId: string, currentName: string) => {
    setChatToRename(chatId);
    setNewChatName(currentName);
  };

  // Function to cancel renaming
  const handleCancelRename = () => {
    setChatToRename(null);
    setNewChatName('');
  };

  // Function to save the new chat name
  const handleSaveRename = (chatId: string) => {
    if (newChatName.trim() && chatId) {
      updateChatMutation.mutate({
        chatId,
        name: newChatName.trim(),
      });
      setChatToRename(null);
      setNewChatName('');
    }
  };

  // Function to handle key press in rename input
  const handleRenameKeyDown = (e: React.KeyboardEvent, chatId: string) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSaveRename(chatId);
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancelRename();
    }
  };

  // Function to toggle pin status
  const handleTogglePin = (chatId: string, currentPinned: boolean) => {
    toggleChatPinMutation.mutate({
      chatId,
      pinned: !currentPinned,
    });
  };

  // // Placeholder functions for popover actions
  // const handleShare = (chatId: string, e: React.MouseEvent) => {
  //   e.stopPropagation(); // Prevent link navigation
  //   console.log('Share chat:', chatId);
  //   // TODO: Implement Share logic
  // };
  // const handleRename = (chatId: string, e: React.MouseEvent) => {
  //   e.stopPropagation(); // Prevent link navigation
  //   console.log('Rename chat:', chatId);
  //   // TODO: Implement Rename logic
  // };
  // const handleArchive = (chatId: string, e: React.MouseEvent) => {
  //   e.stopPropagation(); // Prevent link navigation
  //   console.log('Archive chat:', chatId);
  //   // TODO: Implement Archive logic
  // };

  // --- Delete Confirmation Logic ---
  // Function called by AlertDialogAction
  const confirmDelete = () => {
    if (chatToDelete && !deleteChatMutation.isPending) {
      deleteChatMutation.mutate({ chatId: chatToDelete });

      setChatToDelete(null);
    }
  };

  // Function called by AlertDialogCancel or when dialog is closed
  const cancelDelete = () => {
    setChatToDelete(null);
    console.log('Cancelled delete');
  };

  // Helper function to find a chat by ID across all categories
  const findChatById = (chatId: string | null) => {
    if (!chatId || !chats) return null;

    // Search in each category
    for (const group of chats) {
      const found = group.data.find((chat) => chat.id === chatId);
      if (found) return found;
    }
    return null;
  };

  // Check if there are any chats in any category
  const hasChats = chats && chats.length > 0;

  // Determine which chats to display - filtered or original
  const displayChats = filteredChats || chats;

  // State to track which categories are expanded
  const [expandedCategories, setExpandedCategories] = React.useState<
    Record<string, boolean>
  >({
    Pinned: true,
    Today: true,
    Yesterday: true,
    'Previous 7 Days': true,
    'Previous 30 Days': false,
    'Further History': false,
  });

  // Toggle category expansion
  const toggleCategory = (category: string) => {
    setExpandedCategories((prev) => ({
      ...prev,
      [category]: !prev[category],
    }));
  };

  // Render a single chat item
  const renderChatItem = (chat: IChat) => (
    <div
      key={chat.id}
      className={cn(
        'group relative flex h-7 w-full items-center justify-between rounded-md text-sm font-normal',
        'focus-visible:ring-ring focus-visible:ring-1 focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50', // Base styles for focus/disabled
        currentChatId === chat.id
          ? chatLinkActiveClasses
          : 'hover:bg-accent hover:text-accent-foreground' // Hover only if not active
      )}
    >
      {chatToRename === chat.id ? (
        // Rename input field
        <div className="flex w-full items-center px-1">
          <Input
            value={newChatName}
            onChange={(e) => setNewChatName(e.target.value)}
            onKeyDown={(e) => handleRenameKeyDown(e, chat.id)}
            className="h-6 text-sm"
            autoFocus
            onClick={(e) => e.stopPropagation()}
          />
          <div className="flex">
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={() => handleSaveRename(chat.id)}
            >
              <Check className="h-3 w-3" />
              <span className="sr-only">Save</span>
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6"
              onClick={handleCancelRename}
            >
              <X className="h-3 w-3" />
              <span className="sr-only">Cancel</span>
            </Button>
          </div>
        </div>
      ) : (
        // Normal display
        <>
          <ChatSidebarLink
            href={`/c/${chat.id}`}
            title={chat.name}
            className="flex-1 cursor-pointer truncate px-2" // Link takes up most space
            onClick={(e) => {
              e.stopPropagation();
              setStore((prev) => ({
                ...prev,
                newChatMessage: null,
              }));
            }}
          >
            {chat.name}
          </ChatSidebarLink>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                size="icon"
                className="invisible absolute top-1/2 right-1 h-6 w-6 flex-shrink-0 -translate-y-1/2 rounded-md p-0 group-hover:visible hover:cursor-pointer data-[state=open]:visible"
                onClick={(e) => e.stopPropagation()}
              >
                <Ellipsis className="h-4 w-4" />
                <span className="sr-only">Chat options</span>
              </Button>
            </PopoverTrigger>
            <PopoverContent
              className="w-40 p-1"
              onClick={(e) => e.stopPropagation()} // Prevent link nav if clicking inside content
              align="start"
            >
              {/* Pin/Unpin */}
              <Button
                variant="ghost"
                className="hover:bg-accent hover:text-accent-foreground flex h-8 w-full items-center justify-start gap-2 px-2 text-sm font-normal"
                onClick={(e) => {
                  e.stopPropagation();
                  handleTogglePin(chat.id, !!chat.pinned);
                }}
              >
                <Pin
                  className={cn('h-4 w-4', chat.pinned && 'fill-foreground')}
                />
                <span>{chat.pinned ? 'Unpin' : 'Pin'}</span>
              </Button>

              {/* Rename */}
              <Button
                variant="ghost"
                className="hover:bg-accent hover:text-accent-foreground flex h-8 w-full items-center justify-start gap-2 px-2 text-sm font-normal"
                onClick={(e) => {
                  e.stopPropagation();
                  handleStartRename(chat.id, chat.name);
                }}
              >
                <Pencil className="h-4 w-4" />
                <span>Rename</span>
              </Button>

              {/* Delete */}
              <AlertDialogTrigger asChild>
                <Button
                  variant="ghost"
                  className="flex h-8 w-full items-center justify-start gap-2 px-2 text-sm font-normal text-red-600 hover:bg-red-50 hover:text-red-600 dark:hover:bg-red-900/50"
                  onClick={(e) => {
                    e.stopPropagation();
                    setChatToDelete(chat.id);
                  }}
                >
                  <Trash2 className="h-4 w-4" />
                  <span>Delete</span>
                </Button>
              </AlertDialogTrigger>
            </PopoverContent>
          </Popover>
        </>
      )}
    </div>
  );

  return (
    // Wrap the component that contains triggers AND the content
    <AlertDialog onOpenChange={(open: boolean) => !open && cancelDelete()}>
      <div
        className={cn(
          'border-border bg-background flex h-full flex-shrink-0 flex-col border-r transition-all duration-300 ease-in-out',
          // Apply fixed width using CSS variable
          'w-[var(--chat-sidebar-width)]',
          sidebarStore.isMobile && 'fixed inset-y-0 left-[0px] z-20',
          // On mobile: hide by default, show when isOpen is true (as overlay)
          sidebarStore.isMobile && !sidebarStore.isOpen && '-translate-x-full',
          // On mobile: when visible, add shadow to indicate it's an overlay
          sidebarStore.isMobile &&
            sidebarStore.isOpen &&
            'left-[60px] shadow-xl'
        )}
        style={
          {
            '--chat-sidebar-width': `${UI_CONFIG.CHAT?.SIDEBAR_WIDTH}px`,
          } as React.CSSProperties
        }
      >
        <div className="flex flex-col overflow-hidden border-b p-2">
          <div className="flex items-center gap-2">
            <ChatSidebarLink
              href={'/c'}
              className={cn(
                'group flex h-8 w-full cursor-pointer items-center justify-between rounded-md px-2 text-sm font-normal',
                'hover:bg-accent hover:text-accent-foreground focus-visible:ring-ring focus-visible:ring-1 focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50',
                {
                  [chatLinkActiveClasses]: pathname === '/c',
                }
              )}
            >
              <span>New Chat</span>

              <div>
                <Pencil className="h-4 w-4" />
                <span className="sr-only">New Chat</span>
              </div>
            </ChatSidebarLink>
            <div>
              <Button
                variant={isSearchExpanded ? 'default' : 'outline'}
                size="icon"
                onClick={() => setIsSearchExpanded(!isSearchExpanded)}
              >
                <Search className="h-4 w-4" />
                <span className="sr-only">Search</span>
              </Button>
            </div>
          </div>
          <div
            className={cn(
              'relative transition-all duration-300 ease-in-out',
              isSearchExpanded ? 'mt-2 h-10 opacity-100' : 'mt-0 h-0 opacity-0'
            )}
          >
            <Input
              placeholder="Search chats..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="h-8 pr-8"
            />
            {searchQuery && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute top-0 right-0 h-8 w-8"
                onClick={handleClearSearch}
              >
                <X size={12} className="h-4 w-4" />
                <span className="sr-only">Clear search</span>
              </Button>
            )}
          </div>
        </div>

        <div className="flex-1 overflow-y-auto p-2">
          {isPending ? (
            <div className="flex h-full justify-center">
              <div className="space-y-4 py-4">
                {Array.from({ length: 10 }).map((_, index) => (
                  <Skeleton key={index} className="h-4 w-[230px]" />
                ))}
              </div>
            </div>
          ) : error ? (
            <div className="p-2 text-center text-sm text-red-500">
              Error loading chats: {error.message}
            </div>
          ) : hasChats ? (
            <div>
              {searchQuery && filteredChats?.length === 0 ? (
                <div className="text-muted-foreground p-2 text-center text-sm">
                  No chats found matching "{searchQuery}"
                </div>
              ) : (
                (displayChats || []).map((group) => (
                  <div key={group.name} className="mb-4">
                    <button
                      type="button"
                      onClick={() => toggleCategory(group.name)}
                      className="text-muted-foreground hover:text-foreground flex w-full cursor-pointer items-center px-[6px] py-1 text-xs"
                    >
                      {expandedCategories[group.name] ? (
                        <ChevronDown className="mr-1 h-3 w-3" />
                      ) : (
                        <ChevronRight className="mr-1 h-3 w-3" />
                      )}
                      {group.name} ({group.data.length})
                    </button>

                    {expandedCategories[group.name] && (
                      <div className="space-y-1/2">
                        {group.data.map((chat) => renderChatItem(chat))}
                      </div>
                    )}
                  </div>
                ))
              )}
            </div>
          ) : (
            <div className="text-muted-foreground p-2 text-center text-sm">
              No chats yet.
            </div>
          )}
        </div>
      </div>

      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
          <AlertDialogDescription>
            This action cannot be undone. This will permanently delete the chat
            "{findChatById(chatToDelete)?.name || 'this chat'}" and remove its
            data.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel
            onClick={cancelDelete}
            disabled={deleteChatMutation.isPending}
          >
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={confirmDelete}
            disabled={deleteChatMutation.isPending}
          >
            {deleteChatMutation.isPending ? 'Deleting...' : 'Confirm Delete'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
