import { useQuery } from '@tanstack/react-query';
import axiosInstanceUi from '@/lib/axiosInstance.ui';
import { API_CONFIG } from '@/config/api';
import Cookies from 'js-cookie';

export async function getSpeechToken() {
  const speechToken = Cookies.get('speech-token');

  if (!speechToken) {
    return await axiosInstanceUi
      .get(`${API_CONFIG.auth.speechToken}`)
      .then((response) => response.data)
      .then((data) => {
        Cookies.set('speech-token', `${data.region}:${data.token}`, {
          expires: new Date(new Date().getTime() + 9 * 60 * 1000),
        });
        return data;
      });
  }

  const idx = speechToken.indexOf(':');
  return {
    token: speechToken.slice(idx + 1),
    region: speechToken.slice(0, idx),
  };
}

export function useGetSpeechToken() {
  return useQuery<any, Error>({
    queryKey: [API_CONFIG.auth.speechToken],
    queryFn: getSpeechToken,
  });
}
