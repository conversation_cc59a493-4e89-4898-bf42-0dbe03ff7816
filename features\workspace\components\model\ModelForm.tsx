'use client';

import React, { useMemo, useState } from 'react';
import { useForm } from '@tanstack/react-form';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { SheetFooter } from '@/components/ui/sheet';
import { MultiSelect } from '@/components/ui/multi-select';
import { Trash2, Upload, X, Info } from 'lucide-react';
import { IModelCreatePayload, IModelData } from '@/types/model';
import { useCreateModel } from '@/features/workspace/api/useCreateModel';
import { getModelTemplate } from './helpers/getModelTemplate';
import { useUpdateModel } from '@/features/workspace/api/useUpdateModel';
import { usePermissions } from '@/hooks/usePermissions';
import { useDeleteModel } from '@/features/workspace/api/useDeleteModel';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { ModelAvatar } from '@/components/ui/model-avatar';
import { cn } from '@/lib/utils';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import PromptHelpPanel from './PromptHelpPanel';

interface ModelFormProps {
  onOpenChange: (open: boolean) => void;
  model?: IModelData | null;
  modelOptions: IModelData[];
  collectionOptions: { value: string; label: string }[];
  toolOptions: { value: string; label: string }[];
  groupOptions: { value: string; label: string }[];
}

const MAX_KB = 512;
const MAX_FILE_SIZE = MAX_KB * 1024;

// Zod schema for validation
const modelFormSchema = z.object({
  baseModel: z.string().min(1, 'Please select a base model'),
  name: z.string().min(3, 'Name must be at least 3 characters'),
  description: z.string().optional(),
  collectionIds: z.array(z.string()),
  toolIds: z.array(z.string()),
  groupIds: z.array(z.string()),
  prompt: z.string().optional(),
  prompt_suggestions: z.array(z.string()),
  image: z.string().optional(),
});

export default function ModelForm({
  onOpenChange,
  model,
  modelOptions,
  collectionOptions,
  toolOptions,
  groupOptions,
}: ModelFormProps) {
  const isEditMode = !!model?.id;
  const [newSuggestion, setNewSuggestion] = React.useState('');
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = React.useState(false);
  const [isHelpPanelOpen, setIsHelpPanelOpen] = React.useState(false);
  const [imagePreview, setImagePreview] = useState<string | null>(
    model?.image || null
  );
  const { user, isAdmin } = usePermissions();

  // Check if the current user is the owner or an admin
  const canEdit =
    !isEditMode ||
    isAdmin() ||
    (model?.owner_id && user?.id === model.owner_id);

  // Find the user's personal group ID
  const userPersonalGroupId = useMemo(() => {
    if (!user || !user.groups || user.groups.length === 0) return null;

    // Find the personal group for the current user
    const personalGroup = user.groups.find((group) => {
      // Check if the group has meta.personal = true
      if (typeof group.meta === 'string') {
        try {
          const metaObj = JSON.parse(group.meta);
          return metaObj.personal === true;
        } catch (e) {
          return false;
        }
      } else if (group.meta && typeof group.meta === 'object') {
        return group.meta.personal === true;
      }
      return false;
    });

    return personalGroup?.id || null;
  }, [user]);

  const handleSuccess = () => {
    onOpenChange(false);
  };

  const { mutate: createModelMutate, isPending: isCreatingModel } =
    useCreateModel({ onSuccessCallback: handleSuccess });
  const { mutate: updateModelMutate, isPending: isUpdatingModel } =
    useUpdateModel({ onSuccessCallback: handleSuccess });
  const { mutate: deleteModelMutate, isPending: isDeletingModel } =
    useDeleteModel({ onSuccessCallback: handleSuccess });

  const isLoadingMutation =
    isCreatingModel || isUpdatingModel || isDeletingModel;

  const handleDeleteModel = () => {
    if (model?.id) {
      deleteModelMutate(model.id);
    }
  };

  // Get default group IDs for new model
  const getDefaultGroupIds = () => {
    // If editing an existing model, use its groups
    if (model?.groups) {
      return model.groups.map((group) => group.id);
    }

    // For new model, include user's personal group if available
    if (!isEditMode && userPersonalGroupId) {
      return [userPersonalGroupId];
    }

    return [];
  };

  // --- Form Setup ---
  const form = useForm({
    defaultValues: {
      ...getModelTemplate(
        model,
        modelOptions.length > 0 ? modelOptions[0].base_model : undefined
      ),
      // Override groupIds with our custom logic
      groupIds: getDefaultGroupIds(),
    },
    onSubmit: async ({ value }) => {
      const payload: IModelCreatePayload = {
        baseModel: value.baseModel,
        name: value.name,
        description: value.description,
        collectionIds: value.collectionIds,
        toolIds: value.toolIds,
        groupIds: value.groupIds.length > 0 ? value.groupIds : [],
        image: value.image,
        config: {
          prompt: value.prompt,
          prompt_suggestions: value.prompt_suggestions,
        },
      };

      if (isEditMode && model?.id) {
        updateModelMutate({ modelId: model.id, payload });
      } else {
        createModelMutate(payload);
      }
    },
  });

  // --- Effects ---
  // Effect: Set default baseModel only in CREATE mode when options load

  // Handle image upload
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!canEdit) return;

    const file = e.target.files?.[0];
    if (!file) return;

    // Check if file is an image
    if (!file.type.startsWith('image/')) {
      alert('Please upload an image file');
      return;
    }

    // Check file size (limit to 1MB)
    if (file.size > MAX_FILE_SIZE) {
      alert(`Image size should be less than ${MAX_KB}KB`);
      return;
    }

    const reader = new FileReader();
    reader.onload = (event) => {
      const base64String = event.target?.result as string;
      setImagePreview(base64String);
      form.setFieldValue('image', base64String);
    };
    reader.readAsDataURL(file);
  };

  // Handle image removal
  const handleRemoveImage = () => {
    if (!canEdit) return;
    setImagePreview(null);
    form.setFieldValue('image', undefined);
  };

  const submitButtonText = isEditMode
    ? isLoadingMutation
      ? 'Updating...'
      : 'Update'
    : isLoadingMutation
      ? 'Saving...'
      : 'Submit';

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        e.stopPropagation();
        if (canEdit) {
          form.handleSubmit();
        }
      }}
    >
      <div className="grid gap-6">
        <div className="flex gap-6">
          {/* Image upload section - Left side */}
          <form.Field name="image">
            {(field) => (
              <div className="group relative">
                <Input
                  type="file"
                  id="image-upload"
                  accept="image/*"
                  className="hidden"
                  onChange={handleImageUpload}
                  disabled={isEditMode && !canEdit}
                />
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Label
                        htmlFor={canEdit ? 'image-upload' : undefined}
                        className={cn(
                          'block h-32 w-32 cursor-default',
                          canEdit && 'cursor-pointer hover:opacity-90'
                        )}
                      >
                        {imagePreview ? (
                          <div className="relative h-32 w-32">
                            <ModelAvatar
                              image={imagePreview}
                              name={model?.name || 'New Model'}
                              size="lg"
                              className="h-32 w-32 border"
                            />
                            {canEdit && (
                              <>
                                <div className="absolute inset-0 flex items-center justify-center bg-black/40 opacity-0 transition-opacity group-hover:opacity-100">
                                  <Upload className="h-6 w-6 text-white" />
                                </div>
                                <Button
                                  type="button"
                                  variant="destructive"
                                  size="icon"
                                  className="absolute top-1 right-1 h-6 w-6 rounded-full opacity-0 transition-opacity group-hover:opacity-100"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    e.stopPropagation();
                                    handleRemoveImage();
                                  }}
                                >
                                  <X className="h-4 w-4" />
                                  <span className="sr-only">Remove image</span>
                                </Button>
                              </>
                            )}
                          </div>
                        ) : (
                          <div className="relative h-32 w-32">
                            <ModelAvatar
                              name={model?.name || 'New Model'}
                              size="lg"
                              className="h-32 w-32 border border-dashed"
                            />
                            {canEdit && (
                              <div className="absolute inset-0 flex items-center justify-center bg-black/40 opacity-0 transition-opacity group-hover:opacity-100">
                                <Upload className="h-6 w-6 text-white" />
                              </div>
                            )}
                          </div>
                        )}
                      </Label>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Square image recommended (max {MAX_KB}KB)</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                {field.state.meta.errors &&
                  field.state.meta.errors.length > 0 && (
                    <p className="text-destructive text-xs">
                      {String(field.state.meta.errors[0])}
                    </p>
                  )}
              </div>
            )}
          </form.Field>

          {/* Name and Base Model - Right side */}
          <div className="flex-1 space-y-4">
            <form.Field
              name="name"
              validators={{ onChange: modelFormSchema.shape.name }}
            >
              {(field) => (
                <div className="grid gap-2">
                  <Label htmlFor={field.name}>
                    Name <span className="text-destructive">*</span>
                  </Label>
                  <Input
                    id={field.name}
                    name={field.name}
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="Model name"
                    disabled={isEditMode && !canEdit}
                  />
                  {field.state.meta.isTouched &&
                  field.state.meta.errors.length > 0 ? (
                    <p className="text-destructive text-xs">
                      {field.state.meta.errors[0]?.message}
                    </p>
                  ) : null}
                </div>
              )}
            </form.Field>

            <form.Field
              name="baseModel"
              validators={{ onChange: modelFormSchema.shape.baseModel }}
            >
              {(field) => (
                <div className="grid gap-2">
                  <Label htmlFor={field.name}>
                    Base Model <span className="text-destructive">*</span>
                  </Label>
                  <Select
                    name={field.name}
                    value={field.state.value}
                    onValueChange={field.handleChange}
                    disabled={isEditMode && !canEdit}
                  >
                    <SelectTrigger id={field.name} className="w-full">
                      <SelectValue placeholder={'Choose a model'} />
                    </SelectTrigger>
                    <SelectContent>
                      {modelOptions?.map((m) => (
                        <SelectItem key={m.id} value={m.base_model}>
                          {m.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {field.state.meta.errors.length > 0 ? (
                    <p className="text-destructive text-xs">
                      {field.state.meta.errors[0]?.message}
                    </p>
                  ) : null}
                </div>
              )}
            </form.Field>
          </div>
        </div>
        {/* Groups selector */}
        <form.Field name="collectionIds">
          {(field) => {
            const currentValue = field.state.value ?? [];
            return (
              <div className="grid gap-2">
                <Label>Collections</Label>
                <MultiSelect
                  options={collectionOptions}
                  onValueChange={field.handleChange}
                  defaultValue={currentValue}
                  placeholder={'Select collections'}
                  animation={0}
                  className="w-full"
                  onBlur={field.handleBlur}
                  disabled={isEditMode && !canEdit}
                />
              </div>
            );
          }}
        </form.Field>

        <form.Field name="groupIds">
          {(field) => {
            const currentValue = field.state.value ?? [];
            return (
              <div className="grid gap-2">
                <Label>Groups</Label>
                <MultiSelect
                  options={groupOptions}
                  onValueChange={field.handleChange}
                  defaultValue={currentValue}
                  placeholder={'Select groups'}
                  animation={0}
                  className="w-full"
                  onBlur={field.handleBlur}
                  disabled={isEditMode && !canEdit}
                />
              </div>
            );
          }}
        </form.Field>

        <form.Field name="toolIds">
          {(field) => {
            const currentValue = field.state.value ?? [];
            return (
              <div className="grid gap-2">
                <Label>Tools</Label>
                <MultiSelect
                  options={toolOptions}
                  onValueChange={field.handleChange}
                  defaultValue={currentValue}
                  placeholder={'Select tools'}
                  animation={0}
                  className="w-full"
                  onBlur={field.handleBlur}
                  disabled={isEditMode && !canEdit}
                />
              </div>
            );
          }}
        </form.Field>

        <form.Field name="description">
          {(field) => (
            <div className="grid gap-2">
              <Label htmlFor={field.name}>Description</Label>
              <Textarea
                id={field.name}
                name={field.name}
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="Provide a short description"
                className="min-h-[80px] break-words"
                outline
                disabled={isEditMode && !canEdit}
              />
            </div>
          )}
        </form.Field>

        <form.Field name="prompt">
          {(field) => (
            <div className="grid gap-2">
              <div className="flex items-center">
                <Label htmlFor={field.name}>Prompt</Label>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-6 w-6 rounded-full hover:bg-blue-100 hover:text-blue-600"
                  aria-label="Prompt writing help"
                  type="button"
                  onClick={() => setIsHelpPanelOpen(true)}
                >
                  <Info size={14} />
                </Button>
              </div>
              <Textarea
                id={field.name}
                name={field.name}
                value={field.state.value}
                onBlur={field.handleBlur}
                onChange={(e) => field.handleChange(e.target.value)}
                placeholder="Add a system prompt"
                className="min-h-[300px]"
                outline
                disabled={isEditMode && !canEdit}
              />
            </div>
          )}
        </form.Field>

        <form.Field name="prompt_suggestions">
          {(field) => {
            const handleAddSuggestion = () => {
              if (newSuggestion.trim() === '') return;
              const updatedSuggestions = [
                ...field.state.value,
                newSuggestion.trim(),
              ];
              field.handleChange(updatedSuggestions);
              setNewSuggestion('');
            };

            const handleRemoveSuggestion = (index: number) => {
              const updatedSuggestions = [...field.state.value];
              updatedSuggestions.splice(index, 1);
              field.handleChange(updatedSuggestions);
            };

            const handleKeyDown = (
              e: React.KeyboardEvent<HTMLInputElement>
            ) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                handleAddSuggestion();
              }
            };

            return (
              <div className="grid gap-3">
                <Label htmlFor={field.name}>Prompt Suggestions</Label>
                <div className="flex flex-col gap-4">
                  {field.state.value.length > 0 ? (
                    <div className="bg-muted/20 flex flex-col gap-3 rounded-md">
                      {field.state.value.map(
                        (suggestion: string, index: number) => (
                          <div
                            key={index}
                            className="group flex items-center gap-2"
                          >
                            <div className="text-muted-foreground w-4 text-xs">
                              {index + 1}
                            </div>
                            <Input
                              value={suggestion}
                              onChange={(e) => {
                                const updatedSuggestions = [
                                  ...field.state.value,
                                ];
                                updatedSuggestions[index] = e.target.value;
                                field.handleChange(updatedSuggestions);
                              }}
                              className="flex-1"
                              placeholder="Edit this prompt suggestion"
                              disabled={isEditMode && !canEdit}
                            />
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              className="h-9 w-9 opacity-50 group-hover:opacity-100"
                              onClick={() => handleRemoveSuggestion(index)}
                              disabled={isEditMode && !canEdit}
                            >
                              <span className="sr-only">Remove</span>×
                            </Button>
                          </div>
                        )
                      )}
                    </div>
                  ) : (
                    <div className="text-muted-foreground rounded-md border border-dashed p-4 text-sm">
                      No prompt suggestions added yet.
                    </div>
                  )}

                  <div className="flex gap-2">
                    <Input
                      value={newSuggestion}
                      onChange={(e) => setNewSuggestion(e.target.value)}
                      onKeyDown={handleKeyDown}
                      placeholder="Type a prompt suggestion and press Enter or Add"
                      className="flex-1"
                      disabled={isEditMode && !canEdit}
                    />
                    <Button
                      type="button"
                      variant={
                        newSuggestion.trim() === '' ? 'outline' : 'default'
                      }
                      onClick={handleAddSuggestion}
                      disabled={
                        newSuggestion.trim() === '' || (isEditMode && !canEdit)
                      }
                    >
                      Add
                    </Button>
                  </div>
                </div>
              </div>
            );
          }}
        </form.Field>
      </div>

      <SheetFooter className="flex flex-row justify-between gap-2">
        <div className="flex items-center gap-2">
          {isEditMode && canEdit && (
            <AlertDialog
              open={isDeleteDialogOpen}
              onOpenChange={setIsDeleteDialogOpen}
            >
              <AlertDialogTrigger asChild>
                <Button
                  variant="destructive"
                  type="button"
                  disabled={isDeletingModel}
                >
                  {isDeletingModel ? (
                    'Deleting...'
                  ) : (
                    <>
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete
                    </>
                  )}
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This action cannot be undone. This will permanently delete
                    the model "{model?.name}".
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleDeleteModel}
                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                  >
                    Delete
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          )}
          {isEditMode && !canEdit && (
            <div className="text-foreground/50 text-xs">
              Readonly (Only Owner or Admin can edit)
            </div>
          )}
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            type="button"
            onClick={() => onOpenChange(false)}
          >
            Cancel
          </Button>
          <form.Subscribe selector={(state) => [state.canSubmit]}>
            {([canSubmit]) => (
              <Button
                type="submit"
                disabled={
                  !canSubmit || isLoadingMutation || (isEditMode && !canEdit)
                }
              >
                {submitButtonText}
              </Button>
            )}
          </form.Subscribe>
        </div>
      </SheetFooter>

      {/* Help Panel */}
      <PromptHelpPanel
        isOpen={isHelpPanelOpen}
        onClose={() => setIsHelpPanelOpen(false)}
      />
    </form>
  );
}
