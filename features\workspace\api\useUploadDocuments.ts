import { useMutation, useQueryClient, QueryKey } from '@tanstack/react-query';
import axiosInstanceUi from '@/lib/axiosInstance.ui';
import { API_CONFIG } from '@/config/api';
import { toast } from 'sonner';

// Interface for the upload documents payload
interface UploadDocumentsPayload {
  collectionId: string;
  files: File[];
  documentIds?: string[];
  chatId?: string;
}

// Function to upload documents to a collection
export const uploadDocuments = async ({
  collectionId,
  files,
  documentIds,
  chatId,
}: UploadDocumentsPayload): Promise<void> => {
  console.log('Uploading files:', files);
  // Create form data for the API request
  const formData = new FormData();
  if (collectionId) {
    formData.append('collectionId', collectionId);
  }
  if (documentIds) {
    formData.append('documentIds', JSON.stringify(documentIds));
  }
  if (chatId) {
    formData.append('chatId', chatId);
  }

  // Add all files to the form data
  files.forEach((file) => {
    formData.append('files', file);
  });

  // Upload the files
  await axiosInstanceUi.post(API_CONFIG.ingestion.upload, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    timeout: 60 * 10 * 1000, // 10 minute timeout
  });
};

// Define the query key for the collection detail (to invalidate after upload)
const getCollectionDetailQueryKey = (collectionId: string): QueryKey => [
  API_CONFIG.workspace.collection,
  collectionId,
];

export function useUploadDocuments({
  collectionId,
  onSuccessCallback,
}: {
  collectionId: string;
  onSuccessCallback?: () => void;
}) {
  const queryClient = useQueryClient();

  return useMutation<void, Error, File[]>({
    mutationFn: (files) => uploadDocuments({ collectionId, files }),
    onSuccess: (_, files) => {
      toast.success('Files uploaded successfully', {
        description: `Uploaded ${files.length} file(s) to the collection.`,
      });

      // Invalidate the collection detail query to refresh the documents list
      queryClient.invalidateQueries({
        queryKey: getCollectionDetailQueryKey(collectionId),
      });

      // Call the success callback if provided
      onSuccessCallback?.();
    },
    onError: (error) => {
      console.error('Error uploading files:', error);
      toast.error('Failed to upload files', {
        description:
          error.message || 'An error occurred while uploading files.',
      });
    },
  });
}
