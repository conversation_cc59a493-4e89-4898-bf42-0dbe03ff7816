'use client';

import React, { useState, useMemo } from 'react';
import { useFetchGroups } from '@/features/admin/api/useFetchGroups';
import { IGroupData } from '@/types/group';
import GroupCard from '@/features/admin/components/group/GroupCard';
import GroupSheet from '@/features/admin/components/group/GroupSheet';
import LoadingSpinner from '@/components/LoadingSpinner';
import { PlusIcon, EyeIcon, EyeOffIcon } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';

export default function GroupPage() {
  const { data: groups, isLoading, error } = useFetchGroups();
  const [groupToEdit, setGroupToEdit] = useState<IGroupData | null>(null);
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const [showPersonalGroups, setShowPersonalGroups] = useState(false);

  // Filter out personal groups unless showPersonalGroups is true
  const filteredGroups = useMemo(() => {
    if (!groups) return [];

    return groups.filter((group) => {
      const isPersonal = group.meta?.personal === true;
      return showPersonalGroups ? true : !isPersonal;
    });
  }, [groups, showPersonalGroups]);

  const handleEditGroup = (group: IGroupData) => {
    setGroupToEdit(group);
    setIsSheetOpen(true);
  };

  const handleCreateGroup = () => {
    setGroupToEdit(null);
    setIsSheetOpen(true);
  };

  const togglePersonalGroups = () => {
    setShowPersonalGroups((prev) => !prev);
  };

  if (isLoading) {
    return (
      <div className="flex h-full w-full items-center p-4">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <p className="text-red-500">Error loading groups: {error.message}</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center gap-2">
        <div className="flex items-center space-x-2">
          <Label htmlFor="show-personal-groups" className="cursor-pointer">
            <span>Personal Groups</span>
          </Label>
          <Switch
            id="show-personal-groups"
            checked={showPersonalGroups}
            onCheckedChange={togglePersonalGroups}
          />
        </div>
      </div>

      <div className="grid w-full gap-4 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5">
        {filteredGroups.map((group) => (
          <GroupCard key={group.id} group={group} onEdit={handleEditGroup} />
        ))}

        <Card
          className="hover:bg-foreground/5 border-foreground-200 cursor-pointer rounded-md border-dashed shadow-none"
          onClick={handleCreateGroup}
        >
          <CardContent className="flex h-full items-center justify-center">
            <PlusIcon size={24} />
          </CardContent>
        </Card>

        {isSheetOpen && (
          <GroupSheet
            isOpen={isSheetOpen}
            onOpenChange={setIsSheetOpen}
            group={groupToEdit}
          />
        )}
      </div>
    </div>
  );
}
