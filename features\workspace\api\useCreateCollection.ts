import { useMutation, useQueryClient, QueryKey } from '@tanstack/react-query';
import axiosInstanceUi from '@/lib/axiosInstance.ui';
import { API_CONFIG } from '@/config/api';
import { ICollectionCreate } from '@/types/collection';
import { toast } from 'sonner';

// Function to create a new collection via POST request
const createCollection = async (payload: ICollectionCreate): Promise<void> => {
  console.log('Creating collection:', payload);
  // Assuming API returns the created collection
  await axiosInstanceUi.post<void>(
    // Use the client-side proxy route
    API_CONFIG.workspace.collection,
    payload
  );
};

// Define the query key for the collection list (consistent with useFetchCollections)
const collectionListQueryKey: QueryKey = [API_CONFIG.workspace.collection];

export function useCreateCollection({
  onSuccessCallback,
}: {
  onSuccessCallback?: () => void;
} = {}) {
  const queryClient = useQueryClient();

  return useMutation<void, Error, ICollectionCreate>({
    mutationFn: createCollection,
    onSuccess: () => {
      toast.success(`Collection created successfully!`);

      // Invalidate the collection list query to refresh relevant UI parts
      queryClient.invalidateQueries({ queryKey: collectionListQueryKey });

      // Call the success callback if provided
      onSuccessCallback?.();
    },
    onError: (error) => {
      console.error('Error creating new collection:', error);
      toast.error(`Error creating collection: ${error.message}`);
    },
  });
}
