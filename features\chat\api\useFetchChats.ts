import { useQuery } from '@tanstack/react-query';
import axiosInstanceUi from '@/lib/axiosInstance.ui';
import { API_CONFIG } from '@/config/api';
import { groupChatsByDate } from '@/lib/utils/groupChatsByDate';
import { IChat, IChatGroup } from '@/types/chat';

// Function to fetch the raw chat data and apply grouping
const fetchChats = async (): Promise<IChatGroup[]> => {
  // Use the client-side instance that includes the auth token
  const response = await axiosInstanceUi.get(API_CONFIG.chat.chat);

  if (!response.data) {
    // Return empty array if no data
    return [];
  }

  // Apply grouping logic on the client side
  const groupedChats = groupChatsByDate(response.data);
  return groupedChats;
};

export function useFetchChats() {
  return useQuery<IChatGroup[], Error>({
    queryKey: [API_CONFIG.chat.chat], // Unique query key for chats
    queryFn: fetchChats,
    // Optional configurations:
    // staleTime: 1 * 60 * 1000, // 1 minute
  });
}
