'use client';

import { useEffect } from 'react';

export default function TeamsConfigPage() {
  useEffect(() => {
    // Initialize Microsoft Teams SDK
    if (typeof window !== 'undefined') {
      // Check if we're in Teams context
      const urlParams = new URLSearchParams(window.location.search);
      const inTeams = urlParams.get('inTeams') === 'true';
      
      if (inTeams) {
        // Load Teams SDK dynamically
        const script = document.createElement('script');
        script.src = 'https://res.cdn.office.net/teams-js/2.0.0/js/MicrosoftTeams.min.js';
        script.onload = () => {
          // @ts-ignore - Teams SDK
          if (window.microsoftTeams) {
            // @ts-ignore
            window.microsoftTeams.app.initialize().then(() => {
              // @ts-ignore
              window.microsoftTeams.pages.config.registerOnSaveHandler((saveEvent) => {
                // @ts-ignore
                window.microsoftTeams.pages.config.setConfig({
                  entityId: 'chuck-studio-tab',
                  contentUrl: `${window.location.origin}/c`,
                  websiteUrl: `${window.location.origin}/c`,
                  suggestedDisplayName: 'Chuck Studio'
                });
                saveEvent.notifySuccess();
              });
              
              // @ts-ignore
              window.microsoftTeams.pages.config.setValidityState(true);
            });
          }
        };
        document.head.appendChild(script);
      }
    }
  }, []);

  return (
    <div className="flex min-h-screen items-center justify-center bg-background">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-4">Chuck Studio Teams Configuration</h1>
        <p className="text-muted-foreground">
          Configuring Chuck Studio for Microsoft Teams...
        </p>
      </div>
    </div>
  );
}
