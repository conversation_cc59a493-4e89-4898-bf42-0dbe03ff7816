import { ChartConfiguration } from 'chart.js';
import { ThemeOptions } from './types';

/**
 * Applies dark theme styling to a Chart.js configuration
 *
 * @param config - Chart.js configuration object
 * @param options - Theme options containing gridColor and fontColor
 * @returns The modified Chart.js configuration with dark theme applied
 */
export function applyDarkTheme(
  config: ChartConfiguration,
  options: ThemeOptions
): ChartConfiguration {
  const { gridColor, fontColor } = options;

  const updatedConfig = JSON.parse(
    JSON.stringify(config)
  ) as ChartConfiguration;

  if (!updatedConfig.options) {
    updatedConfig.options = {};
  }

  if (!updatedConfig.options.plugins) {
    updatedConfig.options.plugins = {};
  }

  if (updatedConfig.options.plugins.title) {
    // @ts-ignore - The Chart.js types aren't perfectly aligned with the actual API
    updatedConfig.options.plugins.title.color = fontColor;
  }

  if (updatedConfig.options.plugins.legend) {
    // @ts-ignore - The Chart.js types aren't perfectly aligned with the actual API
    updatedConfig.options.plugins.legend.labels = {
      ...(updatedConfig.options.plugins.legend.labels || {}),
      color: fontColor,
    };
  }

  if (!updatedConfig.options.scales) {
    updatedConfig.options.scales = {};
  }

  if (updatedConfig.options.scales.x) {
    // @ts-ignore - The Chart.js types aren't perfectly aligned with the actual API
    updatedConfig.options.scales.x.ticks = {
      ...(updatedConfig.options.scales.x.ticks || {}),
      color: fontColor,
    };

    // @ts-ignore - The Chart.js types aren't perfectly aligned with the actual API
    updatedConfig.options.scales.x.title = {
      ...((updatedConfig.options.scales.x as any).title || {}),
      color: fontColor,
    };

    // @ts-ignore - The Chart.js types aren't perfectly aligned with the actual API
    updatedConfig.options.scales.x.grid = {
      ...(updatedConfig.options.scales.x.grid || {}),
      color: gridColor,
    };
  }

  if (updatedConfig.options.scales.y) {
    // @ts-ignore - The Chart.js types aren't perfectly aligned with the actual API
    updatedConfig.options.scales.y.ticks = {
      ...(updatedConfig.options.scales.y.ticks || {}),
      color: fontColor,
    };

    // @ts-ignore - The Chart.js types aren't perfectly aligned with the actual API
    updatedConfig.options.scales.y.title = {
      ...((updatedConfig.options.scales.y as any).title || {}),
      color: fontColor,
    };

    // @ts-ignore - The Chart.js types aren't perfectly aligned with the actual API
    updatedConfig.options.scales.y.grid = {
      ...(updatedConfig.options.scales.y.grid || {}),
      color: gridColor,
    };
  }

  if (updatedConfig.type === 'polarArea' || updatedConfig.type === 'radar') {
    updatedConfig.options.scales = updatedConfig.options.scales || {};

    // @ts-ignore - The Chart.js types aren't perfectly aligned with the actual API
    updatedConfig.options.scales.r = {
      ...(updatedConfig.options.scales.r || {}),
      grid: { color: gridColor },
      angleLines: { color: gridColor },
      ticks: { color: fontColor },
      pointLabels: { color: fontColor },
    };
  }

  return updatedConfig;
}
