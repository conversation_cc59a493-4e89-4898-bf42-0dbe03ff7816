import { useQuery, Query<PERSON><PERSON> } from '@tanstack/react-query';
import axiosInstanceUi from '@/lib/axiosInstance.ui';
import { API_CONFIG } from '@/config/api';
import { ICollectionData } from '@/types/collection';

// Function to fetch a single collection by its ID
export const fetchCollectionById = async (
  collectionId: string
): Promise<ICollectionData> => {
  const response = await axiosInstanceUi.get<ICollectionData>(
    // Use the client-side proxy route
    `${API_CONFIG.workspace.collection}/${collectionId}`
  );

  if (!response.data) {
    throw new Error('Collection not found or empty response');
  }

  return response.data;
};

// Define the query key structure
const collectionDetailQueryKey = (collectionId: string): QueryKey => [
  API_CONFIG.workspace.collection, // Correct path
  collectionId,
];

export function useFetchCollectionById(
  collectionId: string | null | undefined,
  options?: {
    enabled?: boolean;
    staleTime?: number;
    gcTime?: number;
    refetchOnMount?: boolean;
    refetchOnWindowFocus?: boolean;
    refetchOnReconnect?: boolean;
  }
) {
  return useQuery<ICollectionData, Error>({
    queryKey: collectionDetailQueryKey(collectionId ?? ''), // Ensure key is always an array
    queryFn: ({ queryKey }) => {
      const id = queryKey[1] as string;
      if (!id) {
        // This should theoretically not happen if enabled is false when id is null/undefined
        return Promise.reject(
          new Error('Collection ID is required for queryFn')
        );
      }
      return fetchCollectionById(id);
    },
    // Query is only enabled if collectionId is truthy
    enabled: !!collectionId && options?.enabled !== false, // Respect passed enabled option
    // Apply default caching behavior or passed options
    staleTime: options?.staleTime ?? 5 * 60 * 1000, // Default 5 mins
    gcTime: options?.gcTime ?? 10 * 60 * 1000, // Default 10 mins
    refetchOnMount: options?.refetchOnMount ?? true,
    refetchOnWindowFocus: options?.refetchOnWindowFocus ?? true,
    refetchOnReconnect: options?.refetchOnReconnect ?? true,
  });
}
