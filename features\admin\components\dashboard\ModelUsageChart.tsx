'use client';

import React from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, ResponsiveContainer } from 'recharts';
import { Skeleton } from '@/components/ui/skeleton';

interface ModelUsageChartProps {
  data: {
    name: string;
    usage: number;
  }[];
  isLoading?: boolean;
}

export default function ModelUsageChart({
  data,
  isLoading = false,
}: ModelUsageChartProps) {
  const chartConfig = {
    usage: {
      label: 'Usage',
      color: 'var(--chart-5)',
    },
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Model Usage</CardTitle>
        <CardDescription>Usage distribution across models</CardDescription>
      </CardHeader>
      <CardContent className="h-[300px]">
        {isLoading ? (
          <div className="flex h-full w-full items-center justify-center">
            <Skeleton className="h-[250px] w-full" />
          </div>
        ) : (
          <ChartContainer config={chartConfig}>
            <BarChart data={data} margin={{ top: 5, right: 5, bottom: 25, left: 5 }}>
              <CartesianGrid strokeDasharray="3 3" vertical={false} />
              <XAxis
                dataKey="name"
                tickLine={false}
                axisLine={false}
                tickMargin={10}
                tick={{ fontSize: 12 }}
                angle={-45}
                textAnchor="end"
              />
              <YAxis
                tickLine={false}
                axisLine={false}
                tickMargin={10}
                tick={{ fontSize: 12 }}
              />
              <ChartTooltip
                content={({ active, payload }) => (
                  <ChartTooltipContent
                    active={active}
                    payload={payload}
                    formatter={(value) => value.toLocaleString()}
                  />
                )}
              />
              <Bar
                dataKey="usage"
                fill="var(--chart-5)"
                radius={[4, 4, 0, 0]}
              />
            </BarChart>
          </ChartContainer>
        )}
      </CardContent>
    </Card>
  );
}
