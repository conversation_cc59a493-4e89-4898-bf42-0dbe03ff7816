import * as React from 'react';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
  SelectSeparator,
} from '@/components/ui/select';
import { cn } from '@/lib/utils';
import {
  useFetchRawModels,
  useFetchGroupedModels,
} from '@/features/workspace/api/useFetchModels';
import { useChuckContext } from '@/contexts/ChuckContext';
import { useEffect, useMemo } from 'react';
import { ModelAvatar } from '@/components/ui/model-avatar';
import { UI_CONFIG } from '@/config/ui';

const CHUCK_MODEL_NAME = UI_CONFIG.KEYS.CHUCK.name;

export default function ChatModelSelector() {
  const {
    data: rawModels,
    isPending: isRawPending,
    error: rawError,
  } = useFetchRawModels();

  const { data: groupedModels, isPending: isGroupedPending } =
    useFetchGroupedModels();
  const isPending = isRawPending || isGroupedPending;
  const error = rawError;

  const {
    store: { modelId, setModelId },
  } = useChuckContext();

  // Effect to set the default model only when models are first loaded
  useEffect(() => {
    // Check if loading is done, models exist, and no model is currently selected
    if (!isPending && rawModels && rawModels.length > 0 && !modelId) {
      const chuckModel = rawModels.find(
        (model) => model.name.toLowerCase() === CHUCK_MODEL_NAME
      );

      if (chuckModel) {
        setModelId(chuckModel.id);
      } else {
        setModelId(rawModels[0].id);
      }
    }
    // We only want to run this logic when models load or if modelId becomes empty
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [rawModels, isPending, modelId]);

  const placeholderText = isPending
    ? 'Loading models...'
    : error
      ? 'Error loading models'
      : 'Select a Model';

  // Find the currently selected model
  const selectedModel = useMemo(() => {
    if (!modelId || !rawModels) return null;
    return rawModels.find((model) => model.id === modelId);
  }, [modelId, rawModels]);

  const handleValueChange = (newValue: string) => {
    if (newValue && newValue !== modelId) {
      // Update the context state with the user's selection
      setModelId(newValue);

      // Dispatch a custom event to notify other components that the user manually selected a model
      window.dispatchEvent(
        new CustomEvent('userSelectedModel', { detail: { modelId: newValue } })
      );
    }
  };

  return (
    <div className="bg-background rounded-lg">
      <Select
        disabled={isPending || !!error}
        value={modelId || ''} // Control the selected value from context state
        onValueChange={handleValueChange} // Update context state on change
        defaultOpen={false}
      >
        <SelectTrigger
          className={cn(
            'hover:bg-accent h-auto max-w-[580px] cursor-pointer justify-start truncate border-none bg-transparent px-2 py-1 text-xl font-bold shadow-none outline-none focus:ring-0 focus:ring-offset-0 disabled:cursor-not-allowed disabled:opacity-50'
          )}
          // Add title attribute to show error on hover if disabled due to error
          title={
            error
              ? `Error: ${error.message}`
              : selectedModel?.name || placeholderText
          }
        >
          <SelectValue
            placeholder={placeholderText}
            className="truncate overflow-hidden overflow-ellipsis whitespace-nowrap"
          />
        </SelectTrigger>
        <SelectContent>
          {groupedModels && groupedModels.length > 0 ? (
            <>
              {groupedModels.map((group, index) => (
                <React.Fragment key={group.type}>
                  {index > 0 && <SelectSeparator />}
                  <SelectGroup>
                    <SelectLabel>{group.name}</SelectLabel>
                    {group.data.map((model) => (
                      <SelectItem key={model.id} value={model.id}>
                        <div className="flex items-center gap-2 truncate">
                          <ModelAvatar
                            image={model.image}
                            name={model.name}
                            size="md"
                          />
                          {model.name}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </React.Fragment>
              ))}
            </>
          ) : (
            // Optional: Display something if no models are loaded (even after loading finished)
            !isPending && (
              <div className="text-muted-foreground p-2 text-center text-sm">
                No models available
              </div>
            )
          )}
        </SelectContent>
      </Select>
    </div>
  );
}
