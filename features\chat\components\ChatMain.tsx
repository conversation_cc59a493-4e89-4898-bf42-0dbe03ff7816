'use client'; // Ensure this is a client component

import React, { useState, useEffect, useLayoutEffect, useRef } from 'react';
import { useFetchChatById } from '@/features/chat/api/useFetchChatById';
import ChatMessage from './ChatMessage';
import ChatInput from './ChatInput';
import { cn } from '@/lib/utils'; // Import cn
import { useChuckContext } from '@/contexts/ChuckContext';

import { ChatService } from '@/services/chat.service';
import { getWebSocketUrl } from '@/lib/api';
import { IMessage, MessageRoleEnum } from '@/types/chat';
import { IToolProgress } from '@/types/chat/tool-progress';
import { useParams } from 'next/navigation';
import { useQueryClient } from '@tanstack/react-query';
import { API_CONFIG } from '@/config/api';
import { useCreateChat } from '@/features/chat/api/useCreateChat';
import { getTimestamp } from './helpers/getTimestamp';
import ChatToolProgress from './ChatToolProgress';
import { Loader2 } from 'lucide-react';

const HEIGHT_OFFSET = 10;

export default function ChatMain() {
  const {
    store: { newChatMessage, modelId, setModelId },
  } = useChuckContext();
  const createChatMutation = useCreateChat();
  const queryClient = useQueryClient();
  const [shouldInvalidateChatQuery, setShouldInvalidateChatQuery] =
    useState(false);
  const chatId = useParams().chatId as string;
  const { data: chatData, isLoading } = useFetchChatById(
    chatId,
    newChatMessage === null
  );
  const [messages, setMessages] = useState<IMessage[]>(
    newChatMessage ? [newChatMessage] : []
  );
  const [isStreaming, setIsStreaming] = useState(false);
  const [isUserScrolling, setIsUserScrolling] = useState(false);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [isInitializingChat, setIsInitializingChat] = useState(false);
  const [hasSetModelFromChat, setHasSetModelFromChat] = useState(false);
  const [toolProgress, setToolProgress] = useState<
    IToolProgress[] | undefined
  >();
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const chatServiceRef = useRef<ChatService | null>(null);
  const chatInputContainerRef = useRef<HTMLDivElement>(null);
  const chatInputInnerContainerRef = useRef<HTMLDivElement>(null);
  const previousChatIdRef = useRef<string | null>(null);

  useEffect(() => {
    if (!chatInputInnerContainerRef.current) return;

    // Store the initial height
    let previousHeight = chatInputInnerContainerRef.current.clientHeight;

    // Create a ResizeObserver to monitor height changes
    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const currentHeight = entry.contentRect.height;

        // Only log if the height has actually changed
        if (currentHeight !== previousHeight) {
          previousHeight = currentHeight;

          // Update chatInputContainerRef's height
          if (chatInputContainerRef.current) {
            chatInputContainerRef.current.style.height = `${currentHeight - HEIGHT_OFFSET}px`;
          }
        }
      }
    });

    // Start observing the element
    resizeObserver.observe(chatInputInnerContainerRef.current);

    // Clean up the observer when component unmounts
    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  useEffect(() => {
    if (newChatMessage || !chatData) return;
    const formattedMessages: IMessage[] = chatData.messages.map((message) => {
      return {
        ...message,
        content: message.content || '',
        attachments: message.attachments,
        ts: message.ts as number,
      };
    });
    setMessages(formattedMessages);

    // Only set the model from chat history if we haven't done it yet for this chat
    if (!hasSetModelFromChat) {
      // Find the latest assistant message with a model ID in its config
      const assistantMessages = formattedMessages.filter(
        (msg) => msg.role === MessageRoleEnum.ASSISTANT && msg.config?.modelId
      );

      if (assistantMessages.length > 0) {
        // Get the latest message (last in the array)
        const latestMessage = assistantMessages[assistantMessages.length - 1];
        const latestModelId = latestMessage.config?.modelId;

        // Update the selected model if we found a valid model ID
        if (latestModelId) {
          setModelId(latestModelId);
          setHasSetModelFromChat(true);
        }
      }
    }

    // Mark as initial load when messages are first loaded from chatData
    setIsInitialLoad(true);
  }, [chatData, hasSetModelFromChat, setModelId]);

  useEffect(() => {
    const inner = async () => {
      chatServiceRef.current = new ChatService({
        endpoint: getWebSocketUrl(`/api/chat/${chatId}`),
        // endpoint: getWebSocketUrl(`/api/chat/test-queue/${chatId}`),
        onMessage,
        onStreamEnd: () => {
          setIsStreaming(false);
        },
        onProgress: onToolProgress,
      });

      if (newChatMessage) {
        setIsStreaming(true);
        await createChatMutation.mutateAsync({
          chatId,
          message: newChatMessage.content,
        });
        chatServiceRef.current?.sendMessage(newChatMessage);
      }
    };

    inner();

    return () => {
      chatServiceRef.current?.dispose();
    };
  }, [chatId]);

  useEffect(() => {
    return () => {
      shouldInvalidateChatQuery &&
        queryClient.invalidateQueries({
          queryKey: [API_CONFIG.chat.chat, chatId],
        });
    };
  }, [shouldInvalidateChatQuery]);

  // Add wheel event listener to detect user scrolling
  useEffect(() => {
    const scrollArea = scrollAreaRef.current;
    if (!scrollArea) return;

    const handleWheel = () => {
      // Set user scrolling to true when wheel event is detected
      // Once set to true, it will stay true until user sends a new message
      setIsUserScrolling(true);
    };

    scrollArea.addEventListener('wheel', handleWheel, { passive: true });

    return () => {
      scrollArea.removeEventListener('wheel', handleWheel);
    };
  }, []);

  // Listen for user model selection events
  useEffect(() => {
    const handleUserModelSelection = () => {
      // When user manually selects a model, set the flag to prevent auto-selection from chat history
      setHasSetModelFromChat(true);
    };

    window.addEventListener('userSelectedModel', handleUserModelSelection);

    return () => {
      window.removeEventListener('userSelectedModel', handleUserModelSelection);
    };
  }, []);

  // Separate effect specifically for chat ID changes - using useLayoutEffect to ensure it runs before paint
  useLayoutEffect(() => {
    if (!scrollAreaRef.current) return;

    // Always use instant scroll (no animation) when chat ID changes
    scrollAreaRef.current.scrollTo({
      top: scrollAreaRef.current.scrollHeight,
      behavior: 'auto', // Force no animation for chat switches
    });

    // Reset states when switching chats
    setIsUserScrolling(false);
    setHasSetModelFromChat(false); // Reset this flag to allow model to be set from the new chat

    // Update the reference to current chat
    previousChatIdRef.current = chatId;
  }, [chatId]); // Only depend on chatId to ensure this runs only on chat changes

  // Auto-scroll to bottom when messages change (but not on chat ID changes)
  useEffect(() => {
    // Skip if this is a chat change (handled by the effect above)
    if (previousChatIdRef.current !== chatId) return;

    if (!scrollAreaRef.current) return;

    // Skip auto-scroll if user has manually scrolled
    if (isUserScrolling) return;

    // For initial load, use instant scroll
    if (isInitialLoad) {
      scrollAreaRef.current.scrollTo({
        top: scrollAreaRef.current.scrollHeight,
        behavior: 'auto', // No animation for initial load
      });

      // Delay marking initial load as false to avoid flickering
      setTimeout(() => {
        setIsInitialLoad(false);
      }, 100);
    } else {
      // For new messages during chat, use smooth scrolling
      scrollAreaRef.current.scrollTo({
        top: scrollAreaRef.current.scrollHeight,
        behavior: 'smooth',
      });
    }
  }, [messages, isUserScrolling, isInitialLoad, chatId]);

  const handleSendMessage = async (message: IMessage) => {
    if (message.content.trim() || message.attachments.length > 0) {
      setMessages((prev) => [...prev, message]);
      setIsInitializingChat(true);
      // Reset scroll flags when sending a new message
      setIsUserScrolling(false);
      setIsInitialLoad(false); // Ensure smooth scrolling for user-sent messages
      await chatServiceRef.current?.sendMessage(message);
      setIsInitializingChat(false);
      setIsStreaming(true);
      setShouldInvalidateChatQuery(true);
    }
  };

  const onToolProgress = (data: IToolProgress) => {
    setToolProgress((prev) => {
      if (prev && prev.length > 0) {
        return [...prev, data];
      }
      return [data];
    });
  };

  const onMessage = (data: any) => {
    setMessages((prev) => {
      setToolProgress(undefined);
      const { id, text, config } = data;
      const message = prev.find((msg) => msg.id === id);

      // If the message has a model ID in its config and we haven't manually selected a model,
      // update the selected model
      if (config?.modelId && !hasSetModelFromChat) {
        setModelId(config.modelId);
        setHasSetModelFromChat(true);
      }

      // Check if the message already exists
      if (message) {
        if (text) {
          return prev.map((msg) =>
            msg.id === id
              ? {
                  ...msg,
                  content: msg.content ? `${msg.content}${text}` : text,
                  config: config || msg.config, // Preserve or update config
                }
              : msg
          );
        }
      }

      // Otherwise, add a new message
      return [
        ...prev,
        {
          id,
          ts: getTimestamp(),
          role: MessageRoleEnum.ASSISTANT,
          content: text,
          attachments: [],
          config, // Include the config in the new message
        },
      ];
    });
  };

  return (
    <div className="flex h-full w-full flex-col">
      <div ref={scrollAreaRef} className="flex-1 overflow-auto px-4 py-[60px]">
        <div className={cn('flex w-full flex-1 justify-center')}>
          <div className="flex w-full max-w-[860px] flex-col gap-y-6 px-2 sm:px-0">
            {messages.map((message, index) => (
              <ChatMessage
                chatId={chatId}
                key={`${message.id}-${index}`}
                message={message}
                isLoading={
                  index === messages.length - 1 && (isStreaming || isLoading)
                }
                isStreaming={isStreaming}
              />
            ))}

            <div className="flex items-center gap-2 px-2">
              {(isLoading || isStreaming) && (
                <Loader2 className="text-foreground/30 animate-spin" />
              )}
              {toolProgress && <ChatToolProgress toolProgress={toolProgress} />}
            </div>
          </div>
        </div>
      </div>

      <div
        ref={chatInputContainerRef}
        className="relative flex h-[150px] w-full justify-center"
      >
        <div
          ref={chatInputInnerContainerRef}
          className="absolute bottom-0 w-full max-w-[860px] px-4 pb-3 md:px-0"
        >
          <ChatInput
            onSend={handleSendMessage}
            onCancel={() => {
              chatServiceRef.current?.send(
                JSON.stringify({
                  e: 'cancel',
                })
              );
              setIsStreaming(false);
            }}
            withImagePaste
            isLoading={isStreaming}
            uploading={isInitializingChat}
            disabled={!modelId || isInitializingChat}
            textDisabled={isInitializingChat}
          />
          <div className="pt-3 text-center text-sm text-gray-500">
            Chuck can make mistakes. Check important info.
          </div>
        </div>
      </div>
    </div>
  );
}
