import { useQuery } from '@tanstack/react-query';
import axiosInstanceUi from '@/lib/axiosInstance.ui';
import { API_CONFIG } from '@/config/api';
import { IModelData } from '@/types/model';

// Function to fetch a single chat by its ID
export const fetchModelById = async (modelId: string): Promise<IModelData> => {
  console.log('fetching model by id', modelId);
  const response = await axiosInstanceUi.get<IModelData>(
    `${API_CONFIG.chat.model}/${modelId}`
  );

  if (!response.data) {
    throw new Error('Chat not found or empty response'); // Or handle as needed
  }

  return response.data;
};

export function useFetchModelById(modelId: string | null | undefined) {
  return useQuery<IModelData, Error>({
    queryKey: [API_CONFIG.chat.model, modelId],
    queryFn: ({ queryKey }) => {
      const id = queryKey[1] as string;
      if (!id) {
        return Promise.reject(new Error('Model ID is required'));
      }
      return fetchModelById(id);
    },
    enabled: !!modelId,
    // Disable caching
    // staleTime: 0, // Data is always stale
    // gcTime: 0, // Remove from cache when inactive
    // refetchOnMount: false, // Don't refetch just because component mounts
    // refetchOnWindowFocus: false, // Don't refetch on window focus
    // refetchOnReconnect: false, // Don't refetch on network reconnect
  });
}
