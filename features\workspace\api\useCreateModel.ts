import { useMutation, useQueryClient, QueryKey } from '@tanstack/react-query';
import axiosInstanceUi from '@/lib/axiosInstance.ui'; // Use the UI axios instance
import { API_CONFIG } from '@/config/api';
import { IModelData, IModelCreatePayload } from '@/types/model';
import { toast } from 'sonner'; // Assuming sonner is used for notifications

// Function to create a new model via POST request
const createModel = async (payload: IModelCreatePayload): Promise<void> => {
  await axiosInstanceUi.post<void>(
    API_CONFIG.chat.model, // Endpoint for creating a model
    payload
  );
};

// Define the query key for the model list (consistent with useFetchModels)
const modelListQueryKey: QueryKey = [API_CONFIG.chat.model];

export function useCreateModel({
  onSuccessCallback,
}: {
  onSuccessCallback?: () => void;
} = {}) {
  const queryClient = useQueryClient();

  return useMutation<void, Error, IModelCreatePayload>({
    mutationFn: createModel,
    onSuccess: (newModel) => {
      console.log('New agent created successfully:', newModel);
      toast.success(`Model created successfully!`);

      // Invalidate the model list query to refresh relevant UI parts
      queryClient.invalidateQueries({ queryKey: modelListQueryKey });

      // Invalidate all model versions queries since a new model was created
      // This ensures any open version lists will refresh if needed
      queryClient.invalidateQueries({
        queryKey: ['model-versions'],
      });

      // Optionally, pre-populate the cache for the new model query if needed
      // queryClient.setQueryData([API_CONFIG.chat.model, newModel.id], newModel);

      // Call the success callback if provided
      onSuccessCallback?.();
    },
    onError: (error) => {
      console.error('Error creating new agent:', error);
      toast.error(`Error creating agent: ${error.message}`);
    },
  });
}
