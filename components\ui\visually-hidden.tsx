import { cn } from "@/lib/utils";
import { Slot } from "@radix-ui/react-slot";
import React from "react";

interface VisuallyHiddenProps {
  children: React.ReactNode;
  asChild?: boolean;
  className?: string;
}

/**
 * VisuallyHidden component that hides content visually but keeps it accessible to screen readers
 */
export function VisuallyHidden({
  children,
  asChild = false,
  className,
  ...props
}: VisuallyHiddenProps & React.HTMLAttributes<HTMLSpanElement>) {
  const Comp = asChild ? Slot : "span";

  return (
    <Comp
      className={cn(
        "absolute h-px w-px overflow-hidden whitespace-nowrap border-0 p-0",
        "clip-[rect(0,0,0,0)]",
        className
      )}
      {...props}
    >
      {children}
    </Comp>
  );
}
