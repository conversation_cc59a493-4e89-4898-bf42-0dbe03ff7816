'use client';

import { Tabs, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { AdminGuard } from '@/features/auth/components/PermissionGuard';

interface ILayoutProps {
  children: React.ReactNode;
}

export default function AdminLayout({ children }: ILayoutProps) {
  const router = useRouter();
  const pathname = usePathname();

  // Determine initial tab value based on the current URL path
  const getTabValueFromPath = (path: string): string => {
    if (path.includes('/admin/dashboard')) return 'dashboard';
    if (path.includes('/admin/user')) return 'user';
    if (path.includes('/admin/group')) return 'group';
    if (path.includes('/admin/activity')) return 'activity';
    return 'dashboard'; // Default to dashboard tab
  };

  const [tabValue, setTabValue] = useState(getTabValueFromPath(pathname));

  // Update tab value when pathname changes
  useEffect(() => {
    setTabValue(getTabValueFromPath(pathname));
  }, [pathname]);

  const handleTabChange = (value: string) => {
    setTabValue(value);
    router.push(`/admin/${value}`);
  };

  return (
    <AdminGuard>
      <div className="flex h-full w-full flex-col justify-center gap-4 p-4">
        <div className="flex justify-center sm:justify-between">
          <Tabs
            defaultValue={tabValue}
            value={tabValue}
            onValueChange={handleTabChange}
          >
            <TabsList>
              <TabsTrigger className="cursor-pointer" value="dashboard">
                Dashboard
              </TabsTrigger>
              <TabsTrigger className="cursor-pointer" value="activity">
                Activity
              </TabsTrigger>
              <TabsTrigger className="cursor-pointer" value="user">
                Users
              </TabsTrigger>
              <TabsTrigger className="cursor-pointer" value="group">
                Groups
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
        <div>{children}</div>
      </div>
    </AdminGuard>
  );
}
