export class RecordingService {
  private ws: WebSocket | null = null;
  private endpoint: string;
  private onError?: (error: string) => void;

  constructor({
    endpoint,
    onError,
  }: {
    endpoint: string;
    onError?: (error: string) => void;
  }) {
    this.endpoint = endpoint;
    this.onError = onError;
  }

  public async startRecording() {
    await this.wsAssert();
    this.ws?.send(JSON.stringify({ e: 'start_recording' }));
  }

  public async stopRecording() {
    await this.wsAssert();
    this.ws?.send(JSON.stringify({ e: 'stop_recording' }));
  }

  public async cancelRecording() {
    await this.wsAssert();
    this.ws?.send(JSON.stringify({ e: 'cancel' }));
  }

  public async sendTranscription(data: any) {
    await this.wsAssert();
    this.ws?.send(
      JSON.stringify({
        e: 'transcription',
        d: data,
      })
    );
  }

  public async sendAudioData(audioData: ArrayBuffer) {
    await this.wsAssert();
    this.ws?.send(audioData);
  }

  private async wsAssert() {
    if (!this.ws || this.ws.readyState === WebSocket.CLOSED) {
      await this.wsInit().catch((err) => {
        console.error('Failed to connect to recording service:', err);
        throw err;
      });
    }
  }

  private async wsInit() {
    return new Promise<void>((resolve, reject) => {
      this.ws = new WebSocket(this.endpoint);

      this.ws.onopen = () => {
        console.log('WebSocket connected for recording');
        resolve();
      };

      this.ws.onerror = (err) => {
        console.error('WebSocket error:', err);
        reject(err);
      };

      // Add message processing for error handling only
      this.ws.onmessage = (event) => {
        if (event.data instanceof Blob) {
          return;
        }

        try {
          const data = JSON.parse(event.data);
          switch (data.e) {
            case 'error':
              console.error('Recording error:', data.d);
              this.onError?.(data.d?.message || 'Recording error occurred');
              break;
            default:
              // Ignore other messages
              break;
          }
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };

      this.ws.onclose = () => {
        this.wsClose();
      };
    });
  }

  private wsClose() {
    this.ws?.close();
    this.ws = null;
  }

  public dispose() {
    this.wsClose();
  }
}
