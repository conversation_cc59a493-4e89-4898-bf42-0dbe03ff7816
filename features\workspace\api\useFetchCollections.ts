import { useQuery } from '@tanstack/react-query';
import axiosInstanceUi from '@/lib/axiosInstance.ui'; // Use the UI axios instance
import { API_CONFIG } from '@/config/api';
import { IModelData } from '@/types/model';
import { ICollectionData } from '@/types/collection';

const fetchCollections = async (): Promise<ICollectionData[]> => {
  const response = await axiosInstanceUi.get(API_CONFIG.workspace.collection);
  return response.data;
};

export function useFetchCollections() {
  return useQuery<ICollectionData[], Error>({
    queryKey: [API_CONFIG.workspace.collection], // Use a descriptive query key
    queryFn: fetchCollections, // Use the separated fetch function
    // Optional: Configure staleTime, gcTime, refetchOnWindowFocus, etc.
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}
