import { useMutation, useQueryClient, QueryKey } from '@tanstack/react-query';
import axiosInstanceUi from '@/lib/axiosInstance.ui';
import { API_CONFIG } from '@/config/api';
import { IModelCreatePayload } from '@/types/model'; // Reusing payload type for now
import { toast } from 'sonner';

// Function to update an existing model via PUT request
const updateModel = async ({
  modelId,
  payload,
}: {
  modelId: string;
  payload: IModelCreatePayload; // Assuming payload structure is similar enough for PUT
}): Promise<void> => {
  await axiosInstanceUi.patch<void>(
    `${API_CONFIG.chat.model}/${modelId}`, // Endpoint for updating a specific model
    payload
  );
};

// Define the query key for the model list
const modelListQueryKey: QueryKey = [API_CONFIG.chat.model];

export function useUpdateModel({
  onSuccessCallback,
}: {
  onSuccessCallback?: () => void;
} = {}) {
  const queryClient = useQueryClient();

  return useMutation<
    void, // Return type
    Error, // Error type
    { modelId: string; payload: IModelCreatePayload } // Input type for mutationFn
  >({
    mutationFn: updateModel,
    onSuccess: (updatedModel, variables) => {
      console.log('Model updated successfully:', updatedModel);
      toast.success(`Model updated successfully!`);

      // Invalidate the model list query
      queryClient.invalidateQueries({ queryKey: modelListQueryKey });
      // Invalidate the specific model query
      queryClient.invalidateQueries({
        queryKey: [API_CONFIG.chat.model, variables.modelId],
      });
      // Invalidate the model versions query to refresh the version list
      queryClient.invalidateQueries({
        queryKey: ['model-versions', variables.modelId],
      });

      // Call the success callback if provided
      onSuccessCallback?.();
    },
    onError: (error, variables) => {
      console.error(`Error updating model ${variables.modelId}:`, error);
      toast.error(`Error updating model: ${error.message}`);
    },
  });
}
