import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';

export async function POST(request: NextRequest) {
  try {
    const supportTicketData = await request.json();

    // Get the support platform URL from environment variables
    const supportPlatformUrl =
      process.env.SUPPORT_PLATFORM_SDP_TICKET_SUBMISSION_URL;

    if (!supportPlatformUrl) {
      return NextResponse.json(
        { success: false, message: 'Support platform URL not configured' },
        { status: 500 }
      );
    }

    // Forward the request to the support platform
    const response = await axios.post(supportPlatformUrl, supportTicketData);

    console.log('[Support API Response]:', response);

    return NextResponse.json(
      { success: true, data: response.data },
      { status: 200 }
    );
  } catch (error: any) {
    console.error(
      '[Support API Error]:',
      error.response?.data || error.message
    );

    const status = error.response?.status || 500;
    const message =
      error.response?.data?.message || 'Error submitting support ticket';

    return NextResponse.json({ success: false, message }, { status });
  }
}
