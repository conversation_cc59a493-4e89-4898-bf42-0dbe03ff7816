import { useQuery } from '@tanstack/react-query';
import axiosInstanceUi from '@/lib/axiosInstance.ui';
import { API_CONFIG } from '@/config/api';
import { IGroupData } from '@/types/group';

// Function to fetch the group data
const fetchGroups = async (): Promise<IGroupData[]> => {
  // Use the client-side instance that includes the auth token
  const response = await axiosInstanceUi.get(API_CONFIG.admin.group);

  if (!response.data) {
    // Return empty array if no data
    return [];
  }
  return response.data;
};

export function useFetchGroups() {
  return useQuery<IGroupData[], Error>({
    queryKey: [API_CONFIG.admin.group], // Unique query key for groups
    queryFn: fetchGroups,
  });
}
