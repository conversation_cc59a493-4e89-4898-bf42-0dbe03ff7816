{
  // General settings
  "editor.formatOnSave": true, // Format code on save
  "editor.defaultFormatter": "esbenp.prettier-vscode", // Use Prettier as the default formatter
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": "explicit" // Run ESLint fix on save
  },

  // Files settings
  "files.eol": "\n", // Ensure consistent line endings
  "files.associations": {
    "*.css": "css"
  },

  // ESLint settings
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "typescript",
    "typescriptreact",
    "json"
  ],
  "eslint.useFlatConfig": true, // Enable if using eslint.config.js (default in ESLint v9)

  // Prettier settings
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[jsonc]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[markdown]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[css]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[scss]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },

  "editor.quickSuggestions": {
    "strings": true
  },
  "cSpell.words": [
    "cmdk",
    "cognitiveservices",
    "kwargs",
    "Mawer",
    "msal",
    "shadcn",
    "sonner",
    "turbopack",
    "uuidv"
  ]
}
