import { NextRequest, NextResponse } from 'next/server';
import axiosInstanceApi from '@/lib/axiosInstance.api';
import { API_CONFIG } from '@/config/api';

export async function GET(request: NextRequest) {
  const authorization = request.headers.get('Authorization');
  const targetPath = API_CONFIG.admin.userList;

  try {
    const apiResponse = await axiosInstanceApi.get(targetPath, {
      headers: {
        ...(authorization && { Authorization: authorization }),
      },
    });

    if (apiResponse.data) {
      // Sort users by name
      apiResponse.data.sort((a: any, b: any) => a.name.localeCompare(b.name));
    }

    return NextResponse.json(apiResponse.data, { status: apiResponse.status });
  } catch (error: any) {
    console.error(
      `[API Route Proxy Error] GET ${targetPath}:`,
      error.response?.data || error.message
    );
    const status = error.response?.status || 500;
    const message =
      error.response?.data?.message || 'Proxy error fetching users';
    return NextResponse.json({ message }, { status });
  }
}
