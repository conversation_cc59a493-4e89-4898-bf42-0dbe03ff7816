'use client';

import React, { useState, useMemo } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>oot<PERSON>,
  CardContent,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useFetchCollections } from '@/features/workspace/api/useFetchCollections';
import LoadingSpinner from '@/components/LoadingSpinner';
import { PlusIcon, Users } from 'lucide-react';
import CollectionSheet from '@/features/workspace/components/collection/CollectionSheet';
import { ICollectionData } from '@/types/collection';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';

const CollectionListPage = () => {
  const { data: allCollections, isLoading: isLoadingAllCollections } =
    useFetchCollections();
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const [collectionToEdit, setCollectionToEdit] =
    useState<ICollectionData | null>(null);
  const [showPersonalCollections, setShowPersonalCollections] = useState(false);

  // Filter out collections with ONLY personal groups unless showPersonalCollections is true
  const filteredCollections = useMemo(() => {
    if (!allCollections) return [];

    return allCollections.filter((collection) => {
      // If there are no groups, show the collection
      if (!collection.groups || collection.groups.length === 0) return true;

      // Count personal and non-personal groups
      let personalGroupCount = 0;
      let nonPersonalGroupCount = 0;

      collection.groups.forEach((group) => {
        let isPersonal = false;

        // Handle both string and object meta formats
        if (typeof group.meta === 'string') {
          try {
            const metaObj = JSON.parse(group.meta);
            isPersonal = metaObj.personal === true;
          } catch (e) {
            isPersonal = false;
          }
        } else if (group.meta && typeof group.meta === 'object') {
          isPersonal = group.meta.personal === true;
        }

        if (isPersonal) {
          personalGroupCount++;
        } else {
          nonPersonalGroupCount++;
        }
      });

      // If showPersonalCollections is true, show all collections
      if (showPersonalCollections) return true;

      // If showPersonalCollections is false, hide collections that ONLY have personal groups
      // (i.e., has at least one personal group AND no non-personal groups)
      return !(personalGroupCount > 0 && nonPersonalGroupCount === 0);
    });
  }, [allCollections, showPersonalCollections]);

  if (isLoadingAllCollections) {
    return (
      <div className="flex items-center p-8">
        <LoadingSpinner />
      </div>
    );
  }

  const handleCreate = () => {
    setCollectionToEdit(null);
    setIsSheetOpen(true);
  };

  const handleEdit = (collection: ICollectionData) => {
    setCollectionToEdit(collection);
    setIsSheetOpen(true);
  };

  const togglePersonalCollections = () => {
    setShowPersonalCollections((prev) => !prev);
  };

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="flex items-center space-x-2">
            <Label
              htmlFor="show-personal-collections"
              className="cursor-pointer"
            >
              <div className="flex items-center gap-1">
                <span>Show Personal-Only Collections</span>
              </div>
            </Label>
            <Switch
              id="show-personal-collections"
              checked={showPersonalCollections}
              onCheckedChange={togglePersonalCollections}
            />
          </div>
        </div>

        <Button
          variant="ghost"
          onClick={handleCreate}
          size="sm"
          className="ml-auto"
        >
          <PlusIcon size={16} />
          <span className="hidden sm:block">Collection</span>
        </Button>
      </div>

      <div className="grid w-full gap-4 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5">
        {filteredCollections.map((collection) => (
          <TooltipProvider key={collection.id}>
            <Card className="group relative gap-2 rounded-md py-4 shadow-none transition-shadow hover:shadow">
              <CardHeader className="px-4 pt-0 pb-2">
                {collection.description ? (
                  <Tooltip delayDuration={0}>
                    <TooltipTrigger asChild>
                      <CardTitle className="text-base font-bold">
                        {collection.name}
                      </CardTitle>
                    </TooltipTrigger>
                    <TooltipContent className="max-w-xs">
                      <p className="text-sm">{collection.description}</p>
                    </TooltipContent>
                  </Tooltip>
                ) : (
                  <CardTitle className="text-base font-bold">
                    {collection.name}
                  </CardTitle>
                )}
              </CardHeader>
              <CardContent className="flex flex-1 flex-col gap-4 px-4">
                {collection.groups && collection.groups.length > 0 && (
                  <div className="flex flex-wrap gap-1.5">
                    <Tooltip delayDuration={0}>
                      <TooltipTrigger asChild>
                        <Badge
                          variant="outline"
                          className="flex items-center gap-2 bg-slate-50/10 text-xs"
                        >
                          <Users size={10} />
                          {collection.groups.length === 1
                            ? collection.groups[0].name
                            : `${collection.groups.length} Groups`}
                        </Badge>
                      </TooltipTrigger>
                      <TooltipContent className="max-w-xs">
                        <div className="space-y-1">
                          <p className="text-xs font-medium">Groups:</p>
                          {collection.groups.map((group) => (
                            <div
                              key={group.id}
                              className="flex items-center gap-2"
                            >
                              <Users size={10} />
                              <span className="text-xs">{group.name}</span>
                            </div>
                          ))}
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                )}
              </CardContent>
              <CardFooter className="flex h-[30px] items-center justify-between px-4 py-4">
                <span className="text-muted-foreground text-xs">
                  {collection.owner_name || 'Anonymous'}
                </span>
                <Button
                  className="block md:hidden md:group-hover:block"
                  variant="outline"
                  size="sm"
                  onClick={() => handleEdit(collection)}
                >
                  Edit
                </Button>
              </CardFooter>
            </Card>
          </TooltipProvider>
        ))}

        <Card
          className="hover:bg-foreground/5 border-foreground-200 cursor-pointer rounded-md border-dashed shadow-none"
          onClick={handleCreate}
        >
          <CardContent className="flex h-full items-center justify-center">
            <PlusIcon size={24} />
          </CardContent>
        </Card>

        {isSheetOpen && (
          <CollectionSheet
            isOpen={isSheetOpen}
            onOpenChange={setIsSheetOpen}
            collection={collectionToEdit}
          />
        )}
      </div>
    </div>
  );
};

export default CollectionListPage;
