import { NextRequest, NextResponse } from 'next/server';
import axiosInstanceApi from '@/lib/axiosInstance.api';
import { API_CONFIG } from '@/config/api';
import { ICollectionCreate, ICollectionData } from '@/types/collection'; // Use Collection types

// GET handler for fetching a specific collection
export async function GET(
  request: NextRequest,
  props: { params: Promise<{ collectionId: string }> }
) {
  const authorization = request.headers.get('Authorization');
  const { collectionId } = await props.params;

  if (!collectionId) {
    return NextResponse.json(
      { message: 'Collection ID is required' },
      { status: 400 }
    );
  }

  const targetPath = `${API_CONFIG.workspace.collection}/${collectionId}`;

  try {
    console.log(
      `[API Route Proxy] Forwarding GET ${targetPath} with Auth: ${!!authorization}`
    );
    const apiResponse = await axiosInstanceApi.get<ICollectionData>(
      targetPath,
      {
        headers: { ...(authorization && { Authorization: authorization }) },
      }
    );

    if (apiResponse.data.documents) {
      // Sort documents by name
      apiResponse.data.documents.sort((a, b) =>
        a.metadata.name.localeCompare(b.metadata.name)
      );
    }

    return NextResponse.json(apiResponse.data, { status: apiResponse.status });
  } catch (error: any) {
    console.error(
      `[API Route Proxy Error] GET ${targetPath}:`,
      error.response?.data || error.message
    );
    const status = error.response?.status || 500;
    const message =
      error.response?.data?.message || 'Proxy error fetching collection';
    return NextResponse.json({ message }, { status });
  }
}

// PUT/PATCH handler for updating a specific collection
// Using PUT here, adjust to PATCH if your API uses it
export async function PATCH(
  request: NextRequest,
  props: { params: Promise<{ collectionId: string }> }
) {
  const authorization = request.headers.get('Authorization');
  const { collectionId } = await props.params;

  if (!collectionId) {
    return NextResponse.json(
      { message: 'Collection ID is required for update' },
      { status: 400 }
    );
  }

  const targetPath = `${API_CONFIG.workspace.collection}/${collectionId}`;

  try {
    const body: ICollectionCreate = await request.json(); // Assume payload matches create structure

    // Basic validation (optional, depends on API needs)
    if (!body.name || typeof body.name !== 'string' || !body.name.trim()) {
      return NextResponse.json(
        { message: 'Name is required' },
        { status: 400 }
      );
    }

    console.log(
      `[API Route Proxy] Forwarding PATCH ${targetPath} with Auth: ${!!authorization}`
    );
    const apiResponse = await axiosInstanceApi.patch(targetPath, body, {
      headers: {
        'Content-Type': 'application/json',
        ...(authorization && { Authorization: authorization }),
      },
    });
    return NextResponse.json(apiResponse.data, { status: apiResponse.status });
  } catch (error: any) {
    if (error instanceof SyntaxError) {
      return NextResponse.json(
        { message: 'Invalid JSON payload' },
        { status: 400 }
      );
    }
    console.error(
      `[API Route Proxy Error] PATCH ${targetPath}:`,
      error.response?.data || error.message
    );
    const status = error.response?.status || 500;
    const message =
      error.response?.data?.message || 'Proxy error updating collection';
    return NextResponse.json({ message }, { status });
  }
}

// Optional: DELETE handler
export async function DELETE(
  request: NextRequest,
  props: { params: Promise<{ collectionId: string }> }
) {
  const authorization = request.headers.get('Authorization');
  const { collectionId } = await props.params;

  if (!collectionId) {
    return NextResponse.json(
      { message: 'Collection ID is required for deletion' },
      { status: 400 }
    );
  }

  const targetPath = `${API_CONFIG.workspace.collection}/${collectionId}`;

  try {
    console.log(
      `[API Route Proxy] Forwarding DELETE ${targetPath} with Auth: ${!!authorization}`
    );
    const apiResponse = await axiosInstanceApi.delete(targetPath, {
      headers: { ...(authorization && { Authorization: authorization }) },
    });
    return NextResponse.json(apiResponse.data, { status: apiResponse.status });
  } catch (error: any) {
    console.error(
      `[API Route Proxy Error] DELETE ${targetPath}:`,
      error.response?.data || error.message
    );
    const status = error.response?.status || 500;
    const message =
      error.response?.data?.message || 'Proxy error deleting collection';
    return NextResponse.json({ message }, { status });
  }
}
