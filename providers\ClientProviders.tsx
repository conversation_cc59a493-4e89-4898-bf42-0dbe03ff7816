'use client'; // This marks the component as a Client Component

import React from 'react';
import { QueryClientProvider } from '@tanstack/react-query';
import { GlobalContextProvider } from '@/contexts/GlobalContext';
import { UserDataProvider } from '@/features/auth/components/UserDataProvider';
import Ms<PERSON><PERSON>ootstrap from './MsalProvider';
import { queryClient } from '@/lib/queryClient'; // Adjust path if needed

// This component will wrap all client-side providers and guards
export default function ClientProviders({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <MsalBootstrap>
      <GlobalContextProvider>
        <QueryClientProvider client={queryClient}>
          <UserDataProvider>{children}</UserDataProvider>
        </QueryClientProvider>
      </GlobalContextProvider>
    </MsalBootstrap>
  );
}
