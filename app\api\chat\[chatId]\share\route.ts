import { NextRequest, NextResponse } from 'next/server';
import axiosInstanceApi from '@/lib/axiosInstance.api';

/**
 * GET handler for creating a shared link for a chat
 * This endpoint proxies the request to the backend API
 */
export async function GET(
  request: NextRequest,
  props: { params: Promise<{ chatId: string }> }
) {
  try {
    const { chatId } = await props.params;

    if (!chatId) {
      return NextResponse.json(
        { message: 'Chat ID is required' },
        { status: 400 }
      );
    }

    // Extract Authorization header from the incoming request
    const authorization = request.headers.get('Authorization');

    console.log(
      `[API Route Proxy] Forwarding GET /chat/${chatId}/share with Auth: ${!!authorization}`
    );

    // Forward the request to the backend API
    const apiResponse = await axiosInstanceApi.get(`/chat/${chatId}/share`, {
      headers: {
        'Content-Type': 'application/json',
        ...(authorization && { Authorization: authorization }),
      },
    });

    // Return the response from the backend service
    return NextResponse.json(apiResponse.data, { status: apiResponse.status });
  } catch (error: any) {
    // Log the error for debugging
    console.error(
      `[API Route Proxy Error] GET /chat/${props.params.then((p) => p.chatId)}/share:`,
      error.response?.data || error.message
    );

    // Handle 404 errors specifically
    if (error.response?.status === 404) {
      return NextResponse.json({ message: 'Chat not found' }, { status: 404 });
    }

    // Handle other errors
    const status = error.response?.status || 500;
    const message =
      error.response?.data?.message || 'Failed to create shared link';
    return NextResponse.json({ message }, { status });
  }
}
