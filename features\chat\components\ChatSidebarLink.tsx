'use client';

import Link from 'next/link';
import { useSidebar } from '@/contexts/SidebarContext';
import { ReactNode } from 'react';

interface ChatSidebarLinkProps {
  href: string;
  className?: string;
  title?: string;
  children: ReactNode;
  onClick?: (e: React.MouseEvent) => void;
}

export default function ChatSidebarLink({
  href,
  className,
  title,
  children,
  onClick,
}: ChatSidebarLinkProps) {
  const { store: sidebarStore } = useSidebar();

  const handleClick = (e: React.MouseEvent) => {
    // Call the original onClick if provided
    if (onClick) {
      onClick(e);
    }
    
    // Only close the sidebar on mobile
    if (sidebarStore.isMobile) {
      sidebarStore.closeSidebar();
    }
  };

  return (
    <Link href={href} className={className} title={title} onClick={handleClick}>
      {children}
    </Link>
  );
}
