import { Renderer } from 'marked';

/**
 * Detects and processes Chart.js configurations in code blocks
 *
 * @param code - The code content from the markdown
 * @param lang - The language identifier of the code block
 * @returns HTML string with Chart.js container if applicable, null otherwise
 */
export function processChartJsCodeBlock(
  code: string,
  lang?: string
): string | null {
  // Detect JSON chart configs, being more permissive with the lang parameter
  if (
    (lang === 'json' || !lang) &&
    code.includes('"type"') &&
    (code.includes('"bar"') ||
      code.includes('"line"') ||
      code.includes('"pie"') ||
      code.includes('"scatter"') ||
      code.includes('"bubble"') ||
      code.includes('"polarArea"') ||
      code.includes('"radar"') ||
      code.includes('"doughnut"'))
  ) {
    try {
      // Attempt to parse the JSON
      const config = JSON.parse(code);

      // Validate the config has minimum required properties
      if (config.type && config.data && config.data.datasets) {
        return `<div class="chart-js chart-hidden" data-config="${encodeURIComponent(code)}"></div>`;
      }
    } catch (e) {
      console.error('Invalid JSON for chart:', e);

      // If this looks like chart JSON but can't be parsed properly,
      // return an error indicator that can be seen by users
      if (
        code.includes('"type"') &&
        code.includes('"data"') &&
        code.includes('"datasets"')
      ) {
        return `<div class="chart-loading flex items-center justify-center p-4 text-sm text-gray-500 dark:text-white"><div class="text-red-500">Invalid chart configuration: JSON parsing error</div></div>`;
      }
    }
  }

  return null;
}

/**
 * Extends a marked.js renderer to handle Chart.js code blocks
 *
 * @param renderer - The marked.js renderer to extend
 */
export function extendRendererWithChartJs(renderer: Renderer): void {
  const originalCode = renderer.code;

  // @ts-ignore - The type definition in marked.js doesn't match the actual implementation
  renderer.code = function (options: {
    text: string;
    lang?: string;
    escaped?: boolean;
  }) {
    const { text: code, lang } = options;

    const chartHtml = processChartJsCodeBlock(code, lang);
    if (chartHtml) {
      return chartHtml;
    }

    // @ts-ignore - Pass through to the original method
    return originalCode.call(this, options);
  };
}
