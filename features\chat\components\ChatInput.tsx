'use client';

import React, { useState, useRef, useEffect } from 'react';
import STTInput from '@/features/stt/components/STTInput';
import { Globe, ArrowUp, X, Square, Loader2, FileIcon } from 'lucide-react';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { useChuckContext } from '@/contexts/ChuckContext';
import { IMessage, IMessageAttachment } from '@/types/chat';
import { isEmpty } from 'lodash-es';
import { getNewChatInputMessage } from './helpers/getNewChatInputMessage';
import Image from 'next/image';
import ChatDropzone from './ChatDropzone';
import { v4 as uuidv4 } from 'uuid';
import { useFetchModelById } from '@/features/workspace/api/useFetchModelById';
import { useAutoResizeTextarea } from '@/hooks/useAutoResizeTextarea';

import { cn } from '@/lib/utils';
import { UI_CONFIG } from '@/config/ui';

interface IChatInputProps {
  onSend: (message: IMessage) => void;
  onCancel?: () => void;
  value?: IMessage;
  isLoading?: boolean;
  disabled?: boolean;
  textDisabled?: boolean;
  uploading?: boolean;
  withImagePaste?: boolean;
  className?: string;
}

const ChatInput = ({
  onSend,
  onCancel,
  value = getNewChatInputMessage(),
  isLoading = false,
  disabled = false,
  uploading = false,
  textDisabled = false,
  withImagePaste = false,
  className,
}: IChatInputProps) => {
  const {
    store: { modelId },
  } = useChuckContext();
  const [useWebSearch, setUseWebSearch] = useState(false);
  const [message, setMessage] = useState<IMessage>(value);
  const textAreaRef = useRef<HTMLTextAreaElement>(null);

  // Fetch model information to check if it's the Chuck model
  const { data: modelData } = useFetchModelById(modelId);

  // Track if user has manually changed the useWebSearch setting
  const [userChangedWebSearch, setUserChangedWebSearch] = useState(false);

  // Check if the current model is the Chuck model and set useWebSearch accordingly
  useEffect(() => {
    if (modelData && !userChangedWebSearch) {
      const isChuck = modelData.name
        .toLowerCase()
        .includes(UI_CONFIG.KEYS.CHUCK.name);
      // Set web search based on model type, but only if user hasn't manually changed it
      setUseWebSearch(isChuck);
    }
  }, [modelData, userChangedWebSearch]);

  // Use the auto-resize hook to handle textarea resizing
  useAutoResizeTextarea(textAreaRef, message.content);

  // Handle input changes
  useEffect(() => {
    if (!textAreaRef.current) return;

    const handleInput = () => {
      setMessage((prev) => ({
        ...prev,
        content: textAreaRef.current?.value || '',
      }));
    };

    // Listen for input events directly
    textAreaRef.current.addEventListener('input', handleInput);

    return () => {
      textAreaRef.current?.removeEventListener('input', handleInput);
    };
  }, []);

  const handleAddAttachments = (attachments: IMessageAttachment[]) => {
    setMessage((prevMessage) => ({
      ...prevMessage,
      attachments: [...prevMessage.attachments, ...attachments],
    }));
  };

  const handleSend = async () => {
    const trimmedContent = textAreaRef.current?.value.trim() || '';
    if (isEmpty(trimmedContent) && isEmpty(message.attachments)) {
      return;
    }

    message.content = trimmedContent;
    message.config = {
      modelId,
      webSearch: useWebSearch,
    };

    onSend(message);
    textAreaRef.current!.value = '';
    setMessage(getNewChatInputMessage());
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (disabled) return;
    if (event.key === 'Enter') {
      if (event.shiftKey) {
        return;
      } else {
        if (isLoading) {
          return;
        }
        event.preventDefault();
        handleSend();
      }
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    const newAttachments: IMessageAttachment[] = [];
    const items = e.clipboardData.items;
    for (let i = 0; i < items.length; i++) {
      const file = items[i].getAsFile();
      if (file) {
        const attachment: IMessageAttachment = {
          name: file.name,
          type: file.type,
          size: file.size,
          file: file,
          id: uuidv4(),
        };
        if (attachment.type.startsWith('image/')) {
          const reader = new FileReader();
          reader.onload = (event) => {
            const base64Content = event.target?.result;
            attachment.content = base64Content as string;
          };
          reader.readAsDataURL(file);
        }

        newAttachments.push(attachment);
      }
    }

    setMessage((prevMessage) => ({
      ...prevMessage,
      attachments: [...prevMessage.attachments, ...newAttachments],
    }));
  };

  const removeAttachment = (index: number) => {
    const newAttachments = [...message.attachments];
    newAttachments.splice(index, 1);
    setMessage({
      ...message,
      attachments: newAttachments,
    });
  };

  // Helper to render appropriate icon for file type
  const renderAttachmentIcon = (attachment: IMessageAttachment) => {
    switch (attachment.type) {
      default:
        return <FileIcon className="h-8 w-8" />;
    }
  };

  // Helper to get preview URL for images
  const getImagePreviewUrl = (attachment: IMessageAttachment): string => {
    if (attachment.file instanceof File) {
      return URL.createObjectURL(attachment.file);
    }
    return '';
  };

  return (
    <>
      <div
        className={cn(
          'bg-background flex w-full flex-col justify-center rounded-3xl border p-4 shadow-xl',
          className
        )}
      >
        <div className="flex w-full flex-col gap-2">
          {message.attachments.length > 0 && (
            <div className="flex items-center gap-2 overflow-auto">
              {message.attachments.map((attachment, index) => (
                <div
                  className="group relative flex h-18 w-18 items-center justify-center rounded-md border p-1"
                  key={index}
                >
                  {attachment.type.startsWith('image/') ? (
                    <div className="relative h-full w-full">
                      <Image
                        className="rounded-md object-cover"
                        src={getImagePreviewUrl(attachment)}
                        alt={attachment.name || `Attachment ${index + 1}`}
                        fill
                        unoptimized
                      />
                    </div>
                  ) : (
                    <div className="flex flex-col items-center gap-1">
                      <span>{renderAttachmentIcon(attachment)}</span>
                      <span className="max-w-[70px] truncate px-1 text-xs">
                        {attachment.name || `File ${index + 1}`}
                      </span>
                    </div>
                  )}
                  {!uploading && (
                    <Button
                      className="size-0.3 bg-background absolute top-[4px] right-[4px] z-[1] opacity-0 transition-opacity duration-300 group-hover:opacity-100"
                      variant="ghost"
                      size="icon"
                      disabled={uploading}
                      onClick={() => removeAttachment(index)}
                    >
                      <X />
                    </Button>
                  )}

                  {uploading && (
                    <div className="bg-background/80 absolute top-0 left-0 flex h-full w-full items-center justify-center">
                      <Loader2 className="absolute animate-spin" />
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
          <Textarea
            ref={textAreaRef}
            onKeyDown={handleKeyDown}
            onPaste={handlePaste}
            onChange={() => {
              setMessage({
                ...message,
                content: textAreaRef.current?.value || '',
              });
            }}
            placeholder="Send a message... (Shift + Enter for newline)"
            className="max-h-[150px] flex-1 resize-none overflow-y-auto rounded-xl p-2 break-words md:text-base"
            autoComplete="off"
            autoFocus
            disabled={textDisabled}
            rows={1}
            style={{ minHeight: '42px' }} // Ensure consistent initial height across browsers
          />
          <div className="flex w-full items-center justify-between">
            <div className="flex w-full items-center gap-2">
              <Button
                className="cursor-pointer"
                variant={useWebSearch ? 'default' : 'outline'}
                onClick={() => {
                  setUseWebSearch(!useWebSearch);
                  setUserChangedWebSearch(true); // Mark that user has manually changed this setting
                }}
              >
                <Globe className="h-4 w-4" />
                <span>Search</span>
              </Button>
              <STTInput
                textAreaRef={
                  textAreaRef as React.RefObject<HTMLTextAreaElement>
                }
              />
            </div>
            <div className="flex items-center gap-2">
              {uploading && (
                <div className="text-muted-foreground animate-pulse text-sm">
                  Uploading...
                </div>
              )}
              <div className="relative">
                {isLoading && (
                  <Loader2
                    size="55"
                    className="pointer-events-none absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 animate-spin"
                    strokeWidth={0.5}
                  />
                )}
                <Button
                  type="button"
                  onClick={isLoading ? onCancel : handleSend}
                  disabled={
                    (!isLoading &&
                      !message.content.trim() &&
                      message.attachments.length === 0) ||
                    disabled
                  }
                  size="icon"
                  className={`flex-shrink-0 cursor-pointer rounded-full`}
                >
                  {isLoading ? (
                    <Square className="h-4 w-4" />
                  ) : (
                    <ArrowUp className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <ChatDropzone onFilesAdded={handleAddAttachments} fullScreen={true} />
    </>
  );
};

export default ChatInput;
