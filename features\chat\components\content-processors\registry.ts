import {
  ContentProcessor,
  ContentProcessorContext,
  ContentProcessorRegistry,
  ProcessedContent,
} from './types';

export class DefaultContentProcessorRegistry
  implements ContentProcessorRegistry
{
  private processors: ContentProcessor[] = [];

  register(processor: ContentProcessor): void {
    this.processors.push(processor);
  }

  process(ctx: ContentProcessorContext): ProcessedContent {
    const defaultResult: ProcessedContent = {
      html: `<pre><code class="language-${ctx.lang || ''}">${ctx.code}</code></pre>`,
    };

    for (const processor of this.processors) {
      if (processor.canProcess(ctx)) {
        const result = processor.process(ctx);
        return result;
      }
    }

    return defaultResult;
  }
}

export const contentProcessorRegistry = new DefaultContentProcessorRegistry();
