import { useMutation, useQueryClient } from '@tanstack/react-query';
import axiosInstanceUi from '@/lib/axiosInstance.ui';
import { API_CONFIG } from '@/config/api';
import { IToolData } from '@/types/model';

// Define update payload interface
export interface IToolUpdatePayload {
  name: string;
  description: string;
}

// Function to update a tool
const updateTool = async ({
  toolId,
  payload,
}: {
  toolId: string;
  payload: IToolUpdatePayload;
}): Promise<IToolData> => {
  const response = await axiosInstanceUi.patch(
    `${API_CONFIG.chat.tool}/${toolId}`,
    payload
  );
  return response.data;
};

// Hook for updating a tool
export function useUpdateTool({
  onSuccessCallback,
}: {
  onSuccessCallback?: () => void;
} = {}) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateTool,
    onSuccess: () => {
      // Invalidate and refetch tools
      queryClient.invalidateQueries({ queryKey: [API_CONFIG.chat.tool] });
      if (onSuccessCallback) {
        onSuccessCallback();
      }
    },
  });
}
