import { useQuery, Query<PERSON><PERSON> } from '@tanstack/react-query';
import axiosInstanceUi from '@/lib/axiosInstance.ui';
import { API_CONFIG } from '@/config/api';
import { IGroupData } from '@/types/group';

// Function to fetch a single group by its ID
export const fetchGroupById = async (groupId: string): Promise<IGroupData> => {
  const response = await axiosInstanceUi.get<IGroupData>(
    // Use the client-side proxy route
    `${API_CONFIG.admin.group}/${groupId}`
  );

  if (!response.data) {
    throw new Error('Group not found or empty response');
  }

  // Transform the data to include userIds if it has users array
  const data = response.data;

  // If the API returns users array but not userIds, extract the IDs
  if (data.users && !data.userIds) {
    data.userIds = data.users.map((user) => user.id);
  }

  return data;
};

// Define the query key structure
const groupDetailQueryKey = (groupId: string): QueryKey => [
  API_CONFIG.admin.group,
  groupId,
];

export function useFetchGroupById(
  groupId: string | null | undefined,
  options?: {
    enabled?: boolean;
    staleTime?: number;
    gcTime?: number;
    refetchOnMount?: boolean;
    refetchOnWindowFocus?: boolean;
    refetchOnReconnect?: boolean;
  }
) {
  return useQuery<IGroupData, Error>({
    queryKey: groupDetailQueryKey(groupId ?? ''),
    queryFn: ({ queryKey }) => {
      const id = queryKey[1] as string;
      if (!id) {
        return Promise.reject(new Error('Group ID is required for queryFn'));
      }
      return fetchGroupById(id);
    },
    // Query is only enabled if groupId is truthy
    enabled: !!groupId && options?.enabled !== false,
    // Apply default caching behavior or passed options
    staleTime: options?.staleTime ?? 5 * 60 * 1000, // Default 5 mins
    gcTime: options?.gcTime ?? 10 * 60 * 1000, // Default 10 mins
    refetchOnMount: options?.refetchOnMount ?? true,
    refetchOnWindowFocus: options?.refetchOnWindowFocus ?? true,
    refetchOnReconnect: options?.refetchOnReconnect ?? true,
  });
}
