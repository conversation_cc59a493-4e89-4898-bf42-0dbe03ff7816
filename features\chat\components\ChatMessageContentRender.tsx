'use client';

import { cn } from '@/lib/utils';
import { MessageRoleEnum, IMessage, IArtifact } from '@/types/chat';
import { isEmpty, last } from 'lodash-es';
import { File } from 'lucide-react';
import { marked } from 'marked';
import { useTheme } from 'next-themes';
import Image from 'next/image';
import DOMPurify from 'dompurify';
import 'highlight.js/styles/github-dark.css';
import { useEffect, useRef, useState } from 'react';
import { VisuallyHidden } from '@/components/ui/visually-hidden';
import { renderCharts, Chart } from './renderers/chartjs';
import { renderMermaidDiagrams } from './renderers/mermaid/mermaid-processor';
import { setupNivoContentProcessor, processNivoCharts } from './renderers/nivo';

import {
  createCustomMarkedRenderer,
  enhanceHtmlContent,
} from './content-processors/marked-renderer';
import {
  createArtifactCollector,
  ArtifactCollector,
} from './content-processors/artifact-collector';

// Configure DOMPurify to make all links open in a new tab
DOMPurify.addHook('afterSanitizeAttributes', function (node) {
  // If the node is an anchor tag
  if (node.tagName === 'A') {
    // Set target and rel attributes for all links
    node.setAttribute('target', '_blank');
    node.setAttribute('rel', 'noopener noreferrer');
  }
});

/**
 * ChatMessageContentRender component
 *
 * This component renders the content of a chat message, including attachments,
 * markdown content, and artifacts. It also handles the display of HTML content
 * in an iframe when an artifact is selected.
 */
export default function ChatMessageContentRender({
  message,
  isStreaming,
}: {
  message: IMessage;
  isStreaming?: boolean;
}) {
  let innerContent = null;
  const isUser = message.role === MessageRoleEnum.USER;
  const { theme } = useTheme();
  const [selectedArtifact, setSelectedArtifact] = useState<IArtifact | null>(
    null
  );
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [iframeContent, setIframeContent] = useState<string>('');
  const chartInstancesRef = useRef<Map<string, Chart>>(new Map());
  const artifactCollector = useRef<ArtifactCollector>(
    createArtifactCollector()
  );

  useEffect(() => {
    if (selectedArtifact && selectedArtifact.type === 'html') {
      const htmlContent = `<!DOCTYPE html><html><head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <style>
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      line-height: 1.5;
      padding: 0rem;
      margin: 0;
      color: #333;
      background-color: white;
    }
    @media (prefers-color-scheme: dark) {
      body {
        color: #eee;
        background-color: #1a1a1a;
      }
    }
  </style>
</head>
<body>${selectedArtifact.content}</body></html>`;

      setIframeContent(htmlContent);
    }
  }, [selectedArtifact]);

  useEffect(() => {
    if (isStreaming) return;

    if (isSheetOpen && iframeRef.current && selectedArtifact) {
      // Set a small timeout to ensure the iframe is fully mounted
      const timeoutId = setTimeout(() => {
        const iframe = iframeRef.current;
        if (iframe && iframe.contentWindow) {
          try {
            // Create a blob URL from the HTML content
            const blob = new Blob([iframeContent], { type: 'text/html' });
            const url = URL.createObjectURL(blob);

            // Set the iframe src to the blob URL
            iframe.src = url;

            // Clean up the blob URL when the component unmounts
            return () => URL.revokeObjectURL(url);
          } catch (error) {
            console.error(
              'Error setting iframe content in ChatMessageContentRender:',
              error
            );
          }
        }
      }, 300);

      return () => clearTimeout(timeoutId);
    }
  }, [isSheetOpen, iframeContent, selectedArtifact, isStreaming]);

  if (message.role === MessageRoleEnum.USER) {
    innerContent = (
      <div className="flex flex-col items-end gap-2">
        {message.attachments && message.attachments.length > 0 && (
          <div className="flex gap-2">
            {message.attachments.map((attachment, index) => {
              if (attachment.type.startsWith('image/')) {
                return (
                  <div
                    key={`attachment-${index}`}
                    className="relative h-30 w-30 overflow-hidden"
                  >
                    <Image
                      className="rounded-md"
                      src={attachment.content || ''}
                      alt={`Attachment ${index + 1}`}
                      fill
                    />
                  </div>
                );
              }
              return (
                <div
                  className="bg-foreground/10 flex h-30 w-30 items-center justify-center overflow-hidden rounded-lg p-2"
                  key={`attachment-${index}`}
                >
                  <div className="flex flex-col gap-2">
                    <div className="flex justify-center">
                      <File size={30} />
                    </div>
                    <div className="line-clamp-2 text-center text-xs">
                      {attachment.name}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
        {!isEmpty(message.content) && (
          <div
            className={cn(
              'rounded-2xl p-2 px-3',
              isUser
                ? theme === 'dark'
                  ? 'bg-gray-800'
                  : 'bg-gray-100'
                : undefined
            )}
          >
            {message.content}
          </div>
        )}
      </div>
    );
  } else if (message.role === MessageRoleEnum.ASSISTANT) {
    // Reset the artifact collector
    artifactCollector.current.clear();

    // Create a custom renderer with the current theme and streaming state
    const customRenderer = createCustomMarkedRenderer({
      theme,
      isStreaming,
      artifactCollector: artifactCollector.current,
    });

    // Convert markdown to HTML
    let htmlContent = '';
    if (message.content) {
      htmlContent = marked.parse(message.content, {
        breaks: true,
        gfm: true,
        renderer: customRenderer,
      }) as string;
    }

    // Process HTML content for tables, inline code, etc.
    htmlContent = enhanceHtmlContent(htmlContent);

    // If we've collected artifacts during processing, add them to the message
    const extractedArtifacts = artifactCollector.current.getAll();
    if (extractedArtifacts.length > 0) {
      if (!message.artifacts) {
        message.artifacts = [];
      }
      message.artifacts.push(...extractedArtifacts);
    }

    const sanitizedHTML = DOMPurify.sanitize(htmlContent, {
      ADD_TAGS: ['nivochart', 'nivoChart', 'NIVOCHART'],
      ADD_ATTR: ['data-chart-json', 'class', 'id'],
      ALLOW_UNKNOWN_PROTOCOLS: true,
    });

    innerContent = (
      <div className="flex flex-col gap-4">
        <div
          className={cn('markdown')}
          dangerouslySetInnerHTML={{
            __html: sanitizedHTML,
          }}
        />
      </div>
    );
  }

  // Add event listener using event delegation
  useEffect(() => {
    if (isStreaming) return;

    if (
      !contentRef.current ||
      !message.artifacts ||
      message.artifacts.length === 0
    )
      return;

    const handleClick = (e: Event) => {
      const target = e.target as HTMLElement;
      // Check if the clicked element or any of its parents has the class 'render-html-button'
      const button = target.closest(
        '.render-html-button'
      ) as HTMLButtonElement | null;

      if (button) {
        e.preventDefault();
        e.stopPropagation();

        // Get the artifact index from the button's data attribute
        const artifactIndex = parseInt(
          button.getAttribute('data-artifact-index') || '0',
          10
        );

        // We've already checked that message.artifacts exists and is not empty at the top of useEffect
        const artifacts = message.artifacts!;

        if (artifactIndex < artifacts.length) {
          setSelectedArtifact(artifacts[artifactIndex]);
          setIsSheetOpen(true);
        } else {
          // Fallback to last artifact if index is out of range
          const lastArtifact = artifacts[artifacts.length - 1];
          setSelectedArtifact(lastArtifact);
          setIsSheetOpen(true);
        }
      }
    };

    // Add a single event listener to the parent element
    contentRef.current.addEventListener('click', handleClick);

    // Process any chart-js elements that might not have been properly initialized
    const processDirectChartElements = () => {
      if (!contentRef.current) return;

      // Find chart divs without data-config but with JSON content
      const unprocessedCharts = Array.from(
        contentRef.current.querySelectorAll('.chart-js:not([data-config])')
      );

      unprocessedCharts.forEach((chartDiv) => {
        try {
          const content = chartDiv.textContent || '';
          if (
            content &&
            content.includes('"type"') &&
            content.includes('"data"')
          ) {
            // Try to parse the content as JSON
            const config = JSON.parse(content);
            if (config.type && config.data && config.data.datasets) {
              // Clear the inner content and set as data-config
              chartDiv.setAttribute('data-config', encodeURIComponent(content));
              chartDiv.textContent = '';
              chartDiv.classList.add('chart-hidden');
            }
          }
        } catch (e) {
          console.error('Error processing direct chart element:', e);
        }
      });
    };

    // Run the chart processing
    processDirectChartElements();

    return () => {
      if (contentRef.current) {
        contentRef.current.removeEventListener('click', handleClick);
      }
    };
  }, [message.artifacts, contentRef, isStreaming]);

  // Add effect for mermaid initialization based on theme
  useEffect(() => {
    if (!contentRef.current || isStreaming) return;

    try {
      // Use the extracted mermaid renderer function
      return renderMermaidDiagrams({
        container: contentRef.current,
        theme,
        isStreaming,
      });
    } catch (err) {
      console.error('Error rendering mermaid diagrams:', err);
    }
  }, [contentRef, isStreaming, innerContent, theme]);

  // Effect to setup Nivo chart CSS
  useEffect(() => {
    setupNivoContentProcessor();
  }, []);

  // Effect to process and render Nivo charts
  useEffect(() => {
    if (!contentRef.current || isStreaming) return;

    return processNivoCharts({
      container: contentRef.current,
      theme: theme as 'light' | 'dark' | undefined,
      isStreaming,
      messageId: message.id,
    });
  }, [
    contentRef,
    isStreaming,
    innerContent,
    theme,
    message.id,
    message.content,
  ]);

  // Effect to process and render Chart.js charts
  useEffect(() => {
    if (!contentRef.current) return;

    // Use the renderCharts utility to handle chart rendering and cleanup
    return renderCharts({
      container: contentRef.current,
      theme,
      instanceRef: chartInstancesRef,
      isStreaming,
    });
  }, [isStreaming, innerContent, theme]);

  return (
    <div key={message.id}>
      <div className={cn('w-full max-w-[860px]')} ref={contentRef}>
        {innerContent}
      </div>

      {/* Backdrop overlay */}
      <div
        className={cn(
          'fixed inset-0 z-40 bg-black/50 transition-opacity duration-300',
          selectedArtifact && isSheetOpen
            ? 'opacity-100'
            : 'pointer-events-none opacity-0'
        )}
        onClick={() => setIsSheetOpen(false)}
        aria-hidden="true"
      />

      {/* Persistent right-side panel for HTML content */}
      <div
        className={cn(
          'bg-background fixed inset-y-0 right-0 z-50 flex w-1/2 min-w-[600px] transform flex-col shadow-lg transition-transform duration-300 ease-in-out',
          selectedArtifact && isSheetOpen ? 'translate-x-0' : 'translate-x-full'
        )}
        aria-labelledby="panel-title-content"
        aria-describedby="panel-description-content"
      >
        <button
          type="button"
          onClick={() => setIsSheetOpen(false)}
          className="bg-background/80 hover:bg-accent absolute top-[12px] right-[12px] flex h-8 w-8 cursor-pointer items-center justify-center rounded-full backdrop-blur"
          aria-label="Close"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="18"
            height="18"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M18 6 6 18"></path>
            <path d="m6 6 12 12"></path>
          </svg>
        </button>
        <VisuallyHidden>
          <p id="panel-description-content">
            Rendered HTML content from the chat
          </p>
        </VisuallyHidden>
        <div className="flex-1 overflow-hidden bg-white dark:bg-gray-900">
          {selectedArtifact && (
            <iframe
              key={`iframe-${selectedArtifact.content.substring(0, 20) || ''}`}
              ref={iframeRef}
              className="h-full w-full border-0"
              sandbox="allow-scripts allow-same-origin allow-popups allow-forms"
              title={selectedArtifact.title || 'HTML Content'}
              onLoad={() => console.log('Iframe loaded')}
            />
          )}
        </div>
      </div>
    </div>
  );
}
