import { useQuery } from '@tanstack/react-query';
import axiosInstanceUi from '@/lib/axiosInstance.ui';
import { API_CONFIG } from '@/config/api';
import { IDashboardData } from '@/types/dashboard';

// Function to fetch dashboard data
const fetchDashboardData = async (): Promise<IDashboardData> => {
  const response = await axiosInstanceUi.get(API_CONFIG.admin.dashboard);

  if (!response.data) {
    throw new Error('No dashboard data returned');
  }
  return response.data;
};

export function useFetchDashboard() {
  return useQuery<IDashboardData, Error>({
    queryKey: [API_CONFIG.admin.dashboard],
    queryFn: fetchDashboardData,
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
    staleTime: 2 * 60 * 1000, // Consider data stale after 2 minutes
  });
}
