/**
 * Formats a JSON object into a pretty-printed string with proper indentation
 * @param json The JSON object to format
 * @returns A formatted string representation of the JSON object
 */
export const formatJson = (json: any): string => {
  try {
    return JSON.stringify(json, null, 2);
  } catch (error) {
    console.error('Error formatting JSON:', error);
    return JSON.stringify(json) || '{}';
  }
};
