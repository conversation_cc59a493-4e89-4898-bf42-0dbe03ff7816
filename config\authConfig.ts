import { Configuration, LogLevel } from '@azure/msal-browser';

// Config object to be passed to <PERSON><PERSON> on creation
export const msalConfig: Configuration = {
  auth: {
    clientId: process.env.NEXT_PUBLIC_MSAL_CLIENT_ID || '', // Replace with your Client ID
    authority: process.env.NEXT_PUBLIC_MSAL_AUTHORITY || '', // Replace with your Tenant ID
    redirectUri:
      process.env.NEXT_PUBLIC_MSAL_REDIRECT_URI || 'http://localhost:8000', // Or your specific redirect URI
    postLogoutRedirectUri:
      process.env.NEXT_PUBLIC_MSAL_POST_LOGOUT_REDIRECT_URI ||
      'http://localhost:8000/login', // Optional: Where to redirect after logout
    navigateToLoginRequestUrl: false,
  },
  cache: {
    cacheLocation: 'localStorage', // Configures cache location. 'sessionStorage' or 'localStorage'
    storeAuthStateInCookie: false, // Set this to "true" if you are having issues on IE11 or Edge
  },
  system: {
    loggerOptions: {
      loggerCallback: (
        level: LogLevel,
        message: string,
        containsPii: boolean
      ) => {
        if (containsPii) {
          return;
        }
        switch (level) {
          case LogLevel.Error:
            console.error(message);
            return;
          case LogLevel.Info:
            // console.info(message);
            return;
          case LogLevel.Verbose:
            // console.debug(message);
            return;
          case LogLevel.Warning:
            // console.warn(message);
            return;
          default:
            return;
        }
      },
      logLevel: LogLevel.Error, // Adjust log level as needed for debugging
    },
  },
};

// Add scopes here for ID token to be used at MS Identity Platform endpoints.
export const loginRequest = {
  scopes: ['User.Read'], // Basic scope for reading user profile
};
