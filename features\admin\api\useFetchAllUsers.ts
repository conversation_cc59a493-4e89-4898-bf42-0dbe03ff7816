import { useQuery } from '@tanstack/react-query';
import axiosInstanceUi from '@/lib/axiosInstance.ui';
import { API_CONFIG } from '@/config/api';
import { IUserData } from '@/types/user';

// Function to fetch all users
const fetchAllUsers = async (): Promise<IUserData[]> => {
  const response = await axiosInstanceUi.get(API_CONFIG.admin.userList);

  if (!response.data) {
    return [];
  }
  return response.data;
};

export function useFetchAllUsers() {
  return useQuery<IUserData[], Error>({
    queryKey: [API_CONFIG.admin.userList],
    queryFn: fetchAllUsers,
  });
}
