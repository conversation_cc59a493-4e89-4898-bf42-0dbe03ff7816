'use client';

import Sidebar from '@/components/sidebar';
import MobileMenuButton from '@/components/MobileMenuButton';
import { cn } from '@/lib/utils';
import { useSidebar } from '@/contexts/SidebarContext';

export default function MainLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { store: sidebarStore } = useSidebar();

  const mainClassName = cn(
    'flex-1 transition-all duration-300 ease-in-out overflow-hidden w-screen',
    // On desktop: add left padding for the sidebar
    !sidebarStore.isMobile && 'pl-[60px]',
    // On mobile: add left padding only when sidebar is open
    sidebarStore.isMobile && sidebarStore.isOpen && 'pl-0'
  );

  return (
    <>
      <Sidebar />
      {sidebarStore.isMobile && !sidebarStore.isOpen && <MobileMenuButton />}
      {/* Overlay that appears behind the sidebar on mobile when it's open */}
      {sidebarStore.isMobile && sidebarStore.isOpen && (
        <div
          className="fixed inset-0 z-10 bg-black/50 transition-opacity duration-300 ease-in-out"
          onClick={sidebarStore.closeSidebar}
          aria-hidden="true"
        />
      )}
      <main className={mainClassName}>{children}</main>
    </>
  );
}
