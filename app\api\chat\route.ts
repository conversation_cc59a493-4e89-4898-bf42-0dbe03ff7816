import { NextRequest, NextResponse } from 'next/server';
import axiosInstanceApi from '@/lib/axiosInstance.api';
import { API_CONFIG } from '@/config/api';
import { groupChatsByDate } from '@/lib/utils/groupChatsByDate';

export async function GET(request: NextRequest) {
  // Extract Authorization header from the incoming request
  const authorization = request.headers.get('Authorization');

  // Determine the target path within the backend API
  // Here we assume /api/model should map to /model on the backend
  // Adjust if your paths differ
  const targetPath = API_CONFIG.chat.chat; // The path on your backend service

  // Forward query parameters
  const { searchParams } = new URL(request.url);

  try {
    console.log(
      `[API Route Proxy] Forwarding GET ${targetPath} with Auth: ${!!authorization}`
    );

    const apiResponse = await axiosInstanceApi.get(targetPath, {
      headers: {
        ...(authorization && { Authorization: authorization }),
      },
    });

    // Group chats by date categories
    const groupedChats = groupChatsByDate(apiResponse.data);

    // Return the grouped chats
    return NextResponse.json(groupedChats, { status: apiResponse.status });
  } catch (error: any) {
    console.error(
      `[API Route Proxy Error] GET ${targetPath}:`,
      error.response?.data || error.message
    );
    const status = error.response?.status || 500;
    const message = error.response?.data?.message || 'Proxy error';
    return NextResponse.json({ message }, { status });
  }
}

// You can add handlers for other methods (POST, PUT, DELETE) similarly
// export async function POST(request: NextRequest) { ... }
