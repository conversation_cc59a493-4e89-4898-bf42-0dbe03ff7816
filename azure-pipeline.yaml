variables:
  - ${{ if or(eq( variables['Build.SourceBranch'], 'refs/heads/develop' ), eq(variables['System.PullRequest.TargetBranch'], 'refs/heads/develop'), startsWith(variables['Build.SourceBranch'], 'refs/heads/dev-group/')) }}:
      - group: mawer-ai-dev
  - ${{ if or(eq(variables['System.PullRequest.TargetBranch'], 'refs/heads/main'), eq( variables['Build.SourceBranch'], 'refs/heads/main' )) }}:
      - group: mawer-ai-prd
  - name: isTargetDevelop
    value: $[eq(variables['System.PullRequest.TargetBranch'], 'refs/heads/develop')]
  - name: isDevelop
    value: $[eq(variables['Build.SourceBranch'], 'refs/heads/develop')]
  - name: isDevGroup
    value: $[startsWith(variables['Build.SourceBranch'], 'refs/heads/dev-group/')]
  - name: isRelease
    value: $[eq(variables['Build.SourceBranch'], 'refs/heads/release')]
  - name: isMain
    value: $[eq(variables['Build.SourceBranch'], 'refs/heads/main')]
  - name: imageRepository
    value: 'mawer-ai/chuck-studio'
  - name: imageFileName
    value: 'chuck-studio'
  - name: containerRegistryProd
    value: 'azprduwacrprd1'
  - name: containerRegistryDomain
    value: '.azurecr.io'
  - name: containerRegistry
    value: 'azdevuwacrdev1'
  - name: appDockerFilePath
    value: 'Dockerfile'
  - name: tag
    value: nightly

trigger:
  branches:
    include:
      - dev-group/*
      - develop
      - release
      - main
    exclude:
      - feature/*
  paths:
    exclude:
      - README.md

pool:
  vmImage: 'ubuntu-latest'

stages:
  - stage: SetVersion
    displayName: Set Version
    jobs:
      - job: SetVersion
        steps:
          - script: |
              BRANCH_NAME=$(echo "$(Build.SourceBranch)" | awk -F/ '{print $NF}')
              npmVersionString=1.0.0
              echo "##vso[build.updatebuildnumber]$npmVersionString-$BRANCH_NAME.$(Build.BuildNumber).$(Build.BuildId)"
              echo "$npmVersionString-$BRANCH_NAME.$(Build.BuildNumber).$(Build.BuildId)"
            displayName: 'Set Version Number'

  - stage: Test
    displayName: Run Tests
    dependsOn: SetVersion
    jobs:
      - job: RunTests
        displayName: 'Run Unit Tests'
        steps:
          - task: NodeTool@0
            inputs:
              versionSpec: '22.x'
            displayName: 'Install Node.js'

          # Cache node_modules to speed up builds
          - task: Cache@2
            inputs:
              key: 'pnpm | "$(Agent.OS)" | pnpm-lock.yaml'
              path: $(Pipeline.Workspace)/.pnpm-store
            displayName: Cache pnpm store

          - script: |
              npm install -g pnpm
              pnpm config set store-dir $(Pipeline.Workspace)/.pnpm-store
              pnpm install
            displayName: 'Install dependencies'

          - script: |
              pnpm test:ci
            displayName: 'Run tests'

          - task: PublishTestResults@2
            inputs:
              testResultsFormat: 'JUnit'
              testResultsFiles: '**/test-results.xml'
              mergeTestResults: true
              testRunTitle: 'Vitest Tests'
            condition: succeededOrFailed()
            displayName: 'Publish test results'

  - stage: Build
    displayName: Build Docker Image
    dependsOn: Test
    condition: succeeded('Test')
    jobs:
      - job: BuildAndPushDockerImages
        displayName: 'Build and Push Docker Image'
        steps:
          - script: |
              npm version --no-git-tag-version "$(Build.BuildNumber)"
            displayName: 'Set version to package.json file'

          - script: echo '##vso[task.setvariable variable=tag]uat'
            displayName: 'set tag to uat if target branch is release'
            condition: eq(variables['Build.SourceBranch'], 'refs/heads/release')
          - script: echo '##vso[task.setvariable variable=tag]prd'
            displayName: 'set tag to prd if target branch is main'
            condition: eq(variables['Build.SourceBranch'], 'refs/heads/main')

          - script: echo '##vso[task.setvariable variable=containerRegistry]azdevuwacrdev1'
            condition: or(eq(variables.isTargetDevelop, true), eq(variables.isDevelop, true), eq(variables.isDevGroup, true), eq(variables.isTargetRelease, true), eq(variables.isRelease, true))
            displayName: 'set container registry to DEV'

          - script: echo '##vso[task.setvariable variable=containerRegistry]azprduwacrprd1'
            condition: or(eq(variables.isTargetMain, true), eq(variables.isMain, true))
            displayName: 'set container registry to PRD'

          - script: |
              echo "NEXT_PUBLIC_WS_SERVICE_URL=$(CHAT_SERVICE_ORIGIN_WS)" >> .env
              echo "NEXT_PUBLIC_MSAL_CLIENT_ID=$(MSAL_CLIENT_ID)" >> .env
              echo "NEXT_PUBLIC_MSAL_AUTHORITY=$(MSAL_AUTHORITY)" >> .env
              echo "NEXT_PUBLIC_MSAL_REDIRECT_URI=$(CHUCK_STUDIO_MSAL_REDIRECT_URI)" >> .env
              echo "NEXT_PUBLIC_MSAL_POST_LOGOUT_REDIRECT_URI=$(CHUCK_STUDIO_MSAL_POST_LOGOUT_REDIRECT_URI)" >> .env
              echo "NEXT_PUBLIC_SUPPORT_PLATFORM_URL=$(SUPPORT_PLATFORM_URL)" >> .env
              echo "NEXT_PUBLIC_RYBBIT_SCRIPT_URL=$(RYBBIT_SCRIPT_URL)" >> .env
              echo "NEXT_PUBLIC_RYBBIT_SITE_ID=$(RYBBIT_SITE_ID)" >> .env
            displayName: 'SET BUILD TIME ENV VARS'

          - task: Docker@2
            displayName: Build App image for Dev Registry
            condition: or(eq(variables.isTargetDevelop, true), eq(variables.isDevelop, true), eq(variables.isDevGroup, true), eq(variables.isTargetRelease, true), eq(variables.isRelease, true))
            inputs:
              command: build
              containerRegistry: azdevuwacrdev1
              repository: $(imageRepository)
              dockerfile: $(appDockerFilePath)
              arguments: '--progress=plain'
              tags: |
                $(Build.BuildNumber)
                $(tag)

          - task: Docker@2
            displayName: Push Image to Azure Container Registry
            condition: or(eq(variables.isTargetDevelop, true), eq(variables.isDevelop, true), eq(variables.isDevGroup, true), eq(variables.isTargetRelease, true), eq(variables.isRelease, true))
            inputs:
              containerRegistry: azdevuwacrdev1
              repository: $(imageRepository)
              command: 'push'
              tags: |
                $(Build.BuildNumber)
                $(tag)

          - task: Docker@2
            displayName: Build App image for Prd Registry
            condition: or(eq(variables.isTargetMain, true), eq(variables.isMain, true))
            inputs:
              command: build
              # containerRegistry: azprduwacrprd1
              containerRegistry: azdevuwacrdev1
              repository: $(imageRepository)
              dockerfile: $(appDockerFilePath)
              arguments: '--progress=plain'
              tags: |
                $(Build.BuildNumber)
                $(tag)

          - task: Docker@2
            displayName: Push Image to Azure Container Registry
            condition: or(eq(variables.isTargetMain, true), eq(variables.isMain, true))
            inputs:
              # containerRegistry: azprduwacrprd1
              containerRegistry: azdevuwacrdev1
              repository: $(imageRepository)
              command: 'push'
              tags: |
                $(Build.BuildNumber)
                $(tag)

          - task: Docker@2
            displayName: Build App image for Prd Registry
            condition: or(eq(variables.isTargetMain, true), eq(variables.isMain, true))
            inputs:
              command: build
              containerRegistry: azprduwacrprd1
              repository: $(imageRepository)
              dockerfile: $(appDockerFilePath)
              arguments: '--progress=plain'
              tags: |
                $(Build.BuildNumber)
                $(tag)

          - task: Docker@2
            displayName: Push Image to Azure Container Registry
            condition: or(eq(variables.isTargetMain, true), eq(variables.isMain, true))
            inputs:
              containerRegistry: azprduwacrprd1
              repository: $(imageRepository)
              command: 'push'
              tags: |
                $(Build.BuildNumber)
                $(tag)

  - stage: CopyManifestScript
    jobs:
      - job: CopyManifestScript
        steps:
          - task: Bash@3
            inputs:
              targetType: 'inline'
              script: |
                # Write your commands here
                cp infra/updateK8sManifest.sh  $(build.artifactstagingdirectory)/.
          - task: PublishPipelineArtifact@1
            displayName: 'Publishing Pipeline Artifact'
            inputs:
              path: $(build.artifactstagingdirectory)
              artifact: ManifestScript
    dependsOn:
      - Build
    condition: succeeded('Build')
