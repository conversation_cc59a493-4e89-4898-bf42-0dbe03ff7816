'use client';

import React from 'react';
import { cn } from '@/lib/utils';

// Basic placeholder loading spinner
export default function LoadingSpinner({
  fullScreen = false,
  size = 'md',
}: {
  fullScreen?: boolean;
  size?: 'sm' | 'md' | 'lg';
}) {
  const sizeClasses = {
    sm: 'h-4 w-4 border-2',
    md: 'h-6 w-6 border-4',
    lg: 'h-8 w-8 border-4',
  };

  return (
    <div
      className={cn(
        fullScreen && 'flex h-screen w-full items-center justify-center'
      )}
    >
      <div
        className={cn(
          'border-primary animate-spin rounded-full border-solid border-t-transparent',
          sizeClasses[size]
        )}
      ></div>
    </div>
  );
}
