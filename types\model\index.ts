import { IGroupDataBase } from '../group';

export enum ModelTypeEnum {
  MODEL = 'model',
  ASSISTANT = 'assistant',
}

export interface IToolData {
  id: string;
  name: string;
  description?: string;
  groups: IGroupDataBase[];
}

export interface IModelData {
  id: string;
  type: ModelTypeEnum;
  name: string;
  description: string;
  base_model: string;
  owner_id: string;
  owner_name: string;
  model?: string;
  image?: string;
  config: {
    prompt: string;
    prompt_suggestions?: string[];
  };
  collectionIds: string[];
  tools: IToolData[];
  groups: IGroupDataBase[];
}

export interface IModelGroup {
  name: string;
  type: ModelTypeEnum;
  data: IModelData[];
}

export type IGroupedModelData = IModelGroup[];

export interface IModelCreate {
  baseModel: string;
  collectionIds: string[];
  name: string;
  description: string;
  prompt: string;
  prompt_suggestions: string[];
  toolIds: string[];
  groupIds: string[];
  image?: string;
}

export interface IModelCreatePayload {
  groupIds: string[];
  name: string;
  description: string;
  baseModel: string;
  image?: string;
  config: {
    prompt: string;
    prompt_suggestions?: string[];
  };
  collectionIds: string[];
  toolIds: string[];
}

// Model versioning types
export interface IModelVersionSnapshot {
  id: string;
  name: string;
  type: ModelTypeEnum;
  image?: string;
  config: {
    prompt: string;
    prompt_suggestions?: string[];
  };
  owner_id: string;
  base_model: string;
  created_at: string;
  description: string;
  modified_at: string;
}

export interface IModelVersionChanges {
  [key: string]: any;
}

export interface IModelVersion {
  id: string;
  version_number: number;
  created_at: string;
  created_by_name: string;
  created_by_email: string;
  snapshot: IModelVersionSnapshot;
  changes: IModelVersionChanges;
  change_summary: string;
  version_created_at: string;
  created_by_user_id: string;
}

export interface IModelVersionsResponse {
  versions: IModelVersion[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}
