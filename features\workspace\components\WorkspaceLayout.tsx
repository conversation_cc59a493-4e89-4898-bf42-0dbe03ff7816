'use client';

import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

interface ILayoutProps {
  children: React.ReactNode;
}

export default function WorkspaceLayout({ children }: ILayoutProps) {
  const router = useRouter();
  const pathname = usePathname();

  // Determine initial tab value based on the current URL path
  const getTabValueFromPath = (path: string): string => {
    if (path.includes('/workspace/collection')) return 'collection';
    if (path.includes('/workspace/tool')) return 'tool';
    return 'model'; // Default to model tab
  };

  const [tabValue, setTabValue] = useState(getTabValueFromPath(pathname));

  // Update tab value when pathname changes
  useEffect(() => {
    setTabValue(getTabValueFromPath(pathname));
  }, [pathname]);

  const handleTabChange = (value: string) => {
    setTabValue(value);
    router.push(`/workspace/${value}`);
  };

  return (
    <div className="flex h-full w-full flex-col gap-4 p-4">
      <div className="flex justify-center sm:justify-between">
        <Tabs
          defaultValue={tabValue}
          value={tabValue}
          onValueChange={handleTabChange}
        >
          <TabsList>
            <TabsTrigger className="cursor-pointer" value="model">
              Agents
            </TabsTrigger>
            <TabsTrigger className="cursor-pointer" value="collection">
              Collections
            </TabsTrigger>
            <TabsTrigger className="cursor-pointer" value="tool">
              Tools
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>
      <div>{children}</div>
    </div>
  );
}
