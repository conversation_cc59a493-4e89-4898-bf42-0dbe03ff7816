'use client';

import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ontent,
  CardFooter,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { IGroupData } from '@/types/group';
import { Users } from 'lucide-react';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

interface GroupCardProps {
  group: IGroupData;
  onEdit: (group: IGroupData) => void;
}

export default function GroupCard({ group, onEdit }: GroupCardProps) {
  // Extend dayjs with relative time plugin
  dayjs.extend(relativeTime);

  // Format the date to show how long ago it was modified
  const getFormattedDate = (dateString: string) => {
    try {
      return dayjs(dateString).fromNow();
    } catch (error) {
      return 'Unknown date';
    }
  };

  return (
    <Card className="group relative gap-4 rounded-md shadow-none transition-shadow hover:shadow">
      <CardHeader className="px-4">
        <CardTitle className="font-bold">{group.name}</CardTitle>
      </CardHeader>
      <CardContent className="flex-1 px-4">
        {group.description && (
          <p className="text-muted-foreground line-clamp-2 break-words">
            {group.description}
          </p>
        )}
      </CardContent>
      <CardFooter className="flex h-[30px] items-center justify-between px-4">
        <div className="flex items-center gap-2">
          <Users size={20} className="text-primary" />
          <span className="text-muted-foreground">
            {group.users?.length || group.user_count || 0}
          </span>
        </div>
        <Button
          className="block md:hidden md:group-hover:block"
          variant="outline"
          size="sm"
          onClick={() => onEdit(group)}
        >
          Edit
        </Button>
      </CardFooter>
    </Card>
  );
}
