import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { IChat, IChatGroup } from '@/types/chat';

// Extend dayjs with plugins
dayjs.extend(utc);
dayjs.extend(timezone);

/**
 * Groups chat messages by time periods based on their timestamp
 * Pinned chats are placed in a separate "Pinned" group at the top
 * and do not appear in the date-based groups
 *
 * @param chats Array of chat objects with ts (timestamp) field
 * @returns Array of chat groups with name and data properties
 */
export function groupChatsByDate(chats: IChat[]): IChatGroup[] {
  // Initialize groups
  const groups: IChatGroup[] = [
    { name: 'Pinned', data: [] },
    { name: 'Today', data: [] },
    { name: 'Yesterday', data: [] },
    { name: 'Previous 7 Days', data: [] },
    { name: 'Previous 30 Days', data: [] },
    { name: 'Further History', data: [] },
  ];

  // Get user's local timezone
  const userTimezone = dayjs.tz.guess();

  // Get current date at the start of the day for comparison in user's timezone
  const now = dayjs().tz(userTimezone);
  const today = now.startOf('day');
  const yesterday = today.subtract(1, 'day');
  const previous7Days = today.subtract(7, 'day');
  const previous30Days = today.subtract(30, 'day');

  // Group chats by date
  chats.forEach((chat) => {
    // Convert timestamp to dayjs object in user's timezone
    const chatDate = dayjs.unix(chat.ts || 0).tz(userTimezone);

    // Add to Pinned group if the chat is pinned
    if (chat.pinned) {
      groups[0].data.unshift(chat);
      return; // Skip adding to date groups if pinned
    }

    // Determine which date-based group this chat belongs to
    // Only non-pinned chats will reach this point
    if (chatDate.isAfter(today) || chatDate.isSame(today, 'day')) {
      groups[1].data.unshift(chat);
    } else if (
      chatDate.isAfter(yesterday) ||
      chatDate.isSame(yesterday, 'day')
    ) {
      groups[2].data.unshift(chat);
    } else if (chatDate.isAfter(previous7Days)) {
      groups[3].data.unshift(chat);
    } else if (chatDate.isAfter(previous30Days)) {
      groups[4].data.unshift(chat);
    } else {
      groups[5].data.unshift(chat);
    }
  });

  // Filter out empty groups (optional)
  return groups.filter((group) => group.data.length > 0);
}
