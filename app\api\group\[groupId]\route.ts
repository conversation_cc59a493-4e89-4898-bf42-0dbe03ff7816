import { NextRequest, NextResponse } from 'next/server';
import axiosInstanceApi from '@/lib/axiosInstance.api';
import { API_CONFIG } from '@/config/api';

export async function GET(
  request: NextRequest,
  props: { params: Promise<{ groupId: string }> }
) {
  const authorization = request.headers.get('Authorization');
  const { groupId } = await props.params;

  if (!groupId) {
    return NextResponse.json(
      { message: 'Group ID is required' },
      { status: 400 }
    );
  }

  const targetPath = `${API_CONFIG.admin.group}/${groupId}`;

  try {
    console.log(
      `[API Route Proxy] Forwarding GET ${targetPath} with Auth: ${!!authorization}`
    );
    const apiResponse = await axiosInstanceApi.get(targetPath, {
      headers: {
        ...(authorization && { Authorization: authorization }),
      },
    });

    return NextResponse.json(apiResponse.data, { status: apiResponse.status });
  } catch (error: any) {
    console.error(
      `[API Route Proxy Error] GET ${targetPath}:`,
      error.response?.data || error.message
    );
    const status = error.response?.status || 500;
    const message = error.response?.data?.message || 'Proxy error fetching group';
    return NextResponse.json({ message }, { status });
  }
}

export async function PATCH(
  request: NextRequest,
  props: { params: Promise<{ groupId: string }> }
) {
  const authorization = request.headers.get('Authorization');
  const { groupId } = await props.params;

  if (!groupId) {
    return NextResponse.json(
      { message: 'Group ID is required for update' },
      { status: 400 }
    );
  }

  const targetPath = `${API_CONFIG.admin.group}/${groupId}`;

  try {
    const payload = await request.json();

    console.log(
      `[API Route Proxy] Forwarding PATCH ${targetPath} with Auth: ${!!authorization}`
    );
    const apiResponse = await axiosInstanceApi.patch(targetPath, payload, {
      headers: {
        'Content-Type': 'application/json',
        ...(authorization && { Authorization: authorization }),
      },
    });

    return NextResponse.json(apiResponse.data, { status: apiResponse.status });
  } catch (error: any) {
    console.error(
      `[API Route Proxy Error] PATCH ${targetPath}:`,
      error.response?.data || error.message
    );
    const status = error.response?.status || 500;
    const message = error.response?.data?.message || 'Proxy error updating group';
    return NextResponse.json({ message }, { status });
  }
}

export async function DELETE(
  request: NextRequest,
  props: { params: Promise<{ groupId: string }> }
) {
  const authorization = request.headers.get('Authorization');
  const { groupId } = await props.params;

  if (!groupId) {
    return NextResponse.json(
      { message: 'Group ID is required for deletion' },
      { status: 400 }
    );
  }

  const targetPath = `${API_CONFIG.admin.group}/${groupId}`;

  try {
    console.log(
      `[API Route Proxy] Forwarding DELETE ${targetPath} with Auth: ${!!authorization}`
    );
    const apiResponse = await axiosInstanceApi.delete(targetPath, {
      headers: { ...(authorization && { Authorization: authorization }) },
    });
    return NextResponse.json(apiResponse.data, { status: apiResponse.status });
  } catch (error: any) {
    console.error(
      `[API Route Proxy Error] DELETE ${targetPath}:`,
      error.response?.data || error.message
    );
    const status = error.response?.status || 500;
    const message = error.response?.data?.message || 'Proxy error deleting group';
    return NextResponse.json({ message }, { status });
  }
}
