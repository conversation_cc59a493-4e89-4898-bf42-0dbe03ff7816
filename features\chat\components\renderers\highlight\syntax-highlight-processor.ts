import {
  ContentProcessor,
  ContentProcessorContext,
  ProcessedContent,
} from '../../content-processors/types';
import hljs from 'highlight.js';

export class SyntaxHighlightProcessor implements ContentProcessor {
  /**
   * highlight.js doesn’t require theme-based initialization
   * because styles are applied via CSS already imported in the main component.
   *
   * Unlike Mermaid, which needs to be re-initialized on theme change,
   * highlight.js uses CSS classes, which are automatically styled
   * by the theme system (e.g., Next.js/Tailwind).
   */
  initialize(ctx: ContentProcessorContext): void {
    // No initialization needed for syntax highlighting
  }

  canProcess(ctx: ContentProcessorContext): boolean {
    return true;
  }

  process(ctx: ContentProcessorContext): ProcessedContent {
    let highlightedCode = ctx.code;
    const lang = ctx.lang?.trim() || '';

    if (lang) {
      try {
        highlightedCode = hljs.highlight(ctx.code, {
          language: lang,
          ignoreIllegals: true,
        }).value;
      } catch (error) {
        try {
          highlightedCode = hljs.highlightAuto(ctx.code).value;
        } catch (e) {
          console.error('Failed to highlight code:', e);
        }
      }
    } else {
      try {
        highlightedCode = hljs.highlightAuto(ctx.code).value;
      } catch (e) {
        console.error('Failed to auto-highlight code:', e);
      }
    }

    return {
      html: `<pre><code class="hljs${lang ? ` language-${lang}` : ''} copyable"><span class="code-content">${highlightedCode}</span></code></pre>`,
      skipNextProcessors: true,
    };
  }
}
