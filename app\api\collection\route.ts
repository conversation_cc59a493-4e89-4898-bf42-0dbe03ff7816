import { NextRequest, NextResponse } from 'next/server';
import axiosInstanceApi from '@/lib/axiosInstance.api';
import { API_CONFIG } from '@/config/api';
import { handleError } from '@/lib/server/handleError';
import { ICollectionCreate } from '@/types/collection';

export async function GET(request: NextRequest) {
  const authorization = request.headers.get('Authorization');
  const targetPath = API_CONFIG.workspace.collection;

  try {
    const apiResponse = await axiosInstanceApi.get(targetPath, {
      headers: {
        ...(authorization && { Authorization: authorization }),
      },
    });

    if (apiResponse.data) {
      // Sort collections by name
      apiResponse.data.sort((a: any, b: any) => a.name.localeCompare(b.name));
    }

    return NextResponse.json(apiResponse.data, { status: apiResponse.status });
  } catch (error: any) {
    return handleError(error, targetPath);
  }
}

export async function POST(request: NextRequest) {
  const authorization = request.headers.get('Authorization');
  const targetPath = API_CONFIG.workspace.collection;

  try {
    const body: ICollectionCreate = await request.json();

    // Basic validation
    if (!body.name || typeof body.name !== 'string' || !body.name.trim()) {
      return NextResponse.json(
        { message: 'Name is required' },
        { status: 400 }
      );
    }
    // Add description validation if needed
    // if (!body.description || typeof body.description !== 'string') { ... }

    console.log(
      `[API Route Proxy] Forwarding POST ${targetPath} with Auth: ${!!authorization}`
    );
    const apiResponse = await axiosInstanceApi.post(targetPath, body, {
      headers: {
        ...(authorization && { Authorization: authorization }),
      },
    });
    return NextResponse.json(apiResponse.data, { status: apiResponse.status });
  } catch (error: any) {
    if (error instanceof SyntaxError) {
      return NextResponse.json(
        { message: 'Invalid JSON payload' },
        { status: 400 }
      );
    }
    console.error(
      `[API Route Proxy Error] POST ${targetPath}:`,
      error.response?.data || error.message
    );
    const status = error.response?.status || 500;
    const message =
      error.response?.data?.message || 'Proxy error creating collection';
    return NextResponse.json({ message }, { status });
  }
}
