'use client';

import { useGlobal } from '@/contexts/GlobalContext';
import { Button } from '@/components/ui/button'; // Assuming you have a Button component
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { MsalProvider, useMsal } from '@azure/msal-react';
import { msalInstance } from '@/lib/msalInstance';
import Image from 'next/image';
import Cookies from 'js-cookie';
import axiosInstanceUi from '@/lib/axiosInstance.ui';
import { API_CONFIG } from '@/config/api';

export default function LoginPage() {
  const router = useRouter();
  const { instance } = useMsal();
  const { store, setStore } = useGlobal();

  useEffect(() => {
    sessionStorage.clear();
    localStorage.clear();
    setStore((prevStore) => ({
      ...prevStore,
      token: null,
    }));
    Cookies.remove('token');
    router.push('/login');
  }, []);

  return (
    <div className="flex min-h-screen min-w-screen items-center justify-center">
      <div>Logging out...</div>
    </div>
  );
}
