{"$schema": "https://developer.microsoft.com/en-us/json-schemas/teams/v1.16/MicrosoftTeams.schema.json", "manifestVersion": "1.16", "version": "1.0.0", "id": "chuck-studio-app", "packageName": "com.mawer.chuckstudio", "developer": {"name": "Mawer Investment Management Ltd.", "websiteUrl": "https://www.mawer.com", "privacyUrl": "https://www.mawer.com/privacy", "termsOfUseUrl": "https://www.mawer.com/terms"}, "icons": {"color": "public/Chuck-logo-full.png", "outline": "public/logo-icon.svg"}, "name": {"short": "Chuck Studio", "full": "Chuck Studio - AI Platform"}, "description": {"short": "AI-powered platform for investment professionals", "full": "Chuck Studio is Mawer's AI platform providing chat interfaces, speech-to-text capabilities, and workspace management tools for investment professionals."}, "accentColor": "#FFFFFF", "configurableTabs": [{"configurationUrl": "https://your-domain.com/teams-config", "canUpdateConfiguration": true, "scopes": ["team", "groupchat"]}], "staticTabs": [{"entityId": "chuck-studio-tab", "name": "Chuck Studio", "contentUrl": "https://your-domain.com/c", "websiteUrl": "https://your-domain.com/c", "scopes": ["personal"]}], "permissions": ["identity", "messageTeamMembers"], "validDomains": ["your-domain.com", "*.mawer.com", "*.teams.microsoft.com", "*.teams.office.com"], "webApplicationInfo": {"id": "your-azure-app-id", "resource": "https://your-domain.com"}}