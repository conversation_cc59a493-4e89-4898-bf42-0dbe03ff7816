'use client';

import React from 'react';
import { useFetchDashboard } from '@/features/admin/api/useFetchDashboard';
import StatCard from '@/features/admin/components/dashboard/StatCard';
import FeedbackChart from '@/features/admin/components/dashboard/FeedbackChart';
import FeedbackDetailsCard from '@/features/admin/components/dashboard/FeedbackDetailsCard';
import ChatMetricsChart from '@/features/admin/components/dashboard/ChatMetricsChart';
import ModelCountsChart from '@/features/admin/components/dashboard/ModelCountsChart';
import UserCountsChart from '@/features/admin/components/dashboard/UserCountsChart';
import {
  Users,
  MessageSquare,
  MessagesSquare,
  Book,
  FileText,
  Group,
} from 'lucide-react';

export default function DashboardPage() {
  const { data, isLoading } = useFetchDashboard();

  return (
    data && (
      <div className="space-y-4">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-6">
          <StatCard
            title="Chats"
            value={data.totalChats}
            icon={<MessageSquare className="h-6 w-6" />}
            description="Total chat sessions"
            isLoading={isLoading}
          />
          <StatCard
            title="Messages"
            value={data.totalMes}
            icon={<MessagesSquare className="h-6 w-6" />}
            description={`${Math.round(data.totalMes / data.totalChats)} messages per chat avg.`}
            isLoading={isLoading}
          />
          <StatCard
            title="Users"
            value={data.totalUsers}
            icon={<Users className="h-6 w-6" />}
            description="Active users in the system"
            isLoading={isLoading}
          />
          <StatCard
            title="Groups"
            value={data.totalGroups}
            icon={<Group className="h-6 w-6" />}
            description="Active groups in the system"
            isLoading={isLoading}
          />
          <StatCard
            title="Collections"
            value={data.totalCollections}
            icon={<Book className="h-6 w-6" />}
            description="Total collections"
            isLoading={isLoading}
          />
          <StatCard
            title="Documents"
            value={data.totalDocuments}
            icon={<FileText className="h-6 w-6" />}
            description="Total documents"
            isLoading={isLoading}
          />
        </div>

        <div className="grid grid-cols-1 gap-4 lg:grid-cols-3">
          <FeedbackChart data={data} isLoading={isLoading} />
          <div className="col-span-2">
            <ChatMetricsChart data={data} isLoading={isLoading} />
          </div>
        </div>

        <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
          {data.modelCounts && data.modelCounts.length > 0 && (
            <div className="grid grid-cols-1 gap-6">
              <ModelCountsChart data={data.modelCounts} isLoading={isLoading} />
            </div>
          )}

          {data.userCounts && data.userCounts.length > 0 && (
            <div className="grid grid-cols-1 gap-6">
              <UserCountsChart data={data.userCounts} isLoading={isLoading} />
            </div>
          )}
        </div>
      </div>
    )
  );
}
