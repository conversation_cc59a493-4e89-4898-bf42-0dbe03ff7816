import { useMutation } from '@tanstack/react-query';
import axiosInstanceUi from '@/lib/axiosInstance.ui';
import { API_CONFIG } from '@/config/api';

interface ChatShareResponse {
  token: string;
  url: string;
}

/**
 * Hook to create a shared link for a chat
 * @returns Mutation for creating a shared link
 */
export function useCreateChatSharedLink() {
  return useMutation<ChatShareResponse, Error, string>({
    mutationFn: async (chatId: string) => {
      const response = await axiosInstanceUi.get<ChatShareResponse>(
        `${API_CONFIG.chat.chat}/${chatId}/share`
      );
      
      if (!response.data) {
        throw new Error('Failed to create shared link');
      }
      
      return response.data;
    },
  });
}
