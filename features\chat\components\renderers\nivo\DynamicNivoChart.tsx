import React, { useState, useEffect } from 'react';
import { NivoChartConfig, NivoTheme } from './types';

let ResponsiveLine: any = null;
let ResponsiveBar: any = null;
let ResponsivePie: any = null;

const initializeComponents = async () => {
  try {
    if (!ResponsiveLine) {
      const lineModule = await import('@nivo/line');
      ResponsiveLine = lineModule.ResponsiveLine;
    }
    if (!ResponsiveBar) {
      const barModule = await import('@nivo/bar');
      ResponsiveBar = barModule.ResponsiveBar;
    }
    if (!ResponsivePie) {
      const pieModule = await import('@nivo/pie');
      ResponsivePie = pieModule.ResponsivePie;
    }
  } catch (error) {
    console.error('Error loading components:', error);
  }
};

const loadChartComponent = async (
  chartType: string
): Promise<React.ComponentType<any> | null> => {
  try {
    await initializeComponents();

    switch (chartType) {
      case 'bar': {
        if (ResponsiveBar) {
          return ResponsiveBar;
        }
        const { ResponsiveBar: BarComponent } = await import('@nivo/bar');
        return BarComponent;
      }
      case 'line': {
        if (ResponsiveLine) {
          return ResponsiveLine;
        }
        const { ResponsiveLine: LineComponent } = await import('@nivo/line');
        return LineComponent;
      }
      case 'pie': {
        if (ResponsivePie) {
          return ResponsivePie;
        }
        const { ResponsivePie: PieComponent } = await import('@nivo/pie');
        return PieComponent;
      }
      case 'calendar': {
        const { ResponsiveCalendar } = await import('@nivo/calendar');
        return ResponsiveCalendar;
      }
      case 'radialBar': {
        const { ResponsiveRadialBar } = await import('@nivo/radial-bar');
        return ResponsiveRadialBar;
      }
      case 'radar': {
        const { ResponsiveRadar } = await import('@nivo/radar');
        return ResponsiveRadar;
      }
      default:
        return null;
    }
  } catch (error) {
    console.error(
      `Failed to load chart component for type: ${chartType}`,
      error
    );
    return null;
  }
};

const availableChartTypes = [
  'bar',
  'line',
  'pie',
  'calendar',
  'radialBar',
  'radar',
];

function getContrastColor(hex: string): string {
  hex = hex.replace('#', '');
  if (hex.length === 3) {
    hex = hex
      .split('')
      .map((c) => c + c)
      .join('');
  }
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
  return luminance > 0.5 ? '#222' : '#fff';
}

function createNivoTheme(isDark: boolean): NivoTheme {
  return {
    background: isDark ? '#070d1a' : '#ffffff',
    textColor: isDark ? '#ffffff' : '#222222',
    gridColor: isDark ? '#333333' : '#e1e1e1',
    tooltipBackground: isDark ? '#2a2a2a' : '#ffffff',
    tooltipColor: isDark ? '#ffffff' : '#222222',
  };
}

function applyThemeToChartProps(
  chartProps: any,
  theme: NivoTheme,
  chartType: string
): any {
  const themedProps = {
    ...chartProps,
    theme: {
      background: theme.background,
      textColor: theme.textColor,
      tooltip: {
        container: {
          background: theme.tooltipBackground,
          color: theme.tooltipColor,
          borderRadius: 4,
          boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
          fontSize: 12,
          fontFamily: 'inherit',
        },
      },
      axis: {
        ticks: {
          text: {
            fill: theme.textColor,
            fontSize: 11,
          },
        },
        legend: {
          text: {
            fill: theme.textColor,
            fontSize: 12,
          },
        },
      },
      legends: {
        text: {
          fill: theme.textColor,
          fontSize: 11,
        },
      },
      labels: {
        text: {
          fill: theme.textColor,
          fontSize: 11,
        },
      },
      grid: {
        line: {
          stroke: theme.gridColor,
          strokeWidth: 1,
        },
      },
      ...chartProps.theme,
    },
  };

  switch (chartType) {
    case 'pie':
      return {
        ...themedProps,
        arcLabelsTextColor: (d: any) => {
          const itemData = themedProps.data?.find(
            (item: any) => item.id === d.id
          );
          return getContrastColor(itemData?.color || d.color || '#000000');
        },
        arcLinkLabelsTextColor: theme.textColor,
        tooltip: ({ datum }: any) => {
          return (
            <div
              style={{
                background: theme.tooltipBackground,
                color: theme.tooltipColor,
                padding: '8px 12px',
                borderRadius: '4px',
                fontSize: '12px',
                boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
              }}
            >
              <strong>{datum.id}</strong>: {datum.value}
            </div>
          );
        },
        legends: [
          {
            anchor: 'top-right',
            direction: 'column',
            justify: false,
            translateX: 120,
            translateY: 50,
            itemsSpacing: 6,
            itemWidth: 140,
            itemHeight: 20,
            itemDirection: 'left-to-right',
            itemTextColor: theme.textColor,
            symbolSize: 12,
            symbolShape: 'circle',
          },
        ],
        margin: {
          top: 40,
          right: 200,
          bottom: 40,
          left: 20,
        },
        innerRadius: themedProps.innerRadius || 0.5,
        padAngle: themedProps.padAngle || 0.7,
      };

    case 'bar':
      return {
        ...themedProps,
        animate: false,
        motionConfig: 'disabled',
        isInteractive: true,
        enableLabel: true,
        enableGridX: true,
        enableGridY: true,
        initial: false,
        colors: themedProps.colors
          ? ({ index }: any) => {
              const colorArray = Array.isArray(themedProps.colors)
                ? themedProps.colors
                : [themedProps.colors];
              return colorArray[index % colorArray.length];
            }
          : ['#3182ce'],
        tooltip: ({ id, value, color, data }: any) => {
          return (
            <div
              style={{
                background: theme.tooltipBackground,
                color: theme.tooltipColor,
                padding: '8px 12px',
                borderRadius: '4px',
                fontSize: '12px',
                boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
              }}
            >
              <strong>{id}</strong>: {value}
            </div>
          );
        },
        labelTextColor: (bar: any) => getContrastColor(bar.color || '#000000'),
        legends:
          themedProps.legends?.map((legend: any) => ({
            ...legend,
            itemTextColor: theme.textColor,
          })) || [],
      };

    case 'line':
      let pointColor = themedProps.pointColor;
      if (typeof pointColor === 'object' && pointColor !== null) {
        if (pointColor.theme === 'background') {
          pointColor = theme.background;
        } else if (pointColor.from === 'color') {
          pointColor = 'inherit';
        } else {
          pointColor = 'inherit';
        }
      } else if (!pointColor) {
        pointColor = 'inherit';
      }

      let pointBorderColor = themedProps.pointBorderColor;
      if (typeof pointBorderColor === 'object' && pointBorderColor !== null) {
        if (
          pointBorderColor.from === 'serieColor' ||
          pointBorderColor.from === 'color'
        ) {
          pointBorderColor =
            theme.background === '#070d1a' ? '#ffffff' : '#000000';
        } else if (pointBorderColor.theme) {
          pointBorderColor = theme.textColor;
        } else {
          pointBorderColor =
            theme.background === '#070d1a' ? '#ffffff' : '#000000';
        }
      } else if (!pointBorderColor) {
        pointBorderColor =
          theme.background === '#070d1a' ? '#ffffff' : '#000000';
      }

      return {
        data: themedProps.data,
        xScale: themedProps.xScale,
        yScale: themedProps.yScale,

        axisTop: themedProps.axisTop,
        axisRight: themedProps.axisRight,
        axisBottom: themedProps.axisBottom,
        axisLeft: themedProps.axisLeft,

        colors: themedProps.colors || ['#3182ce'],

        pointSize: themedProps.pointSize || 8,
        pointColor,
        pointBorderWidth: themedProps.pointBorderWidth || 2,
        pointBorderColor,
        enablePoints: themedProps.enablePoints !== false,

        lineWidth: themedProps.lineWidth || 2,
        curve: themedProps.curve || 'linear',

        enableGridX: themedProps.enableGridX !== false,
        enableGridY: themedProps.enableGridY !== false,
        useMesh: true,
        enableSlices: 'x',

        animate: false,
        motionConfig: 'disabled',
        isInteractive: true,

        margin: {
          ...themedProps.margin,
          top: Math.max(themedProps.margin?.top || 40, 80),
          right: Math.max(
            themedProps.margin?.right || 30,
            themedProps.legends && themedProps.legends.length > 0 ? 180 : 80
          ),
          bottom: Math.max(themedProps.margin?.bottom || 70, 90),
          left: Math.max(themedProps.margin?.left || 60, 90),
        },

        theme: themedProps.theme,

        legends:
          themedProps.legends?.map((legend: any) => ({
            ...legend,
            anchor: legend.anchor || 'top-left',
            direction: legend.direction || 'column',
            justify: false,
            translateX: legend.translateX || 10,
            translateY: legend.translateY || 10,
            itemsSpacing: legend.itemsSpacing || 6,
            itemWidth: legend.itemWidth || 140,
            itemHeight: legend.itemHeight || 20,
            itemDirection: legend.itemDirection || 'left-to-right',
            symbolSize: legend.symbolSize || 12,
            symbolShape: legend.symbolShape || 'circle',
            itemTextColor: theme.textColor,
          })) || [],

        tooltip: ({ point }: any) => {
          return (
            <div
              style={{
                background: theme.tooltipBackground,
                color: theme.tooltipColor,
                padding: '12px 16px',
                borderRadius: '6px',
                fontSize: '13px',
                boxShadow: '0 4px 12px rgba(0,0,0,0.25)',
                border: `1px solid ${theme.gridColor}`,
                maxWidth: '280px',
                minWidth: '200px',
                whiteSpace: 'nowrap',
                zIndex: 9999,
                position: 'absolute',
                pointerEvents: 'none',
                transform: 'translate(-50%, -100%)',
                marginTop: '-10px',
              }}
            >
              <div
                style={{
                  fontWeight: 'bold',
                  marginBottom: '6px',
                  color: point.serieColor,
                }}
              >
                {point.serieId}
              </div>
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                }}
              >
                <span
                  style={{
                    marginRight: '12px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                  }}
                >
                  {point.data.xFormatted}
                </span>
                <span
                  style={{
                    fontWeight: 'bold',
                    fontSize: '14px',
                    color: point.serieColor,
                  }}
                >
                  {point.data.yFormatted}
                </span>
              </div>
            </div>
          );
        },
      };

    case 'radar':
      return {
        ...themedProps,
        gridLabelOffset: 16,
        gridLevels: 5,
        gridShape: 'linear',
        theme: {
          ...themedProps.theme,
          grid: {
            line: {
              stroke:
                theme.background === '#070d1a' ? '#4a90e2' : theme.gridColor,
              strokeWidth: 1,
            },
          },
          crosshair: {
            line: {
              stroke: theme.background === '#070d1a' ? '#60a5fa' : '#3b82f6',
              strokeWidth: 1,
            },
          },
        },
        borderWidth: 3,
        borderColor: theme.background === '#070d1a' ? '#60a5fa' : '#3b82f6',
        fillOpacity: theme.background === '#070d1a' ? 0.15 : 0.25,
        blendMode: 'normal',
        enableArea: true,
        areaOpacity: theme.background === '#070d1a' ? 0.15 : 0.25,
        dotSize: 6,
        dotColor: 'inherit',
        dotBorderWidth: 2,
        dotBorderColor: theme.background === '#070d1a' ? '#ffffff' : '#000000',
        enableDots: true,
        enableDotLabel: false,
        tooltip: ({ datum }: any) => {
          return (
            <div
              style={{
                background: theme.tooltipBackground,
                color: theme.tooltipColor,
                padding: '8px 12px',
                borderRadius: '4px',
                fontSize: '12px',
                boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
              }}
            >
              <strong>{datum.key}</strong>: {datum.value}
            </div>
          );
        },
        legends:
          themedProps.legends?.map((legend: any) => ({
            ...legend,
            itemTextColor: theme.textColor,
          })) || [],
      };

    case 'radialBar':
      return {
        ...themedProps,
        data:
          themedProps.data?.map((item: any, index: number) => ({
            id: item[themedProps.indexBy] || item.id || `item-${index}`,
            data: [
              {
                x: item[themedProps.indexBy] || item.id || `item-${index}`,
                y: item[themedProps.keys?.[0]] || item.value || 0,
              },
            ],
          })) || [],
        animate: false,
        motionConfig: 'disabled',
        isInteractive: true,
        initial: false,
        tooltip: ({ datum }: any) => {
          if (!datum) return null;
          return (
            <div
              style={{
                background: theme.tooltipBackground,
                color: theme.tooltipColor,
                padding: '8px 12px',
                borderRadius: '4px',
                fontSize: '12px',
                boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
              }}
            >
              <strong>{datum.id || 'Unknown'}</strong>:{' '}
              {datum.data?.[0]?.y || datum.value || 0}
            </div>
          );
        },
        labelsTextColor: (d: any) => getContrastColor(d.color || '#000000'),
        legends:
          themedProps.legends?.map((legend: any) => ({
            ...legend,
            itemTextColor: theme.textColor,
          })) || [],
      };

    case 'calendar':
      return {
        ...themedProps,
        tooltip: ({ day, value, color }: any) => {
          return (
            <div
              style={{
                background: theme.tooltipBackground,
                color: theme.tooltipColor,
                padding: '8px 12px',
                borderRadius: '4px',
                fontSize: '12px',
                boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
              }}
            >
              <strong>{day}</strong>: {value || 0}
            </div>
          );
        },
        legends:
          themedProps.legends?.map((legend: any) => ({
            ...legend,
            itemTextColor: theme.textColor,
          })) || [],
      };

    default:
      return {
        ...themedProps,
        legends:
          themedProps.legends?.map((legend: any) => ({
            ...legend,
            itemTextColor: theme.textColor,
          })) || [],
      };
  }
}

interface DynamicNivoChartProps {
  jsonString: string;
  width?: number;
  height?: number;
  isDark?: boolean;
  onError?: (error: string) => void;
}

const DynamicNivoChart: React.FC<DynamicNivoChartProps> = ({
  jsonString,
  width = 800,
  height = 500,
  isDark = false,
  onError,
}) => {
  const [error, setError] = useState<string | null>(null);
  const [ChartComponent, setChartComponent] =
    useState<React.ComponentType<any> | null>(null);
  const [chartProps, setChartProps] = useState<any>({});
  const [chartType, setChartType] = useState<string>('');

  useEffect(() => {
    if (!jsonString.trim()) {
      setError(null);
      setChartComponent(null);
      setChartProps({});
      setChartType('');
      return;
    }

    const loadChart = async () => {
      try {
        const parsed: NivoChartConfig = JSON.parse(jsonString);

        if (!parsed.type || !availableChartTypes.includes(parsed.type)) {
          const errorMsg = `Chart type '${parsed.type || 'undefined'}' not supported. Available types: ${availableChartTypes.join(', ')}`;
          throw new Error(errorMsg);
        }

        const Component = await loadChartComponent(parsed.type);

        if (!Component) {
          throw new Error(
            `Failed to load component for chart type: ${parsed.type}`
          );
        }

        const { type, ...props } = parsed;

        const theme = createNivoTheme(isDark);
        const themedProps = applyThemeToChartProps(props, theme, type);

        setChartComponent(Component);
        setChartProps(themedProps);
        setChartType(type);
        setError(null);
      } catch (e: any) {
        const errorMsg =
          e.message || 'Unknown error parsing chart configuration';
        setChartComponent(null);
        setChartProps({});
        setChartType('');
        setError(errorMsg);
        onError?.(errorMsg);
      }
    };

    loadChart();
  }, [jsonString, isDark, onError]);

  if (error) {
    return (
      <div
        style={{
          width,
          height: Math.min(height, 200),
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#ef4444',
          backgroundColor: '#fef2f2',
          padding: '16px',
          borderRadius: '8px',
          border: '1px solid #fecaca',
          fontSize: '14px',
        }}
      >
        <div>
          <strong>Error:</strong> {error}
        </div>
      </div>
    );
  }

  if (!ChartComponent) {
    return (
      <div
        style={{
          width,
          height,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#6b7280',
          backgroundColor: '#f9fafb',
          borderRadius: '8px',
          border: '1px solid #e5e7eb',
        }}
      >
        Waiting for chart data...
      </div>
    );
  }

  return (
    <div style={{ width, height }}>
      <ChartComponent {...chartProps} />
    </div>
  );
};

export default DynamicNivoChart;
