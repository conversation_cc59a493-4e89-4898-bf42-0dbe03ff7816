import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook } from '@testing-library/react';
import { usePermissions } from '../usePermissions';
import { useGlobal } from '@/contexts/GlobalContext';
import { IUserData } from '@/types/user';
import { ResourceType, OperationType } from '@/lib/permissions/types';

// Mock the GlobalContext
vi.mock('@/contexts/GlobalContext', () => ({
  useGlobal: vi.fn(),
}));

describe('usePermissions', () => {
  // Create a mock user with permissions
  const mockUser: IUserData = {
    id: 'user-1',
    name: 'Test User',
    email: '<EMAIL>',
    profile_picture: null,
    groups: [
      {
        id: 'group-1',
        name: 'Admin Group',
        description: 'Group with admin permissions',
        meta: {
          perms: [
            { res: 'admin', ops: ['read'] },
            { res: 'chat', ops: ['read', 'write', 'delete'] },
          ],
        },
      },
      {
        id: 'group-2',
        name: 'User Group',
        description: 'Group with user permissions',
        meta: {
          perms: [
            { res: 'model', ops: ['read'] },
            { res: 'collection', ops: ['read', 'write'] },
          ],
        },
      },
    ],
  };

  // Reset mocks before each test
  beforeEach(() => {
    vi.clearAllMocks();
    (useGlobal as any).mockReturnValue({
      store: { user: mockUser },
    });
  });

  it('should check if user has a specific permission', () => {
    const { result } = renderHook(() => usePermissions());
    
    // User should have these permissions
    expect(result.current.can('admin', 'read')).toBe(true);
    expect(result.current.can('chat', 'write')).toBe(true);
    expect(result.current.can('model', 'read')).toBe(true);
    
    // User should not have these permissions
    expect(result.current.can('admin', 'write')).toBe(false);
    expect(result.current.can('user', 'read')).toBe(false);
  });

  it('should check if user has any of the specified permissions', () => {
    const { result } = renderHook(() => usePermissions());
    
    // User has at least one of these permissions
    expect(result.current.canAny([
      { resource: 'admin', operation: 'write' },
      { resource: 'chat', operation: 'read' },
    ])).toBe(true);
    
    // User has none of these permissions
    expect(result.current.canAny([
      { resource: 'admin', operation: 'delete' },
      { resource: 'user', operation: 'read' },
    ])).toBe(false);
  });

  it('should check if user has all of the specified permissions', () => {
    const { result } = renderHook(() => usePermissions());
    
    // User has all of these permissions
    expect(result.current.canAll([
      { resource: 'admin', operation: 'read' },
      { resource: 'chat', operation: 'read' },
    ])).toBe(true);
    
    // User does not have all of these permissions
    expect(result.current.canAll([
      { resource: 'admin', operation: 'read' },
      { resource: 'admin', operation: 'write' },
    ])).toBe(false);
  });

  it('should check if user is an admin', () => {
    const { result } = renderHook(() => usePermissions());
    
    // User has admin read permission
    expect(result.current.isAdmin()).toBe(true);
    
    // Test with a non-admin user
    (useGlobal as any).mockReturnValue({
      store: { 
        user: {
          ...mockUser,
          groups: [mockUser.groups[1]], // Only include the non-admin group
        },
      },
    });
    
    const { result: nonAdminResult } = renderHook(() => usePermissions());
    expect(nonAdminResult.current.isAdmin()).toBe(false);
  });

  it('should get all resources the user has access to', () => {
    const { result } = renderHook(() => usePermissions());
    
    const resources = result.current.getResources();
    expect(resources).toContain('admin');
    expect(resources).toContain('chat');
    expect(resources).toContain('model');
    expect(resources).toContain('collection');
    expect(resources).not.toContain('user');
  });

  it('should get all operations the user can perform on a specific resource', () => {
    const { result } = renderHook(() => usePermissions());
    
    const chatOperations = result.current.getOperations('chat');
    expect(chatOperations).toContain('read');
    expect(chatOperations).toContain('write');
    expect(chatOperations).toContain('delete');
    
    const modelOperations = result.current.getOperations('model');
    expect(modelOperations).toContain('read');
    expect(modelOperations).not.toContain('write');
    
    const userOperations = result.current.getOperations('user');
    expect(userOperations).toEqual([]);
  });

  it('should handle null user', () => {
    (useGlobal as any).mockReturnValue({
      store: { user: null },
    });
    
    const { result } = renderHook(() => usePermissions());
    
    expect(result.current.can('admin', 'read')).toBe(false);
    expect(result.current.canAny([{ resource: 'admin', operation: 'read' }])).toBe(false);
    expect(result.current.canAll([{ resource: 'admin', operation: 'read' }])).toBe(false);
    expect(result.current.isAdmin()).toBe(false);
    expect(result.current.getResources()).toEqual([]);
    expect(result.current.getOperations('admin')).toEqual([]);
    expect(result.current.user).toBeNull();
  });
});
