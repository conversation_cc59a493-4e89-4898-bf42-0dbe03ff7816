import { NextRequest, NextResponse } from 'next/server';
import axiosInstanceApi from '@/lib/axiosInstance.api';
import { API_CONFIG } from '@/config/api';

// The GET handler now accepts a second argument for route parameters
export async function GET(
  request: NextRequest,
  props: { params: Promise<{ chatId: string }> }
) {
  // Extract Authorization header from the incoming request
  const authorization = request.headers.get('Authorization');
  const { chatId } = await props.params; // Extract chatId from the params object

  // Construct the target path dynamically using the chatId
  // Assuming API_CONFIG.chat.chat is the base path like '/chats'
  const targetPath = `${API_CONFIG.chat.chat}/${chatId}`;

  // No need to forward query parameters when fetching a specific resource by ID usually
  // const { searchParams } = new URL(request.url);

  // Check if chatId is present
  if (!chatId) {
    return NextResponse.json(
      { message: 'Chat ID is required' },
      { status: 400 }
    );
  }

  try {
    console.log(
      `[API Route Proxy] Forwarding GET ${targetPath} with Auth: ${!!authorization}`
    );

    const apiResponse = await axiosInstanceApi.get(targetPath, {
      headers: {
        ...(authorization && { Authorization: authorization }),
      },
    });

    // Return the response from the backend service
    return NextResponse.json(apiResponse.data, { status: apiResponse.status });
  } catch (error: any) {
    if (error.response?.status === 404) {
      return NextResponse.json(
        { message: error.message },
        { status: error.response?.status }
      );
    }

    console.error(
      `[API Route Proxy Error] GET ${targetPath}:`,
      error.response?.data || error.message
    );
    const status = error.response?.status || 500;
    const message = error.response?.data?.message || 'Proxy error';
    return NextResponse.json({ message }, { status });
  }
}

// You can add handlers for other methods (POST, PUT, DELETE) similarly
export async function POST(
  request: NextRequest,
  props: { params: Promise<{ chatId: string }> }
) {
  const authorization = request.headers.get('Authorization');
  const { chatId } = await props.params;
  const targetPath = `${API_CONFIG.chat.chat}/${chatId}`; // Same endpoint for POSTing a message to a chat

  if (!chatId) {
    return NextResponse.json(
      { message: 'Chat ID is required' },
      { status: 400 }
    );
  }

  try {
    // Extract the message from the request body
    const body = await request.json();
    const messageContent = body.message;

    // if (typeof messageContent !== 'string' || !messageContent.trim()) {
    //   return NextResponse.json(
    //     { message: 'Message content (string) is required in the request body' },
    //     { status: 400 }
    //   );
    // }

    console.log(
      `[API Route Proxy] Forwarding POST ${targetPath} with Auth: ${!!authorization}`
    );

    // Forward the POST request to the backend API
    const apiResponse = await axiosInstanceApi.post(
      targetPath,
      { message: messageContent }, // Send the message in the expected payload format
      {
        headers: {
          'Content-Type': 'application/json', // Important for POST requests
          ...(authorization && { Authorization: authorization }),
        },
      }
    );

    // Return the response from the backend service
    return NextResponse.json(apiResponse.data, { status: apiResponse.status });
  } catch (error: any) {
    if (error.response?.status === 404) {
      return NextResponse.json(
        { message: error.message },
        { status: error.response?.status }
      );
    }

    console.error(
      `[API Route Proxy Error] POST ${targetPath}:`,
      error.response?.data || error.message
    );
    // Handle JSON parsing errors specifically
    if (error instanceof SyntaxError) {
      return NextResponse.json(
        { message: 'Invalid JSON payload' },
        { status: 400 }
      );
    }
    const status = error.response?.status || 500;
    const message = error.response?.data?.message || 'Proxy error';
    return NextResponse.json({ message }, { status });
  }
}

// PATCH handler for updating a chat's name
export async function PATCH(
  request: NextRequest,
  props: { params: Promise<{ chatId: string }> }
) {
  const authorization = request.headers.get('Authorization');
  const { chatId } = await props.params;
  const targetPath = `${API_CONFIG.chat.chat}/${chatId}`;

  if (!chatId) {
    return NextResponse.json(
      { message: 'Chat ID is required' },
      { status: 400 }
    );
  }

  try {
    // Extract fields from the request body
    const body = await request.json();
    const { name, pinned } = body;
    const payload: { name?: string; pinned?: boolean } = {};

    // Validate and add name to payload if provided
    if (name !== undefined) {
      if (typeof name !== 'string' || !name.trim()) {
        return NextResponse.json(
          { message: 'Name must be a non-empty string' },
          { status: 400 }
        );
      }
      payload.name = name.trim();
    }

    // Validate and add pinned to payload if provided
    if (pinned !== undefined) {
      if (typeof pinned !== 'boolean') {
        return NextResponse.json(
          { message: 'Pinned must be a boolean value' },
          { status: 400 }
        );
      }
      payload.pinned = pinned;
    }

    // Ensure at least one valid field is provided
    if (Object.keys(payload).length === 0) {
      return NextResponse.json(
        { message: 'At least one valid field (name or pinned) is required' },
        { status: 400 }
      );
    }

    console.log(
      `[API Route Proxy] Forwarding PATCH ${targetPath} with Auth: ${!!authorization}`
    );

    // Forward the PATCH request to the backend API with only the provided fields
    const apiResponse = await axiosInstanceApi.patch(targetPath, payload, {
      headers: {
        'Content-Type': 'application/json',
        ...(authorization && { Authorization: authorization }),
      },
    });

    // Return the response from the backend service
    return NextResponse.json(apiResponse.data, { status: apiResponse.status });
  } catch (error: any) {
    if (error.response?.status === 404) {
      return NextResponse.json(
        { message: error.message },
        { status: error.response?.status }
      );
    }

    console.error(
      `[API Route Proxy Error] PATCH ${targetPath}:`,
      error.response?.data || error.message
    );
    // Handle JSON parsing errors specifically
    if (error instanceof SyntaxError) {
      return NextResponse.json(
        { message: 'Invalid JSON payload' },
        { status: 400 }
      );
    }
    const status = error.response?.status || 500;
    const message = error.response?.data?.message || 'Proxy error';
    return NextResponse.json({ message }, { status });
  }
}

// Handler for DELETE requests to delete a chat
export async function DELETE(
  request: NextRequest,
  props: { params: Promise<{ chatId: string }> }
) {
  const authorization = request.headers.get('Authorization');
  const { chatId } = await props.params;
  const targetPath = `${API_CONFIG.chat.chat}/${chatId}`;

  if (!chatId) {
    return NextResponse.json(
      { message: 'Chat ID is required' },
      { status: 400 }
    );
  }

  try {
    console.log(
      `[API Route Proxy] Forwarding DELETE ${targetPath} with Auth: ${!!authorization}`
    );

    // Forward the DELETE request to the backend API
    const apiResponse = await axiosInstanceApi.delete(targetPath, {
      headers: {
        ...(authorization && { Authorization: authorization }),
      },
    });

    return NextResponse.json(apiResponse.data, { status: apiResponse.status });
  } catch (error: any) {
    if (error.response?.status === 404) {
      return NextResponse.json(
        { message: error.message },
        { status: error.response?.status }
      );
    }

    console.error(
      `[API Route Proxy Error] DELETE ${targetPath}:`,
      error.response?.data || error.message
    );
    const status = error.response?.status || 500;
    const message = error.response?.data?.message || 'Proxy error';
    return NextResponse.json({ message }, { status });
  }
}
