'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Slider } from '@/components/ui/slider';
import {
  Play,
  Pause,
  Square,
  SkipBack,
  SkipForward,
  Mail,
  Download,
  Copy,
  Check,
} from 'lucide-react';
import { useRecording } from '../api/useRecordings';
import { ScrollArea } from '@/components/ui/scroll-area';
import axiosInstanceUi from '@/lib/axiosInstance.ui';
import { API_CONFIG } from '@/config/api';

interface RecordingPlayerProps {
  recordingId: string;
}

interface TranscriptSegment {
  speaker_id: string;
  speaker_name?: string;
  text: string;
  timestamp?: number;
  sequence: number;
}

export function RecordingPlayer({ recordingId }: RecordingPlayerProps) {
  const { data: recording, isLoading } = useRecording(recordingId);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [email, setEmail] = useState('');
  const [isEmailValid, setIsEmailValid] = useState(false);
  const [isSendingEmail, setIsSendingEmail] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const [isCopied, setIsCopied] = useState(false);
  const audioRef = useRef<HTMLAudioElement>(null);

  useEffect(() => {
    if (recording?.download_url && audioRef.current) {
      audioRef.current.src = recording.download_url;
      setDuration(recording.duration || 0);
    }
  }, [recording]);

  const handlePlayPause = () => {
    if (!audioRef.current) return;

    if (isPlaying) {
      audioRef.current.pause();
    } else {
      audioRef.current.play();
    }
    setIsPlaying(!isPlaying);
  };

  const handleStop = () => {
    if (!audioRef.current) return;
    audioRef.current.pause();
    audioRef.current.currentTime = 0;
    setCurrentTime(0);
    setIsPlaying(false);
  };

  const handleSeek = (value: number[]) => {
    if (!audioRef.current) return;
    const time = value[0];
    audioRef.current.currentTime = time;
    setCurrentTime(time);
  };

  const handleTimeUpdate = () => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime);
    }
  };

  const handleEnded = () => {
    setIsPlaying(false);
    setCurrentTime(0);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatTimestamp = (timestamp: number) => {
    // Convert Unix timestamp to readable time
    return new Date(timestamp * 1000).toLocaleTimeString();
  };

  const skip = (seconds: number) => {
    if (!audioRef.current) return;
    const newTime = Math.max(
      0,
      Math.min(duration, audioRef.current.currentTime + seconds)
    );
    audioRef.current.currentTime = newTime;
    setCurrentTime(newTime);
  };

  // Email validation
  useEffect(() => {
    const isValid = email.includes('@') && email.endsWith('@mawer.com');
    setIsEmailValid(isValid);
    setEmailSent(false); // Reset email sent status when email changes
  }, [email]);

  const handleSendEmail = async () => {
    if (!isEmailValid || !recording) return;

    try {
      setIsSendingEmail(true);
      await axiosInstanceUi.post(
        `${API_CONFIG.recording.email}/${recordingId}/email`,
        {
          email: email,
        }
      );
      setEmailSent(true);
      setEmail(''); // Clear email field after successful send
    } catch (error) {
      console.error('Failed to send email:', error);
      alert('Failed to send email. Please try again.');
    } finally {
      setIsSendingEmail(false);
    }
  };

  const handleDownload = () => {
    if (!recording?.download_url) return;

    // Create a temporary anchor element to trigger download
    const link = document.createElement('a');
    link.href = recording.download_url;
    link.download = `${recording.name}.webm`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const formatTranscriptForCopy = (segments: TranscriptSegment[]) => {
    if (segments.length === 0) return 'No transcript available';

    let markdown = `# ${recording?.name || 'Recording'} - Transcript\n\n`;
    markdown += `**Date:** ${recording ? new Date(recording.created * 1000).toLocaleDateString() : 'Unknown'}\n`;
    markdown += `**Duration:** ${recording ? formatTime(recording.duration) : 'Unknown'}\n\n`;
    markdown += '---\n\n';

    segments.forEach((segment, index) => {
      const speaker = segment.speaker_name || `Speaker ${segment.speaker_id}`;
      const timestamp = segment.timestamp
        ? formatTimestamp(segment.timestamp)
        : '';

      markdown += `**${speaker}** ${timestamp ? `_(${timestamp})_` : ''}\n`;
      markdown += `${segment.text}\n\n`;
    });

    return markdown;
  };

  const handleCopyTranscript = async () => {
    if (transcriptSegments.length === 0) return;

    try {
      const formattedTranscript = formatTranscriptForCopy(transcriptSegments);
      await navigator.clipboard.writeText(formattedTranscript);
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000); // Reset after 2 seconds
    } catch (error) {
      console.error('Failed to copy transcript:', error);
      alert('Failed to copy transcript. Please try again.');
    }
  };

  if (isLoading) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-gray-500">Loading recording...</div>
      </div>
    );
  }

  if (!recording) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-red-500">Recording not found</div>
      </div>
    );
  }

  // Use transcription_segments instead of transcription
  const transcriptSegments: TranscriptSegment[] = Array.isArray(
    recording.transcription_segments
  )
    ? recording.transcription_segments.filter(
        (segment) => segment.text && segment.text.trim() !== ''
      )
    : [];

  return (
    <div className="flex h-full flex-col">
      <div className="border-b border-gray-200 p-6">
        <h1 className="mb-4 text-2xl font-bold">{recording.name}</h1>

        {recording.download_url ? (
          <div className="space-y-4">
            <audio
              ref={audioRef}
              onTimeUpdate={handleTimeUpdate}
              onEnded={handleEnded}
              onLoadedMetadata={() =>
                setDuration(audioRef.current?.duration || 0)
              }
            />

            {/* Player Controls */}
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => skip(-10)}
                disabled={!duration}
              >
                <SkipBack size={16} />
              </Button>

              <Button
                onClick={handlePlayPause}
                disabled={!duration}
                className="h-12 w-12"
              >
                {isPlaying ? <Pause size={20} /> : <Play size={20} />}
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={handleStop}
                disabled={!duration}
              >
                <Square size={16} />
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={() => skip(10)}
                disabled={!duration}
              >
                <SkipForward size={16} />
              </Button>
            </div>

            {/* Progress Bar */}
            <div className="space-y-2">
              <Slider
                value={[currentTime]}
                max={duration}
                step={1}
                onValueChange={handleSeek}
                className="w-full"
                disabled={!duration}
              />
              <div className="flex justify-between text-sm text-gray-500">
                <span>{formatTime(currentTime)}</span>
                <span>{formatTime(duration)}</span>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-wrap items-center gap-3 border-t border-gray-100 pt-4">
              <Button
                onClick={handleDownload}
                variant="outline"
                className="flex items-center gap-2"
              >
                <Download size={16} />
                Download Recording
              </Button>

              {/* <Button
                onClick={handleCopyTranscript}
                variant="outline"
                className="flex items-center gap-2"
                disabled={transcriptSegments.length === 0}
              >
                {isCopied ? <Check size={16} /> : <Copy size={16} />}
                {isCopied ? 'Copied!' : 'Copy Transcript'}
              </Button> */}

              <div className="flex min-w-[300px] flex-1 items-center gap-2">
                <Input
                  type="email"
                  placeholder="Enter email (@mawer.com only)"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className={`flex-1 ${
                    email && !isEmailValid
                      ? 'border-red-300 focus:border-red-500'
                      : email && isEmailValid
                        ? 'border-green-300 focus:border-green-500'
                        : ''
                  }`}
                />
                <Button
                  onClick={handleSendEmail}
                  disabled={!isEmailValid || isSendingEmail}
                  className="flex items-center gap-2"
                >
                  <Mail size={16} />
                  {isSendingEmail ? 'Sending...' : 'Send'}
                </Button>
              </div>
            </div>

            {/* Email validation message */}
            {email && !isEmailValid && (
              <p className="text-sm text-red-600">
                Please enter a valid @mawer.com email address
              </p>
            )}

            {/* Email sent confirmation */}
            {emailSent && (
              <p className="text-sm text-green-600">Email sent successfully!</p>
            )}
          </div>
        ) : (
          <div className="rounded-lg border border-yellow-200 bg-yellow-50 p-4">
            <p className="text-yellow-800">
              - Audio not yet available for playback, please refresh the page
              later.'
            </p>
          </div>
        )}
      </div>

      {/* Transcript */}
      <div className="flex-1 overflow-hidden">
        <div className="p-6">
          <div className="mb-4 flex items-center justify-between">
            <h2 className="text-lg font-semibold">Transcript</h2>
            {transcriptSegments.length > 0 && (
              <Button
                onClick={handleCopyTranscript}
                variant="ghost"
                size="sm"
                className="flex items-center gap-2"
              >
                {isCopied ? <Check size={14} /> : <Copy size={14} />}
                {isCopied ? 'Copied' : 'Copy All'}
              </Button>
            )}
          </div>

          {transcriptSegments.length === 0 ? (
            <div className="py-8 text-center text-gray-500">
              No transcript available
            </div>
          ) : (
            <ScrollArea className="h-96">
              <div className="space-y-4">
                {transcriptSegments.map((segment, index) => (
                  <div
                    key={`${segment.sequence}-${index}`}
                    className="border-l-4 border-blue-200 pl-4"
                  >
                    <div className="mb-1 flex items-center gap-2">
                      <span className="text-sm font-medium text-blue-600">
                        {segment.speaker_name ||
                          `Speaker ${segment.speaker_id}`}
                      </span>
                      {segment.timestamp && (
                        <span className="text-xs text-gray-500">
                          {formatTimestamp(segment.timestamp)}
                        </span>
                      )}
                      <span className="text-xs text-gray-400">
                        #{segment.sequence}
                      </span>
                    </div>
                    <p className="text-gray-800">{segment.text}</p>
                  </div>
                ))}
              </div>
            </ScrollArea>
          )}
        </div>
      </div>
    </div>
  );
}
