import { useQuery } from '@tanstack/react-query';
import axiosInstanceUi from '@/lib/axiosInstance.ui';
import { API_CONFIG } from '@/config/api';
import { IChat } from '@/types/chat';

// Function to fetch a single chat by its ID
export const fetchChatById = async (chatId: string): Promise<IChat> => {
  const response = await axiosInstanceUi.get<IChat>(
    `${API_CONFIG.chat.chat}/${chatId}`
  );

  if (!response.data) {
    throw new Error('Chat not found or empty response'); // Or handle as needed
  }

  return response.data;
};

export const fetchSharedChatByToken = async (token: string): Promise<IChat> => {
  const response = await axiosInstanceUi.get<IChat>(
    `${API_CONFIG.chat.chat}/shared/${token}`
  );

  if (!response.data) {
    throw new Error('Chat not found or empty response'); // Or handle as needed
  }

  return response.data;
};

export const fetchChatShareToken = async (chatId: string): Promise<IChat> => {
  const response = await axiosInstanceUi.get<IChat>(
    `${API_CONFIG.chat.chat}/${chatId}/share`
  );

  if (!response.data) {
    throw new Error('Chat not found or empty response'); // Or handle as needed
  }

  return response.data;
};

export function useFetchChatById(
  chatId: string | null | undefined,
  enabled = true
) {
  return useQuery<IChat, Error>({
    queryKey: [API_CONFIG.chat.chat, chatId],
    queryFn: ({ queryKey }) => {
      const id = queryKey[1] as string;
      if (!id) {
        return Promise.reject(new Error('Chat ID is required'));
      }
      return fetchChatById(id);
    },
    enabled: !!chatId && enabled,
  });
}

export function useFetchSharedChatByToken(
  chatToken: string | null | undefined,
  enabled = true
) {
  return useQuery<IChat, Error>({
    queryKey: [API_CONFIG.chat.chat, chatToken],
    queryFn: ({ queryKey }) => {
      const token = queryKey[1] as string;
      if (!token) {
        return Promise.reject(new Error('Chat Token is required'));
      }
      return fetchSharedChatByToken(token);
    },
    enabled: !!chatToken && enabled,
  });
}
