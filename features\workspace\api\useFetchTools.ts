import { useQuery } from '@tanstack/react-query';
import axiosInstanceUi from '@/lib/axiosInstance.ui';
import { API_CONFIG } from '@/config/api';
import { IToolData } from '@/types/model';

// Function to fetch and return tools data
const fetchTools = async (): Promise<IToolData[]> => {
  const response = await axiosInstanceUi.get<IToolData[]>(API_CONFIG.chat.tool);

  // Sort the data alphabetically by name
  return response.data.sort((a, b) => a.name.localeCompare(b.name));
};

// Hook to fetch tools
export function useFetchTools() {
  return useQuery<IToolData[], Error>({
    queryKey: [API_CONFIG.chat.tool],
    queryFn: fetchTools,
  });
}
