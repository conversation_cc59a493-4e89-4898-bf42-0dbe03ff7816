#!/bin/bash

# arguments
# 1. manifest repository url
# 2. manifest branch

set -x

echo "for email"
echo "$(Release.RequestedForEmail)"
REQUEST_EMAIL="$(Release.RequestedForEmail)"

echo "for name"
echo "$(Release.RequestedFor)"
REQUEST_NAME="$(Release.RequestedFor)"

pwd

echo $(System.DefaultWorkingDirectory)

ls -la

cd $(artifactPath)

# set manifest repo url
REPO_URL=$(chuckManifestRepoUrl)

git config --global user.email "$REQUEST_EMAIL"
git config --global user.name "$REQUEST_NAME"

# clone repo
git clone "$REPO_URL" /tmp/temp_repo

cd /tmp/temp_repo

git checkout $(targetBranch)

manifestDirName="ManifestScript"
REPO_NAME=$(repoName)
IMAGE_TAG=$(Build.BuildNumber)

IMAGE_REPO_NAME="chuck-studio"
IMAGE_REPO="$REPO_NAME.azurecr.io/mawer-ai/$IMAGE_REPO_NAME"
VALUE_PATH="$IMAGE_REPO_NAME/$IMAGE_REPO_NAME/values.yaml"

# replace repo
sed -i "s|repository:.*|repository: $IMAGE_REPO|g" $VALUE_PATH
sed -i "s|tag:.*|tag: $IMAGE_TAG|g" $VALUE_PATH
# add git commit
git add .

git commit -m "Update kubernetes manifest for chuck-studio $IMAGE_TAG"

git -c http.extraheader="AUTHORIZATION: bearer $(System.AccessToken)" push

rm -rf /tmp/temp_repo
