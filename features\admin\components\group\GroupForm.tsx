'use client';

import React, { useState } from 'react';
import { useForm } from '@tanstack/react-form';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { SheetFooter } from '@/components/ui/sheet';
import { MultiSelect } from '@/components/ui/multi-select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { IGroupData, IGroupCreate } from '@/types/group';
import { useCreateGroup } from '@/features/admin/api/useCreateGroup';
import { useUpdateGroup } from '@/features/admin/api/useUpdateGroup';
import { useDeleteGroup } from '@/features/admin/api/useDeleteGroup';
import { useFetchAllUsers } from '@/features/admin/api/useFetchAllUsers';
import { usePermissions } from '@/hooks/usePermissions';
import { getGroupTemplate } from './helpers/getGroupTemplate';
import { formatJson } from './helpers/formatJson';
import { Trash2 } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';

interface GroupFormProps {
  onOpenChange: (open: boolean) => void;
  group?: IGroupData | null; // Optional data for editing
}

// Zod schema for validation
const groupFormSchema = z.object({
  name: z.string().min(3, 'Name must be at least 3 characters'),
  description: z.string().optional(),
});

export default function GroupForm({ onOpenChange, group }: GroupFormProps) {
  const isEditMode = !!group?.id;
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // --- Fetch Users ---
  const { data: users, isLoading: isLoadingUsers } = useFetchAllUsers();

  // Get user permissions
  const { user, isAdmin } = usePermissions();

  // Check if the current user is the owner or an admin
  const canEdit =
    !isEditMode ||
    isAdmin() ||
    (group?.owner_id && user?.id === group.owner_id);

  // --- Mutation Hooks ---
  const handleSuccess = () => {
    onOpenChange(false); // Close sheet on success
  };

  const { mutate: createGroupMutate, isPending: isCreating } = useCreateGroup({
    onSuccessCallback: handleSuccess,
  });
  const { mutate: updateGroupMutate, isPending: isUpdating } = useUpdateGroup({
    onSuccessCallback: handleSuccess,
  });
  const { mutate: deleteGroupMutate, isPending: isDeleting } = useDeleteGroup({
    onSuccessCallback: handleSuccess,
  });

  const isLoadingMutation =
    isCreating || isUpdating || isDeleting || isLoadingUsers;

  // Handle group deletion
  const handleDeleteGroup = () => {
    if (group?.id) {
      deleteGroupMutate(group.id);
    }
  };

  // --- Form Setup ---
  const form = useForm({
    defaultValues: getGroupTemplate(group ?? undefined),
    onSubmit: async ({ value }) => {
      // Prepare payload
      const payload: IGroupCreate = {
        name: value.name,
        description: value.description ?? '', // Ensure description is a string
        userIds: value.userIds,
      };

      if (isEditMode && group?.id) {
        updateGroupMutate({ groupId: group.id, payload });
      } else {
        createGroupMutate(payload);
      }
    },
  });

  // --- UI Variables ---
  const submitButtonText = isEditMode
    ? isLoadingMutation
      ? 'Updating...'
      : 'Update'
    : isLoadingMutation
      ? 'Saving...'
      : 'Create';

  return (
    <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
      <div className="flex flex-col gap-4">
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            if (canEdit) {
              form.handleSubmit();
            }
          }}
        >
          <div className="grid gap-6 p-4">
            <form.Field
              name="name"
              validators={{ onChange: groupFormSchema.shape.name }}
            >
              {(field) => (
                <div className="grid gap-2">
                  <Label htmlFor={field.name}>
                    Name <span className="text-destructive">*</span>
                  </Label>
                  <Input
                    id={field.name}
                    name={field.name}
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="Group name"
                    disabled={isEditMode && !canEdit}
                  />
                  {field.state.meta.isTouched &&
                  field.state.meta.errors.length > 0 ? (
                    <p className="text-destructive text-xs">
                      {field.state.meta.errors[0]?.message}
                    </p>
                  ) : null}
                </div>
              )}
            </form.Field>

            {/* Description Field */}
            <form.Field name="description">
              {(field) => (
                <div className="grid gap-2">
                  <Label htmlFor={field.name}>Description</Label>
                  <Textarea
                    id={field.name}
                    name={field.name}
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="Provide an optional description"
                    className="min-h-[100px] break-words"
                    outline
                    disabled={isEditMode && !canEdit}
                  />
                  {field.state.meta.isTouched &&
                  field.state.meta.errors.length > 0 ? (
                    <p className="text-destructive text-xs">
                      {field.state.meta.errors.join(', ')}
                    </p>
                  ) : null}
                </div>
              )}
            </form.Field>

            {/* Users Field */}
            <form.Field name="userIds">
              {(field) => (
                <div className="grid gap-2">
                  <Label htmlFor={field.name}>Users</Label>
                  <MultiSelect
                    options={
                      users?.map((user) => ({
                        label: user.name,
                        value: user.id,
                      })) || []
                    }
                    defaultValue={field.state.value}
                    onValueChange={(values) => field.handleChange(values)}
                    placeholder="Select users to add to this group"
                    disabled={isEditMode && !canEdit}
                  />
                  {field.state.meta.isTouched &&
                  field.state.meta.errors.length > 0 ? (
                    <p className="text-destructive text-xs">
                      {field.state.meta.errors.join(', ')}
                    </p>
                  ) : null}
                </div>
              )}
            </form.Field>

            {/* Meta Field (Read-only JSON display) */}
            {isEditMode && group?.meta && (
              <div className="mt-4 grid gap-2 border-t pt-4">
                <div className="flex items-center justify-between">
                  <Label className="text-md font-semibold">Permissions</Label>
                  <span className="text-muted-foreground text-xs">
                    Read-only
                  </span>
                </div>
                <ScrollArea className="bg-muted max-h-[500px] w-full rounded-md border">
                  <pre className="text-muted-foreground p-4 font-mono text-xs">
                    {formatJson(group.meta)}
                  </pre>
                </ScrollArea>
                <p className="text-muted-foreground text-xs">
                  This metadata contains permission settings for the group and
                  cannot be edited directly.
                </p>
              </div>
            )}
          </div>

          {/* Footer with Buttons */}
          <SheetFooter className="flex flex-row justify-between gap-2 p-4">
            <div className="flex items-center gap-2">
              {isEditMode && canEdit && (
                <AlertDialogTrigger asChild>
                  <Button
                    variant="destructive"
                    type="button"
                    disabled={isDeleting}
                  >
                    {isDeleting ? (
                      'Deleting...'
                    ) : (
                      <>
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete
                      </>
                    )}
                  </Button>
                </AlertDialogTrigger>
              )}
              {isEditMode && !canEdit && (
                <div className="text-foreground/50 text-xs">
                  Readonly (Only Owner or Admin can edit)
                </div>
              )}
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                type="button"
                onClick={() => onOpenChange(false)}
              >
                Cancel
              </Button>
              <form.Subscribe selector={(state) => [state.canSubmit]}>
                {([canSubmit]) => (
                  <Button
                    type="submit"
                    disabled={
                      !canSubmit ||
                      isLoadingMutation ||
                      (isEditMode && !canEdit)
                    }
                  >
                    {submitButtonText}
                  </Button>
                )}
              </form.Subscribe>
            </div>
          </SheetFooter>
        </form>

        {/* Delete confirmation dialog */}
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Group</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the
              group "{group?.name}" and remove all associated permissions.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteGroup}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </div>
    </AlertDialog>
  );
}
