import { useMutation, useQueryClient, QueryKey } from '@tanstack/react-query';
import axiosInstanceUi from '@/lib/axiosInstance.ui';
import { API_CONFIG } from '@/config/api';
import { IGroupCreate } from '@/types/group';
import { toast } from 'sonner';

// Function to update an existing group via PATCH request
const updateGroup = async ({
  groupId,
  payload,
}: {
  groupId: string;
  payload: IGroupCreate;
}): Promise<void> => {
  // Using PATCH for partial updates
  await axiosInstanceUi.patch<void>(
    // Use the client-side proxy route
    `${API_CONFIG.admin.group}/${groupId}`,
    payload
  );
};

// Define query keys
const userListQueryKey: QueryKey = [API_CONFIG.admin.userList];
const groupListQueryKey: QueryKey = [API_CONFIG.admin.group];
const groupDetailQueryKey = (groupId: string): QueryKey => [
  API_CONFIG.admin.group,
  groupId,
];

export function useUpdateGroup({
  onSuccessCallback,
}: {
  onSuccessCallback?: () => void;
} = {}) {
  const queryClient = useQueryClient();

  return useMutation<
    void, // Return type
    Error, // Error type
    { groupId: string; payload: IGroupCreate } // Input type for mutationFn
  >({
    mutationFn: updateGroup,
    onSuccess: (_, variables) => {
      toast.success(`Group updated successfully!`);
      // Invalidate the user list query
      queryClient.invalidateQueries({ queryKey: userListQueryKey });
      // Invalidate the group list query
      queryClient.invalidateQueries({ queryKey: groupListQueryKey });
      // Invalidate the specific group query
      queryClient.invalidateQueries({
        queryKey: groupDetailQueryKey(variables.groupId),
      });

      // Call the success callback if provided
      onSuccessCallback?.();
    },
    onError: (error, variables) => {
      console.error(`Error updating group ${variables.groupId}:`, error);
      toast.error(`Error updating group: ${error.message}`);
    },
  });
}
