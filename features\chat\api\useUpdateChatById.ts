import { useMutation, useQueryClient, QueryKey } from '@tanstack/react-query';
import axiosInstanceUi from '@/lib/axiosInstance.ui';
import { API_CONFIG } from '@/config/api';
import { toast } from 'sonner';

// Function to update a chat by its ID
const updateChatById = async ({
  chatId,
  name,
}: UpdateChatInput): Promise<void> => {
  // Use the client-side instance that includes the auth token
  await axiosInstanceUi.patch(
    `${API_CONFIG.chat.chat}/${chatId}`,
    { name } // Send only the name field
  );
  // No return needed if successful
};

// Define the input type for the mutation
interface UpdateChatInput {
  chatId: string;
  name: string;
}

export function useUpdateChatById() {
  const queryClient = useQueryClient();

  return useMutation<void, Error, UpdateChatInput>({
    mutationFn: updateChatById,
    onSuccess: (data, variables) => {
      const { chatId, name } = variables;
      console.log(`Chat with ID ${chatId} renamed to "${name}" successfully.`);
      toast.success('Chat renamed successfully!');

      const chatListQueryKey: QueryKey = [API_CONFIG.chat.chat];
      const specificChatQueryKey: QueryKey = [API_CONFIG.chat.chat, chatId];

      // 1. Invalidate the chat list query to refresh the sidebar
      queryClient.invalidateQueries({ queryKey: chatListQueryKey });

      // 2. Invalidate the specific chat query to refresh the chat data
      queryClient.invalidateQueries({ queryKey: specificChatQueryKey });
    },
    onError: (error) => {
      console.error('Error renaming chat:', error);
      toast.error('Failed to rename chat.');
    },
  });
}
