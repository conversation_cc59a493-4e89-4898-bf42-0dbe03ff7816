import { contentProcessorRegistry } from './registry';
import { MermaidProcessor } from '../renderers/mermaid/mermaid-processor';
import { HtmlArtifactProcessor } from '../renderers/html/html-artifact-processor';
import { ChartJsProcessor } from '../renderers/chartjs/chartjs-processor';
import { NivoProcessor } from '../renderers/nivo/nivo-processor';
import { SyntaxHighlightProcessor } from '../renderers/highlight/syntax-highlight-processor';

// Register processors in order of specificity (most specific first)
contentProcessorRegistry.register(new NivoProcessor());
contentProcessorRegistry.register(new MermaidProcessor());
contentProcessorRegistry.register(new HtmlArtifactProcessor());
contentProcessorRegistry.register(new ChartJsProcessor());

// Important: Syntax highlighting should be last as it's the most generic one
contentProcessorRegistry.register(new SyntaxHighlightProcessor());

// Re-export types and registry
export * from './types';
export * from './registry';

// Export a convenient function for code processing
export const processCodeBlock = (
  code: string,
  lang?: string,
  options?: { theme?: string; isStreaming?: boolean }
) => {
  return contentProcessorRegistry.process({
    code,
    lang,
    theme: options?.theme,
    isStreaming: options?.isStreaming,
  });
};
