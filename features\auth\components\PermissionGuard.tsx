'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { usePermissions } from '@/hooks/usePermissions';
import { ResourceType, OperationType } from '@/lib/permissions/types';
import { useGlobal } from '@/contexts/GlobalContext';

interface PermissionGuardProps {
  children: React.ReactNode;
  resource: ResourceType;
  operation: OperationType;
  fallbackPath?: string;
}

/**
 * Component to protect routes based on user permissions
 * Redirects to fallbackPath if the user doesn't have the required permission
 */
export function PermissionGuard({
  children,
  resource,
  operation,
  fallbackPath = '/c',
}: PermissionGuardProps) {
  const router = useRouter();
  const { can } = usePermissions();
  const { store } = useGlobal();
  const { isLoadingUser } = store;

  const hasPermission = can(resource, operation);

  useEffect(() => {
    // Only redirect after we've loaded the user data
    if (!isLoadingUser && !hasPermission) {
      router.replace(fallbackPath);
    }
  }, [hasPermission, isLoadingUser, router, fallbackPath]);

  // If still loading, don't render anything
  if (isLoadingUser) {
    return null;
  }

  // If has permission, render children
  return hasPermission ? <>{children}</> : null;
}

/**
 * Component to protect admin routes
 * Redirects to fallbackPath if the user is not an admin
 */
export function AdminGuard({
  children,
  fallbackPath = '/c',
}: {
  children: React.ReactNode;
  fallbackPath?: string;
}) {
  return (
    <PermissionGuard resource="admin" operation="read" fallbackPath={fallbackPath}>
      {children}
    </PermissionGuard>
  );
}
