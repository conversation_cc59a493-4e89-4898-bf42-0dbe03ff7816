@import 'tailwindcss';
@import 'tw-animate-css';
@import './markdown.css';
@import './animations.css';
@import 'highlight.js/styles/github-dark.css';
/* @custom-variant dark (&:is(.dark *)); */

html,
body {
  overscroll-behavior: none;
  width: 100%;
  min-height: 100%;
}

/* Mobile-friendly styles */
@media screen and (max-width: 767px) {
  html,
  body {
    overflow-x: hidden; /* Prevent horizontal scrolling */
  }

  * {
    max-width: 100%; /* Prevent content from overflowing */
    box-sizing: border-box;
  }

  /* Ensure text is readable without zooming */
  /* input,
  select,
  textarea,
  button {
    font-size: 16px;
  } */
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: #147f6c;
  --chart-4: #ff9200;
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.16 0.03 264.66);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.16 0.03 264.66);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.16 0.03 264.66);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.23 0.034 264.665);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.23 0.034 264.665);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.23 0.034 264.665);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: #147f6c;
  --chart-4: #ff9200;
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  ul,
  ol {
    @apply ml-6 list-outside; /* indent again */
  }

  ul {
    @apply list-disc;
  }
  ol {
    @apply list-decimal;
  }
}

/* Artifact styling */
.artifact-preview {
  transition: all 0.2s ease-in-out;
}

.artifact-preview:hover {
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.artifact-content {
  width: 100%;
  height: 100%;
  overflow: auto;
}

/* Make sure HTML artifacts render properly in dark mode */
.dark .artifact-content {
  color-scheme: dark;
}

/* Ensure iframe artifacts have proper dimensions */
.artifact-content iframe,
iframe.w-full {
  width: 100%;
  min-height: 300px;
  border: 0;
  background-color: white;
}

/* Style for iframe in dark mode */
.dark iframe {
  background-color: #1a1a1a;
  color-scheme: dark;
}

/* Code highlighting for HTML artifacts */
pre.hljs,
pre.relative,
pre.github-dark {
  position: relative;
  padding: 1rem;
  border-radius: 0.5rem;
  margin-bottom: 2rem; /* Add space for the button */
}

pre.hljs code,
pre.relative code,
pre.github-dark code {
  font-family: var(--font-geist-mono);
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Ensure consistent github-dark theme */
.hljs.github-dark {
  background-color: #0d1117;
  color: #c9d1d9;
  &::before {
    display: none;
  }
}

/* Style for the Render HTML button */
.render-html-button {
  position: absolute;
  right: 0.4rem;
  bottom: 0.4rem;
  z-index: 50;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  padding: 0.25rem 0.75rem;
  border-radius: 0.25rem;
  background-color: hsl(var(--background));
  border: 1px solid hsl(var(--border));
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  pointer-events: all !important; /* Ensure the button is clickable */
}

.render-html-button:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
  color: hsl(var(--accent-foreground));
}

/* Ensure the Render HTML button is visible on dark backgrounds */
.dark .render-html-button {
  background-color: hsl(var(--background));
  border-color: hsl(var(--border));
}

.chart-hidden {
  display: none;
}

.nivo-hidden {
  display: none;
}
