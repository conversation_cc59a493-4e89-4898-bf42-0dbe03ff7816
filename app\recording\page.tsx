'use client';

import { useState } from 'react';
import { RecordingSidebar } from '@/features/recording/components/RecordingSidebar';
import { RecordingPlayer } from '@/features/recording/components/RecordingPlayer';
import { NewRecording } from '@/features/recording/components/NewRecording';
import { useSidebar } from '@/contexts/SidebarContext';

export default function RecordingPage() {
  const [selectedRecordingId, setSelectedRecordingId] = useState<string | null>(
    null
  );
  const [isNewRecording, setIsNewRecording] = useState(false);

  const { store: sidebarStore, setStore: setSidebarStore } = useSidebar();

  const handleSelectRecording = (recordingId: string) => {
    setSelectedRecordingId(recordingId);
    setIsNewRecording(false);
    // Collapse sidebar on mobile
    if (sidebarStore.isMobile) {
      setSidebarStore((prev: any) => ({
        ...prev,
        isOpen: false,
      }));
    }
  };

  const handleNewRecording = () => {
    setSelectedRecordingId(null);
    setIsNewRecording(true);
    // Collapse sidebar on mobile
    if (sidebarStore.isMobile) {
      setSidebarStore((prev: any) => ({
        ...prev,
        isOpen: false,
      }));
    }
  };

  const handleRecordingComplete = (recordingId: string) => {
    // When recording is completed, transition to show the completed recording
    setSelectedRecordingId(recordingId);
    setIsNewRecording(false);
  };

  return (
    <div className="relative flex h-screen">
      {/* Mobile: show overlay and sidebar absolutely, Desktop: normal sidebar */}
      {sidebarStore.isMobile && sidebarStore.isOpen && (
        <>
          {/* Backdrop, offset for sidebar */}
          <div
            className="fixed top-0 left-[60px] z-40 h-full"
            style={{
              width: 'calc(100vw - 60px)',
              background: 'rgba(0,0,0,0.4)',
            }}
            onClick={() =>
              setSidebarStore((prev: any) => ({
                ...prev,
                isOpen: false,
              }))
            }
          />
          {/* Sidebar overlay, offset for sidebar */}
          <div className="fixed top-0 left-[60px] z-50 h-full w-80">
            <RecordingSidebar
              selectedRecordingId={selectedRecordingId}
              onSelectRecording={handleSelectRecording}
              onNewRecording={handleNewRecording}
            />
          </div>
        </>
      )}
      {/* Desktop sidebar */}
      {!sidebarStore.isMobile && (
        <div className="h-full">
          <RecordingSidebar
            selectedRecordingId={selectedRecordingId}
            onSelectRecording={handleSelectRecording}
            onNewRecording={handleNewRecording}
          />
        </div>
      )}
      <div className="flex-1 overflow-hidden" style={{ paddingTop: '40px' }}>
        {isNewRecording ? (
          <NewRecording onRecordingComplete={handleRecordingComplete} />
        ) : selectedRecordingId ? (
          <RecordingPlayer recordingId={selectedRecordingId} />
        ) : (
          <div className="flex h-full items-center justify-center text-gray-500">
            Select a recording to play or start a new recording
          </div>
        )}
      </div>
    </div>
  );
}
