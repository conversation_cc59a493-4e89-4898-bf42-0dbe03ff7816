import { useMutation, useQueryClient, QueryKey } from '@tanstack/react-query';
import axiosInstanceUi from '@/lib/axiosInstance.ui';
import { API_CONFIG } from '@/config/api';
import { toast } from 'sonner';

// Function to delete a document by its ID
const deleteDocumentById = async ({
  collectionId,
  documentId,
}: {
  collectionId: string;
  documentId: string;
}): Promise<void> => {
  await axiosInstanceUi.delete(
    `${API_CONFIG.workspace.collection}/${collectionId}/documents/${documentId}`
  );
};

// Define the query key for the collection detail (to invalidate after deletion)
const getCollectionDetailQueryKey = (collectionId: string): QueryKey => [
  API_CONFIG.workspace.collection,
  collectionId,
];

export function useDocumentDeleteById(collectionId: string) {
  const queryClient = useQueryClient();

  return useMutation<void, Error, { documentId: string }>({
    mutationFn: ({ documentId }) =>
      deleteDocumentById({ collectionId, documentId }),
    onSuccess: (_, variables) => {
      toast.success('Document deleted successfully', {
        description: 'The document has been removed from the collection.',
      });

      // Invalidate the collection detail query to refresh the documents list
      queryClient.invalidateQueries({
        queryKey: getCollectionDetailQueryKey(collectionId),
      });
    },
    onError: (error) => {
      console.error('Error deleting document:', error);
      toast.error('Failed to delete document', {
        description:
          error.message || 'An error occurred while deleting the document.',
      });
    },
  });
}
