'use client';

import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>ontent,
  CardFooter,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ip<PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { IModelData, ModelTypeEnum } from '@/types/model';
import { Hammer, Users } from 'lucide-react';
import { ModelAvatar } from '@/components/ui/model-avatar';

interface ModelCardProps {
  model: IModelData;
  onEdit: (model: IModelData) => void;
}

const ModelCard: React.FC<ModelCardProps> = ({ model, onEdit }) => (
  <TooltipProvider>
    <Card
      className="group hover:bg-muted/50 relative gap-2 rounded-md py-4 shadow-none hover:shadow"
      key={model.id}
    >
      {model.type === ModelTypeEnum.MODEL && (
        <Badge className="absolute top-2 right-2 border-none border-transparent bg-gradient-to-r from-blue-800 to-indigo-500 px-2 py-0.5 text-xs font-semibold text-white">
          Base
        </Badge>
      )}
      <CardHeader className="px-4">
        {model.description ? (
          <Tooltip delayDuration={0}>
            <TooltipTrigger asChild>
              <div className="flex items-center gap-3">
                <ModelAvatar image={model.image} name={model.name} size="lg" />
                <CardTitle className="text-base font-bold">
                  {model.name}
                </CardTitle>
              </div>
            </TooltipTrigger>
            <TooltipContent className="max-w-xs">
              <p className="text-sm">{model.description}</p>
            </TooltipContent>
          </Tooltip>
        ) : (
          <div className="flex items-center gap-3">
            <ModelAvatar image={model.image} name={model.name} size="lg" />
            <CardTitle className="text-base font-bold">{model.name}</CardTitle>
          </div>
        )}
      </CardHeader>
      <CardContent className="flex flex-1 flex-col justify-between gap-4 px-4">
        <div className="flex flex-wrap gap-1.5">
          {model.tools && model.tools.length > 0 && (
            <Tooltip delayDuration={0}>
              <TooltipTrigger asChild>
                <Badge
                  variant="outline"
                  className="flex items-center gap-2 bg-slate-50/10 text-xs"
                >
                  <Hammer size={10} />
                  {model.tools.length === 1
                    ? model.tools[0].name
                    : `${model.tools.length} Tools`}
                </Badge>
              </TooltipTrigger>
              <TooltipContent className="max-w-xs">
                <div className="space-y-1">
                  <p className="text-xs font-medium">Tools:</p>
                  {model.tools.map((tool) => (
                    <div key={tool.id} className="flex items-center gap-2">
                      <Hammer size={10} />
                      <span className="text-xs">{tool.name}</span>
                    </div>
                  ))}
                </div>
              </TooltipContent>
            </Tooltip>
          )}

          {model.groups && model.groups.length > 0 && (
            <Tooltip delayDuration={0}>
              <TooltipTrigger asChild>
                <Badge
                  variant="outline"
                  className="flex items-center gap-2 bg-slate-50/10 text-xs"
                >
                  <Users size={10} />
                  {model.groups.length === 1
                    ? model.groups[0].name
                    : `${model.groups.length} Groups`}
                </Badge>
              </TooltipTrigger>
              <TooltipContent className="max-w-xs">
                <div className="space-y-1">
                  <p className="text-xs font-medium">Groups:</p>
                  {model.groups.map((group) => (
                    <div key={group.id} className="flex items-center gap-2">
                      <Users size={10} />
                      <span className="text-xs">{group.name}</span>
                    </div>
                  ))}
                </div>
              </TooltipContent>
            </Tooltip>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex h-[30px] justify-between p-4">
        <span className="text-muted-foreground text-xs">
          {model.owner_name || 'Anonymous'}
        </span>
        {model.type !== ModelTypeEnum.MODEL && (
          <Button
            className="block md:hidden md:group-hover:block"
            variant="outline"
            size="sm"
            onClick={() => onEdit(model)}
          >
            Edit
          </Button>
        )}
      </CardFooter>
    </Card>
  </TooltipProvider>
);

export default ModelCard;
