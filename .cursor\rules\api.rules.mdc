---
description: 
globs: 
alwaysApply: false
---
Rule Name: API Route Proxying
Description: 
1.  **Purpose:** To securely forward requests originating from the UI (client-side) to the backend API service. This pattern centralizes backend communication and allows the Next.js server to handle authentication or add necessary headers.
2.  **Location:** Implement these proxy routes within the `app/api/` directory structure (e.g., `app/api/chat/route.ts`, `app/api/models/route.ts`).
3.  **Implementation:**
    *   Use Next.js API Route Handlers (`GET`, `POST`, etc.).
    *   Extract necessary information (like `Authorization` headers or query parameters) from the incoming `NextRequest`.
    *   Utilize the **API-specific Axios instance** (`@/lib/axiosInstance.api.ts`) to make the actual call to the target backend service endpoint (defined in `API_CONFIG`).
    *   Forward the relevant headers (especially `Authorization`) and payload/query parameters to the backend call.
    *   Return the response (or error) from the backend service using `NextResponse`.
4.  **Simplicity:** Keep proxy logic minimal. Primarily focus on request forwarding and authentication handling. Avoid embedding complex business logic within the proxy route itself.
5.  **Reference Example:** See `app/api/chat/route.ts` for a standard implementation of a GET request proxy.
