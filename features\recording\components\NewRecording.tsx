'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Mic, Square, Pause, Play } from 'lucide-react';
import { useCreateRecording } from '../api/useRecordings';
import { TranscriptionService } from '@/services/transcription.service';
import { AudioRecorderService } from '@/services/audio-recorder.service';
import { RecordingService } from '@/services/recording.service';
import { getSpeechToken } from '@/features/stt/api/useGetSpeechToken';
import { getWebSocketUrl } from '@/lib/api';
import { ScrollArea } from '@/components/ui/scroll-area';

interface TranscriptItem {
  speaker_id: string;
  text: string;
  timestamp: Date;
}

interface NewRecordingProps {
  onRecordingComplete?: (recordingId: string) => void;
}

enum RecordingState {
  IDLE = 'idle',
  STARTING = 'starting',
  RECORDING = 'recording',
  PAUSED = 'paused',
  STOPPING = 'stopping',
  COMPLETED = 'completed',
  ERROR = 'error',
}

export function NewRecording({ onRecordingComplete }: NewRecordingProps) {
  // State management
  const [recordingName, setRecordingName] = useState('');
  const [recordingState, setRecordingState] = useState<RecordingState>(
    RecordingState.IDLE
  );
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [transcript, setTranscript] = useState<TranscriptItem[]>([]);
  const [recordingId, setRecordingId] = useState<string | null>(null);
  const [statusMessage, setStatusMessage] = useState('Ready to record');

  // API and service refs
  const createRecording = useCreateRecording();
  const audioRecorderRef = useRef<AudioRecorderService | null>(null);
  const transcriptionServiceRef = useRef<TranscriptionService | null>(null);
  const recordingServiceRef = useRef<RecordingService | null>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Computed states
  const isRecording = recordingState === RecordingState.RECORDING;
  const isPaused = recordingState === RecordingState.PAUSED;
  const isActive = isRecording || isPaused;
  const canStart =
    recordingState === RecordingState.IDLE && recordingName.trim() !== '';
  const canPause = recordingState === RecordingState.RECORDING;
  const canResume = recordingState === RecordingState.PAUSED;
  const canStop = isActive;

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      cleanupServices();
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  // Timer management
  const startTimer = useCallback(() => {
    if (timerRef.current) clearInterval(timerRef.current);
    timerRef.current = setInterval(() => {
      setRecordingDuration((prev) => prev + 1);
    }, 1000);
  }, []);

  const stopTimer = useCallback(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  }, []);

  // Service management
  const cleanupServices = useCallback(() => {
    transcriptionServiceRef.current?.dispose();
    audioRecorderRef.current?.dispose();
    recordingServiceRef.current?.dispose();
    transcriptionServiceRef.current = null;
    audioRecorderRef.current = null;
    recordingServiceRef.current = null;
  }, []);

  const stopServices = useCallback(() => {
    transcriptionServiceRef.current?.stop();
    audioRecorderRef.current?.stopRecording();
  }, []);

  // Error handling
  const handleError = useCallback(
    (error: string) => {
      console.error('Recording error:', error);
      setRecordingState(RecordingState.ERROR);
      setStatusMessage(`Error: ${error}`);
      stopServices();
      stopTimer();
    },
    [stopServices, stopTimer]
  );

  // Service initialization
  const initializeServices = useCallback(
    async (recordingId: string) => {
      try {
        const speechToken = await getSpeechToken();

        // Initialize Recording WebSocket Service
        recordingServiceRef.current = new RecordingService({
          endpoint: getWebSocketUrl(`/api/recording/stream/${recordingId}`),
          onError: handleError,
        });

        // Initialize transcription service
        transcriptionServiceRef.current = new TranscriptionService(
          speechToken.token,
          speechToken.region,
          (sender, event) => {
            // Interim results - don't send to backend
            console.log('Transcribing:', event.result.text);
          },
          (sender, event) => {
            if (!event.result?.text) return;

            // Final results - send to backend
            const transcriptItem = {
              speaker_id: event.result.speakerId,
              text: event.result.text,
              timestamp: new Date(),
            };

            setTranscript((prev) => [...prev, transcriptItem]);

            // Send to backend via Recording Service
            recordingServiceRef.current
              ?.sendTranscription({
                speaker_id: event.result.speakerId,
                speaker_name: `Speaker ${event.result.speakerId}`,
                text: event.result.text,
                timestamp: new Date().toISOString(),
              })
              .catch((error) => {
                console.error('Failed to send transcription:', error);
              });
          }
        );

        // Initialize audio recorder
        audioRecorderRef.current = new AudioRecorderService(
          '', // Empty URL since we're using the callback pattern
          (audioData: ArrayBuffer) => {
            recordingServiceRef.current
              ?.sendAudioData(audioData)
              .catch((error) => {
                console.error('Failed to send audio data:', error);
                handleError('Failed to send audio data');
              });
          }
        );
      } catch (error) {
        throw new Error(`Failed to initialize services: ${error}`);
      }
    },
    [handleError]
  );

  // Recording actions
  const handleStart = useCallback(async () => {
    if (!canStart) return;

    try {
      setRecordingState(RecordingState.STARTING);
      setStatusMessage('Creating recording...');

      // Create recording in backend
      const result = await createRecording.mutateAsync({ name: recordingName });
      setRecordingId(result.id);

      setStatusMessage('Initializing services...');
      await initializeServices(result.id);

      setStatusMessage('Starting recording...');

      // Start all services
      await recordingServiceRef.current?.startRecording();
      await transcriptionServiceRef.current?.start();
      await audioRecorderRef.current?.startRecording();

      setRecordingState(RecordingState.RECORDING);
      setStatusMessage('Recording in progress');
      setRecordingDuration(0);
      startTimer();
    } catch (error) {
      console.error('Failed to start recording:', error);
      handleError('Failed to start recording');
    }
  }, [
    canStart,
    recordingName,
    createRecording,
    initializeServices,
    startTimer,
    handleError,
  ]);

  const handlePause = useCallback(async () => {
    if (!canPause) return;

    try {
      setRecordingState(RecordingState.PAUSED);
      setStatusMessage('Recording paused');

      // Stop services but don't dispose them
      stopServices();
      stopTimer();
    } catch (error) {
      console.error('Failed to pause recording:', error);
      handleError('Failed to pause recording');
    }
  }, [canPause, stopServices, stopTimer, handleError]);

  const handleResume = useCallback(async () => {
    if (!canResume || !recordingId) return;

    try {
      setStatusMessage('Resuming recording...');

      // Clean up old services and reinitialize
      cleanupServices();
      await initializeServices(recordingId);

      // Start all services again
      await recordingServiceRef.current?.startRecording();
      await transcriptionServiceRef.current?.start();
      await audioRecorderRef.current?.startRecording();

      setRecordingState(RecordingState.RECORDING);
      setStatusMessage('Recording in progress');
      startTimer();
    } catch (error) {
      console.error('Failed to resume recording:', error);
      handleError('Failed to resume recording');
    }
  }, [
    canResume,
    recordingId,
    cleanupServices,
    initializeServices,
    startTimer,
    handleError,
  ]);

  const handleStop = useCallback(async () => {
    if (!canStop) return;

    try {
      setRecordingState(RecordingState.STOPPING);
      setStatusMessage('Stopping recording...');

      // Stop all services
      stopServices();
      await recordingServiceRef.current?.stopRecording();

      setRecordingState(RecordingState.COMPLETED);
      setStatusMessage('Recording completed');
      stopTimer();

      // Transition to the completed recording view
      if (recordingId && onRecordingComplete) {
        onRecordingComplete(recordingId);
      }
    } catch (error) {
      console.error('Failed to stop recording:', error);
      handleError('Error stopping recording');
    }
  }, [
    canStop,
    stopServices,
    stopTimer,
    recordingId,
    onRecordingComplete,
    handleError,
  ]);

  // Utility functions
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getStatusColor = () => {
    switch (recordingState) {
      case RecordingState.RECORDING:
        return 'text-red-500';
      case RecordingState.PAUSED:
        return 'text-yellow-500';
      case RecordingState.ERROR:
        return 'text-red-600';
      case RecordingState.COMPLETED:
        return 'text-green-600';
      default:
        return 'text-gray-700';
    }
  };

  return (
    <div className="flex h-full flex-col">
      <div className="border-b border-gray-200 p-6">
        <h1 className="mb-6 text-2xl font-bold">New Recording</h1>

        <div className="space-y-6">
          <div>
            <label className="mb-2 block text-sm font-medium">
              Recording Name
            </label>
            <Input
              value={recordingName}
              onChange={(e) => setRecordingName(e.target.value)}
              placeholder="Enter recording name..."
              disabled={isActive}
              className="max-w-md"
            />
          </div>

          <div className="flex items-center gap-4">
            {recordingState === RecordingState.IDLE ? (
              <Button
                onClick={handleStart}
                className="bg-green-500 hover:bg-green-600"
                disabled={!canStart}
              >
                <Mic className="mr-2" size={16} />
                Start Recording
              </Button>
            ) : (
              <>
                {isRecording && (
                  <Button onClick={handlePause} variant="outline">
                    <Pause className="mr-2" size={16} />
                    Pause
                  </Button>
                )}

                {isPaused && (
                  <Button onClick={handleResume} variant="outline">
                    <Play className="mr-2" size={16} />
                    Resume
                  </Button>
                )}

                <Button
                  onClick={handleStop}
                  variant="destructive"
                  disabled={!canStop}
                >
                  <Square className="mr-2" size={16} />
                  Stop
                </Button>
              </>
            )}
          </div>

          <div className="text-center">
            <p className={`text-lg font-medium ${getStatusColor()}`}>
              {statusMessage}
            </p>
            {isActive && (
              <p className="mt-2 font-mono text-xl text-red-500">
                {formatDuration(recordingDuration)}
              </p>
            )}
          </div>
        </div>
      </div>

      {/* Live Transcript */}
      <div className="flex-1 overflow-hidden">
        <div className="p-6">
          <h2 className="mb-4 text-lg font-semibold">Live Transcript</h2>

          {transcript.length === 0 ? (
            <div className="py-8 text-center text-gray-500">
              {isActive
                ? 'Transcript will appear here as you speak...'
                : 'No transcript yet'}
            </div>
          ) : (
            <ScrollArea className="h-96">
              <div className="space-y-4">
                {transcript.map((item, index) => (
                  <div key={index} className="border-l-4 border-green-200 pl-4">
                    <div className="mb-1 flex items-center gap-2">
                      <span className="text-sm font-medium text-green-600">
                        Speaker {item.speaker_id}
                      </span>
                      <span className="text-xs text-gray-500">
                        {item.timestamp.toLocaleTimeString()}
                      </span>
                    </div>
                    <p className="text-gray-800">{item.text}</p>
                  </div>
                ))}
              </div>
            </ScrollArea>
          )}
        </div>
      </div>
    </div>
  );
}
