'use client';

import React, { useMemo, useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useFetchGroupedModels } from '@/features/workspace/api/useFetchModels';
import LoadingSpinner from '@/components/LoadingSpinner';
import { PlusIcon } from 'lucide-react';
import ModelSheet from '@/features/workspace/components/model/ModelSheet';
import ModelCard from '@/features/workspace/components/model/ModelCard';
import { IModelData, ModelTypeEnum } from '@/types/model';

const ModelPage = () => {
  const { data: groupedModels, isLoading: isLoadingModels } =
    useFetchGroupedModels();

  const filteredGroupedModels = useMemo(() => {
    if (!groupedModels) return [];
    return groupedModels.filter(
      (group) => group.type === ModelTypeEnum.ASSISTANT
    );
  }, [groupedModels]);

  const [isModelFormOpen, setIsModelFormOpen] = useState(false);
  const [modelToEdit, setModelToEdit] = useState<IModelData | null>(null);

  if (isLoadingModels) {
    return (
      <div className="flex items-center p-8">
        <LoadingSpinner />
      </div>
    );
  }

  const handleCreateModel = () => {
    setModelToEdit(null);
    setIsModelFormOpen(true);
  };

  const handleEditModel = (model: IModelData) => {
    setModelToEdit(model);
    setIsModelFormOpen(true);
  };

  return (
    <div className="tw-relative w-full">
      <Button
        className="absolute top-4 right-4"
        variant="ghost"
        onClick={handleCreateModel}
        size="sm"
      >
        <PlusIcon size={16} />
        <span className="hidden sm:block">Agent</span>
      </Button>

      {filteredGroupedModels?.map((group) => (
        <div key={group.type} className="mb-8">
          {/* <h2 className="mb-4 text-xl font-semibold">{group.name}</h2> */}
          <div className="grid w-full gap-4 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4 2xl:grid-cols-5">
            {group.data.map((model) => (
              <ModelCard
                key={model.id}
                model={model}
                onEdit={handleEditModel}
              />
            ))}

            {/* Add the "Create Model" card only after the last group */}
            {group.type === ModelTypeEnum.ASSISTANT && (
              <Card
                className="hover:bg-foreground/5 border-foreground-200 cursor-pointer rounded-md border-dashed shadow-none"
                onClick={handleCreateModel}
              >
                <CardContent className="flex h-full items-center justify-center">
                  <PlusIcon color="gray" size={20} />
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      ))}

      {isModelFormOpen && (
        <ModelSheet
          isOpen={isModelFormOpen}
          onOpenChange={setIsModelFormOpen}
          model={modelToEdit}
        />
      )}
    </div>
  );
};

export default ModelPage;
