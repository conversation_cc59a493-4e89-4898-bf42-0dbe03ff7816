import { useQuery } from '@tanstack/react-query';
import axiosInstanceUi from '@/lib/axiosInstance.ui';
import { API_CONFIG } from '@/config/api';
import { IUserData } from '@/types/user';
import Cookies from 'js-cookie';

// Function to fetch the current user data
const fetchMe = async (): Promise<IUserData> => {
  // Only fetch if we have a token
  const token = Cookies.get('token');
  if (!token) {
    throw new Error('No authentication token available');
  }

  const response = await axiosInstanceUi.get(API_CONFIG.admin.userMe);

  if (!response.data) {
    throw new Error('No user data returned');
  }
  return response.data;
};

export function useFetchMe() {
  const token = Cookies.get('token');

  return useQuery<IUserData, Error>({
    queryKey: [API_CONFIG.admin.userMe],
    queryFn: fetchMe,
    // Only fetch if we have a token
    enabled: !!token,
    // Don't refetch on window focus to avoid unnecessary requests
    refetchOnWindowFocus: false,
    // Only retry once if the request fails
    retry: 1,
    // Consider data stale after 5 minutes
    staleTime: 5 * 60 * 1000,
  });
}
