'use client'; // Ensure this is a client component

import React, { useState, useEffect, useRef } from 'react';
import { useFetchSharedChat } from '@/features/chat/api/useFetchSharedChat';
import ChatMessage from './ChatMessage';
import { cn } from '@/lib/utils';
import { IMessage } from '@/types/chat';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import { Loader2, Home } from 'lucide-react';

export default function ChatShare() {
  const params = useParams();
  // Support both parameter names: chatToken (from c/share/[chatToken]) and token (from share/[token])
  const chatToken = (params.chatToken || params.token) as string;
  const { data: chatData, isLoading, isError } = useFetchSharedChat(chatToken);
  const [messages, setMessages] = useState<IMessage[]>([]);
  const [chatId, setChatId] = useState<string>('');
  const [chatName, setChatName] = useState<string>('');
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom on initial load
  useEffect(() => {
    if (scrollAreaRef.current && messages.length > 0 && !isLoading) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages, isLoading]);

  // Process chat data when it's loaded
  useEffect(() => {
    if (chatData) {
      // Set chat ID and name
      setChatId(chatData.id || '');
      setChatName(chatData.name || 'Shared Chat');

      // Format messages
      const formattedMessages: IMessage[] = chatData.messages.map(
        (message: any) => {
          return {
            ...message,
            content: message.content || '',
            attachments: message.attachments || [],
            ts: message.ts as number,
          };
        }
      );

      setMessages(formattedMessages);
    }
  }, [chatData]);

  if (isError) {
    return (
      <div className="flex h-full w-full flex-col">
        <div className="bg-background flex items-center justify-between border-b px-4 py-3">
          <div className="flex-1">
            <Link
              href="/"
              className="text-muted-foreground hover:text-foreground hover:bg-accent inline-flex items-center gap-2 rounded-md px-3 py-1 text-sm transition-colors"
            >
              <Home size={16} />
              <span>Home</span>
            </Link>
          </div>
          <h1 className="flex-1 text-center text-xl font-semibold">
            Shared Chat
          </h1>
          <div className="flex-1"></div>
        </div>

        <div className="flex flex-1 items-center justify-center">
          <div className="text-center">
            <h2 className="text-2xl font-bold">Chat not found</h2>
            <p className="text-muted-foreground mt-2">
              The shared chat you're looking for doesn't exist or has been
              removed.
            </p>
            <div className="mt-6">
              <Link
                href="/"
                className="bg-primary text-primary-foreground hover:bg-primary/90 inline-flex items-center gap-2 rounded-md px-4 py-2 text-sm font-medium transition-colors"
              >
                <Home size={16} />
                <span>Return to Home</span>
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-full w-full flex-col">
      {chatName && (
        <div className="bg-background flex items-center justify-between border-b px-4 py-3">
          <div className="flex-1">
            <Link
              href="/"
              className="text-muted-foreground hover:text-foreground hover:bg-accent inline-flex items-center gap-2 rounded-md px-3 py-1 text-sm transition-colors"
            >
              <Home size={16} />
              <span>Home</span>
            </Link>
          </div>
          <h1 className="flex-1 text-center text-xl font-semibold">
            {chatName}
          </h1>
          <div className="flex-1"></div>
        </div>
      )}

      <div ref={scrollAreaRef} className="flex-1 overflow-auto px-4 py-[60px]">
        <div className={cn('flex w-full flex-1 justify-center')}>
          <div className="flex w-full max-w-[860px] flex-col gap-y-6 px-2 sm:px-0">
            {isLoading && messages.length === 0 ? (
              <div className="flex flex-col items-center justify-center gap-4 py-10">
                <Loader2 className="text-foreground/30 h-8 w-8 animate-spin" />
                <p className="text-muted-foreground text-sm">
                  Loading shared chat...
                </p>
              </div>
            ) : (
              messages.map((message, index) => (
                <ChatMessage
                  chatId={chatId}
                  key={`${message.id}-${index}`}
                  message={message}
                  isLoading={false}
                  isStreaming={false}
                  useFeedback={false}
                />
              ))
            )}

            {isLoading && messages.length > 0 && (
              <div className="flex items-center justify-center gap-2 px-2">
                <Loader2 className="text-foreground/30 animate-spin" />
                <span className="text-muted-foreground text-sm">
                  Loading...
                </span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
