import { NextRequest, NextResponse } from 'next/server';
import axiosInstanceApi from '@/lib/axiosInstance.api';
import { API_CONFIG } from '@/config/api';

export async function GET(request: NextRequest) {
  const authorization = request.headers.get('Authorization');
  const targetPath = API_CONFIG.chat.model; // The path on your backend service

  // Forward query parameters
  const { searchParams } = new URL(request.url);

  try {
    console.log(
      `[API Route Proxy] Forwarding GET ${targetPath} with Auth: ${!!authorization}`
    );

    const apiResponse = await axiosInstanceApi.get(targetPath, {
      headers: {
        ...(authorization && { Authorization: authorization }),
      },
      params: searchParams, // Forward query parameters
    });

    // Return the response from the backend service
    return NextResponse.json(apiResponse.data, { status: apiResponse.status });
  } catch (error: any) {
    console.error(
      `[API Route Proxy Error] GET ${targetPath}:`,
      error.response?.data || error.message
    );
    const status = error.response?.status || 500;
    const message = error.response?.data?.message || 'Proxy error';
    return NextResponse.json({ message }, { status });
  }
}

export async function POST(request: NextRequest) {
  const authorization = request.headers.get('Authorization');
  const targetPath = API_CONFIG.chat.model; // Assuming the same base path

  try {
    const body = await request.json();

    console.log(
      `[API Route Proxy] Forwarding POST ${targetPath} with Auth: ${!!authorization}`
    );

    // Forward the POST request to the backend API
    const apiResponse = await axiosInstanceApi.post(
      targetPath,
      body, // Send the parsed body data
      {
        headers: {
          ...(authorization && { Authorization: authorization }),
        },
      }
    );

    // Return the response from the backend service (e.g., the newly created model)
    return NextResponse.json(apiResponse.data, { status: apiResponse.status });
  } catch (error: any) {
    // Handle other errors (e.g., network, backend errors)
    console.error(
      `[API Route Proxy Error] POST ${targetPath}:`,
      error.response?.data || error.message
    );

    const status = error.response?.status || 500;
    const message =
      error.response?.data?.detail || // Check for 'detail' common in some frameworks
      error.response?.data?.message ||
      'Proxy error during model creation';
    return NextResponse.json({ message }, { status });
  }
}
