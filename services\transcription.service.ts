import * as SpeechSDK from 'microsoft-cognitiveservices-speech-sdk';
import { ConversationTranscriptionResult } from 'microsoft-cognitiveservices-speech-sdk';

export class TranscriptionService {
  private onTranscribing?: (
    sender: Speech<PERSON><PERSON>.Recognizer,
    event: SpeechSDK.ConversationTranscriptionEventArgs
  ) => void;
  private onTranscribed?: (
    sender: SpeechSDK.Recognizer,
    event: SpeechSDK.ConversationTranscriptionEventArgs
  ) => void;
  private transcriber: SpeechSDK.ConversationTranscriber;

  constructor(
    token: string,
    region: string,
    onTranscribing: (
      sender: SpeechSDK.Recognizer,
      event: SpeechSDK.ConversationTranscriptionEventArgs
    ) => void,
    onTranscribed: (
      sender: SpeechSDK.Recognizer,
      event: SpeechSDK.ConversationTranscriptionEventArgs
    ) => void
  ) {
    this.onTranscribing = onTranscribing;
    this.onTranscribed = onTranscribed;

    const speechConfig = SpeechSDK.SpeechConfig.fromAuthorizationToken(
      token,
      region
    );
    speechConfig.setProfanity(SpeechSDK.ProfanityOption.Raw);
    speechConfig.speechRecognitionLanguage = 'en-US';
    const audioConfig = SpeechSDK.AudioConfig.fromDefaultMicrophoneInput();

    this.transcriber = new SpeechSDK.ConversationTranscriber(
      speechConfig,
      audioConfig
    );

    this.transcriber.transcribing = this.onTranscribing;
    this.transcriber.transcribed = this.onTranscribed;

    this.transcriber.canceled = (s, e) => {
      console.error(e);
    };
  }

  public start() {
    this.transcriber.startTranscribingAsync(
      () => {
        console.log('Successfully started transcription');
      },
      (err) => {
        console.error(err);
      }
    );
  }

  public stop() {
    this.transcriber.stopTranscribingAsync();
  }

  public dispose() {
    if (this.transcriber) {
      this.transcriber.stopTranscribingAsync(() => {
        this.transcriber?.close();
      });
    }
  }
}
