export type PositiveFeedbackReason =
  | 'accurate_information'
  | 'followed_instructions'
  | 'showcased_creativity'
  | 'positive_attitude'
  | 'attention_to_detail'
  | 'thorough_explanation'
  | 'other';

export type NegativeFeedbackReason =
  | 'dont_like_style'
  | 'too_verbose'
  | 'not_helpful'
  | 'not_factually_correct'
  | 'didnt_follow_instructions'
  | 'refused_when_shouldnt'
  | 'being_lazy'
  | 'other';

export type FeedbackReason = PositiveFeedbackReason | NegativeFeedbackReason;

export enum FeedbackTypeEnum {
  POSITIVE = 'positive',
  NEGATIVE = 'negative',
}

export interface IFeedbackData {
  type?: FeedbackTypeEnum;
  reasons?: FeedbackReason[];
  comment?: string;
}
