import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Define protected paths and their required permissions
const PROTECTED_PATHS = [
  {
    path: '/admin',
    resource: 'admin',
    operation: 'read',
  },
  // Add more protected paths as needed
];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Check if the path is protected
  const protectedPath = PROTECTED_PATHS.find(
    (p) => pathname.startsWith(p.path)
  );

  if (protectedPath) {
    // For client-side protection, we'll let the PermissionGuard component handle it
    // This middleware is just an additional layer of security

    // You could implement server-side token validation here if needed
    // For now, we'll rely on client-side protection
  }

  // Create response and add headers for Teams iframe support
  const response = NextResponse.next();

  // Add headers to allow iframe embedding in Microsoft Teams
  response.headers.set('X-Frame-Options', 'ALLOWALL');
  response.headers.set(
    'Content-Security-Policy',
    "frame-ancestors 'self' https://*.teams.microsoft.com https://*.teams.office.com https://*.office.com https://*.microsoft.com https://*.microsoftonline.com;"
  );

  return response;
}

// Configure the paths that trigger this middleware
export const config = {
  matcher: [
    '/admin/:path*',
    // Add more protected paths as needed
  ],
};
