'use client';

import React, { useState } from 'react';
import { useFetchTools } from '@/features/workspace/api/useFetchTools';
import LoadingSpinner from '@/components/LoadingSpinner';
import ToolCard from '@/features/workspace/components/tool/ToolCard';
import ToolSheet from '@/features/workspace/components/tool/ToolSheet';
import { IToolData } from '@/types/model';

const ToolPage = () => {
  const { data: tools, isLoading } = useFetchTools();
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const [selectedTool, setSelectedTool] = useState<IToolData | null>(null);

  if (isLoading) {
    return (
      <div className="flex items-center p-8">
        <LoadingSpinner />
      </div>
    );
  }

  const handleEditTool = (tool: IToolData) => {
    setSelectedTool(tool);
    setIsSheetOpen(true);
  };

  return (
    <div className="w-full space-y-4">
      <p className="text-muted-foreground text-sm">
        Tools are managed by the Business Technology team. Available tools will
        automatically appear here for you to use in your agents.
      </p>

      <div className="grid w-full gap-4 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5">
        {tools?.map((tool) => (
          <ToolCard key={tool.id} tool={tool} onEdit={handleEditTool} />
        ))}
      </div>

      {(!tools || tools.length === 0) && (
        <div className="text-muted-foreground mt-8 text-center">
          No tools are currently available. Please contact the BT team if you
          need specific tools.
        </div>
      )}

      {isSheetOpen && selectedTool && (
        <ToolSheet
          isOpen={isSheetOpen}
          onOpenChange={setIsSheetOpen}
          tool={selectedTool}
        />
      )}
    </div>
  );
};

export default ToolPage;
