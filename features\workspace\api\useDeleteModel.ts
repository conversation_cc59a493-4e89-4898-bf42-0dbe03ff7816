import { useMutation, useQueryClient, QueryKey } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import axiosInstanceUi from '@/lib/axiosInstance.ui';
import { API_CONFIG } from '@/config/api';
import { toast } from 'sonner';

// Function to delete a model by its ID
const deleteModelById = async (modelId: string): Promise<void> => {
  await axiosInstanceUi.delete(`${API_CONFIG.chat.model}/${modelId}`);
};

// Define the query key for the model list
const modelListQueryKey: QueryKey = [API_CONFIG.chat.model];

export function useDeleteModel({
  onSuccessCallback,
}: {
  onSuccessCallback?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  const router = useRouter();

  return useMutation<void, Error, string>({
    mutationFn: (modelId) => deleteModelById(modelId),
    onSuccess: (_, modelId) => {
      console.log(`Model with ID ${modelId} deleted successfully.`);
      toast.success('Model deleted successfully!');

      // Invalidate the model list query to refresh the UI
      queryClient.invalidateQueries({ queryKey: modelListQueryKey });

      // Remove the specific model query data from the cache
      queryClient.removeQueries({
        queryKey: [API_CONFIG.chat.model, modelId],
      });

      // Call the success callback if provided
      onSuccessCallback?.();
    },
    onError: (error) => {
      console.error('Error deleting model:', error);
      toast.error(`Failed to delete model: ${error.message}`);
    },
  });
}
