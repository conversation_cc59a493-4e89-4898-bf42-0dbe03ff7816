import { IMessage } from '@/types/chat';
import React, { createContext, useContext, useState, useEffect } from 'react';
// import { useAuth } from '@/contexts/AuthContext';
// import { CHAT_SERVICE_URL, MethodEnum } from '@/lib/api';

export interface IChat {
  id: string;
  name: string;
  //   modifiedAt: string;
}

export interface IModel {
  id: string;
  name: string;
  type: string;
  description: string;
  base_model: string;
}

interface IChuckStore {
  chats: IChat[];
  modelId: string | null;
  newChatMessage: IMessage | null;
  setModelId: (id: string) => void;
}

interface IChuckContext {
  store: IChuckStore;
  setStore: React.Dispatch<React.SetStateAction<IChuckStore>>;
}

const ChuckContext = createContext<IChuckContext | null>(null);

const ChuckContextProvider = ({ children }: { children: React.ReactNode }) => {
  const [store, setStore] = useState<IChuckStore>({
    chats: [],
    modelId: null,
    newChatMessage: null,
    setModelId: (id: string) => {
      setStore((prevStore) => ({
        ...prevStore,
        modelId: id,
      }));
    },
  });

  return (
    <ChuckContext.Provider value={{ store, setStore }}>
      {children}
    </ChuckContext.Provider>
  );
};

export const useChuckContext = () => {
  return useContext(ChuckContext) as IChuckContext;
};

export default ChuckContextProvider;
