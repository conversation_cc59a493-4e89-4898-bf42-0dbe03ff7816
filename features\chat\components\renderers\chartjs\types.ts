import { Chart } from 'chart.js';

export interface ChartRenderOptions {
  container: HTMLElement;
  theme: string | undefined;
  instanceRef: React.MutableRefObject<Map<string, Chart>>;
  isStreaming?: boolean;
}

export interface ThemeOptions {
  gridColor: string;
  fontColor: string;
}

export interface ChartElementWithConfig extends HTMLElement {
  getAttribute(name: string): string | null;
  innerHTML: string;
  id: string;
}
