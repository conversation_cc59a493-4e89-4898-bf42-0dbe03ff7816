import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';

export async function POST(request: NextRequest) {
  try {
    const authorization = request.headers.get('Authorization');

    // Get the collection ID from the form data
    const formData = await request.formData();
    const collectionId = formData.get('collectionId');
    const documentIds = formData.get('documentIds');
    const chatId = formData.get('chatId');

    if (!collectionId || typeof collectionId !== 'string') {
      return NextResponse.json(
        { message: 'Collection ID is required' },
        { status: 400 }
      );
    }

    // Get the files from the form data
    const files = formData.getAll('files');

    if (!files || files.length === 0) {
      return NextResponse.json(
        { message: 'At least one file is required' },
        { status: 400 }
      );
    }

    // Create a new FormData object to send to the backend
    const backendFormData = new FormData();
    backendFormData.append('collectionId', collectionId);
    if (documentIds) {
      backendFormData.append('documentIds', documentIds);
    }
    if (chatId) {
      backendFormData.append('chatId', chatId);
    }

    // Add all files to the form data
    files.forEach((file) => {
      if (file instanceof File) {
        backendFormData.append('files', file);
      }
    });

    // Make the request to the backend API
    const apiResponse = await axios.post(
      `${process.env.INGESTION_SERVICE_URL}/api/upload-documents?code=${process.env.INGESTION_SERVICE_URL_CODE}`,
      backendFormData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
          ...(authorization && { Authorization: authorization }),
          timeout: 60 * 10 * 1000, // 10 minute timeout
        },
      }
    );

    // Return the response from the backend
    return NextResponse.json(apiResponse.data, { status: apiResponse.status });
  } catch (error: any) {
    console.error('Error uploading documents:', error);

    // Handle specific error responses from the backend
    if (error.response) {
      const status = error.response.status || 500;
      const message =
        error.response.data?.message || 'Error uploading documents';
      return NextResponse.json({ message }, { status });
    }

    // Handle other errors
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
