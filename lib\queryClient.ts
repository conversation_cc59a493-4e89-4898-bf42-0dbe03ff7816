import { QueryClient, QueryCache } from '@tanstack/react-query';
import { AxiosError } from 'axios'; // Assuming you use axios
import { toast } from 'sonner';

const MAX_RETRIES = 3;

const queryRetryFunction = (
  failureCount: number,
  error: Error | AxiosError
): boolean => {
  // Don't retry if it's a 404 error
  if (error instanceof AxiosError && error.response?.status === 404) {
    console.log(
      `Query failed with 404, not retrying (failure count: ${failureCount}).`
    );
    return false;
  }

  // Log other retry attempts for visibility (optional)
  if (failureCount < MAX_RETRIES) {
    console.log(
      `Query failed, retrying... (attempt ${failureCount + 1})`,
      error.message
    );
    return true; // Retry up to 3 times for other errors
  }

  console.log(`Query failed after ${failureCount} retries.`, error.message);
  return false; // Max retries reached
};

const globalQueryErrorHandler = (error: Error | AxiosError) => {
  console.error('Global Query Error Handler:', error); // Log all errors globally

  // Check specifically for 404 errors after retries
  if (error instanceof AxiosError && error.response?.status === 404) {
    toast('Resource not found', {
      description: 'The resource you are looking for is not found.',
      action: {
        label: 'OK',
        onClick: () => console.log('OK'),
      },
    });
  } else {
    // Optional: Show a generic error for other types of failed queries
    // Be cautious with this, as it might be too noisy.
    // toast.error('An error occurred while fetching data.');
  }
};

export const queryClient = new QueryClient({
  queryCache: new QueryCache({
    onError: globalQueryErrorHandler,
  }),
  defaultOptions: {
    queries: {
      retry: queryRetryFunction,
      staleTime: 1000 * 60 * 3, // 5 minutes
      refetchOnWindowFocus: false, // Optional: disable refetch on window focus
    },
  },
});
