'use client';

import { useGlobal } from '@/contexts/GlobalContext';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { cn } from '@/lib/utils';

interface UserAvatarProps {
  className?: string;
}

/**
 * UserAvatar component that displays the current user's avatar
 * Uses the profile_picture from user data if available, otherwise shows initials
 */
export function UserAvatar({ className }: UserAvatarProps) {
  const { store } = useGlobal();
  const { user, isLoadingUser } = store;

  // Get user initials from name (first letter of first and last name)
  const getUserInitials = (name: string): string => {
    if (!name) return '';

    const nameParts = name.split(' ');
    if (nameParts.length === 1) {
      return nameParts[0].charAt(0).toUpperCase();
    }

    const firstInitial = nameParts[0].charAt(0);
    const lastInitial = nameParts[nameParts.length - 1].charAt(0);
    return `${firstInitial}${lastInitial}`.toUpperCase();
  };

  if (isLoadingUser || !user) {
    // Show a placeholder or loading state
    return (
      <Avatar className={cn('bg-muted', className)}>
        <AvatarFallback>...</AvatarFallback>
      </Avatar>
    );
  }

  return (
    <Avatar className={className}>
      {user.profile_picture ? (
        <AvatarImage src={user.profile_picture} alt={user.name} />
      ) : null}
      <AvatarFallback>{getUserInitials(user.name)}</AvatarFallback>
    </Avatar>
  );
}
