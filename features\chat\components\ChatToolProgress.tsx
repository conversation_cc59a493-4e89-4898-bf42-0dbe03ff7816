'use client';

import { IToolProgress } from '@/types/chat/tool-progress';
import { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';

interface AnimatedMessage extends IToolProgress {
  id: string;
  isExiting: boolean;
}

export default function ChatToolProgress({
  toolProgress,
}: {
  toolProgress: IToolProgress[];
}) {
  const [animatedMessages, setAnimatedMessages] = useState<AnimatedMessage[]>(
    []
  );

  // Handle new messages being added
  useEffect(() => {
    if (toolProgress.length === 0) {
      setAnimatedMessages([]);
      return;
    }

    // Get the latest message
    const latestMessage = toolProgress[toolProgress.length - 1];

    // Check if this message is already in our animated messages
    const messageExists = animatedMessages.some(
      (msg) => msg.message === latestMessage.message
    );

    if (!messageExists) {
      // Mark all existing messages as exiting
      const updatedMessages = animatedMessages.map((msg) => ({
        ...msg,
        isExiting: true,
      }));

      // Add the new message
      setAnimatedMessages([
        ...updatedMessages,
        {
          ...latestMessage,
          id: `msg-${Date.now()}`,
          isExiting: false,
        },
      ]);
    }
  }, [toolProgress]);

  // Remove exiting messages after animation completes
  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimatedMessages((prev) =>
        prev.filter((message) => !message.isExiting)
      );
    }, 800); // Match this with the CSS transition duration

    return () => clearTimeout(timer);
  }, [animatedMessages]);

  return (
    <div className="tw-max-w-[80%] relative h-[30px] w-full overflow-hidden">
      {animatedMessages.map((message, index) => (
        <div
          key={message.id}
          className={cn(
            'absolute top-0 left-0 max-w-[80%]',
            'flex overflow-hidden transition-all delay-200 duration-800 ease-in-out',
            message.isExiting ? 'translate-x-[20px] opacity-0' : 'opacity-100'
          )}
        >
          <div className="bg-accent animate-pulse truncate overflow-hidden rounded-md p-2 px-3 text-xs text-ellipsis whitespace-nowrap">
            {message.message}
          </div>
        </div>
      ))}
    </div>
  );
}
