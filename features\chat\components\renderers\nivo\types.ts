export interface NivoChartConfig {
  type: string;
  data: any[];
  [key: string]: any;
}

export interface NivoRenderOptions {
  container: HTMLElement;
  theme?: 'light' | 'dark';
  isStreaming?: boolean;
  messageId?: string;
}

export interface NivoTheme {
  background: string;
  textColor: string;
  gridColor: string;
  tooltipBackground: string;
  tooltipColor: string;
}

export interface NivoProcessorContext {
  code: string;
  lang?: string;
  theme?: string;
  isStreaming?: boolean;
}
