import { useQuery } from '@tanstack/react-query';
import axiosInstanceUi from '@/lib/axiosInstance.ui';
import { API_CONFIG } from '@/config/api';
import { IUserData } from '@/types/user';

// Function to fetch the user data
const fetchUsers = async (): Promise<IUserData[]> => {
  // Use the client-side instance that includes the auth token
  const response = await axiosInstanceUi.get(API_CONFIG.admin.userList);

  if (!response.data) {
    // Return empty array if no data
    return [];
  }
  return response.data;
};

export function useFetchUsers() {
  return useQuery<IUserData[], Error>({
    queryKey: [API_CONFIG.admin.userList], // Unique query key for users
    queryFn: fetchUsers,
  });
}
