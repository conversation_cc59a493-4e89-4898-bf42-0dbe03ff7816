'use client';

import React, { useState } from 'react';
import { useFetchUsers } from '@/features/admin/api/useFetchUsers';
import { IUserData } from '@/types/user';
import LoadingSpinner from '@/components/LoadingSpinner';
import { Badge } from '@/components/ui/badge';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

export default function UserPage() {
  const { data: users, isLoading, error } = useFetchUsers();
  const [userToEdit, setUserToEdit] = useState<IUserData | null>(null);
  const [isSheetOpen, setIsSheetOpen] = useState(false);

  const handleEditUser = (user: IUserData) => {
    setUserToEdit(user);
    setIsSheetOpen(true);
  };

  const handleCreateUser = () => {
    setUserToEdit(null);
    setIsSheetOpen(true);
  };

  // Generate initials from name
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase();
  };

  if (isLoading) {
    return (
      <div className="flex h-full w-full items-center p-4">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-full w-full items-center justify-center">
        <p className="text-red-500">Error loading users: {error.message}</p>
      </div>
    );
  }

  return (
    <div className="w-full">
      {/* <div className="mb-4 flex justify-end">
        <Button onClick={handleCreateUser}>
          <PlusIcon className="mr-2 h-4 w-4" />
          Add User
        </Button>
      </div> */}

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[50px]"></TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Groups</TableHead>
              {/* <TableHead className="w-[100px]">Actions</TableHead> */}
            </TableRow>
          </TableHeader>
          <TableBody>
            {users?.map((user) => (
              <TableRow key={user.id}>
                <TableCell>
                  <Avatar className="h-8 w-8">
                    {user.profile_picture ? (
                      <AvatarImage src={user.profile_picture} alt={user.name} />
                    ) : (
                      <AvatarFallback className="bg-gray-600 text-xs text-white">
                        {getInitials(user.name)}
                      </AvatarFallback>
                    )}
                  </Avatar>
                </TableCell>
                <TableCell className="font-medium">{user.name}</TableCell>
                <TableCell>{user.email}</TableCell>
                <TableCell>
                  <div className="flex flex-wrap gap-1">
                    {user.groups && user.groups.length > 0 ? (
                      user.groups.map((group) => (
                        <Badge
                          key={group.id}
                          variant="outline"
                          className="bg-primary/10"
                        >
                          {group.name}
                        </Badge>
                      ))
                    ) : (
                      <span className="text-muted-foreground text-sm">
                        No groups
                      </span>
                    )}
                  </div>
                </TableCell>
                {/* <TableCell className="">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleEditUser(user)}
                  >
                    <Pencil className="h-4 w-4" />
                  </Button>
                </TableCell> */}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* User edit/create sheet would go here */}
      {/* {isSheetOpen && (
        <UserSheet
          isOpen={isSheetOpen}
          onOpenChange={setIsSheetOpen}
          user={userToEdit}
        />
      )} */}
    </div>
  );
}
