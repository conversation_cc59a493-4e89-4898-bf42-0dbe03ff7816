import { NextRequest, NextResponse } from 'next/server';
import axiosInstanceApi from '@/lib/axiosInstance.api';
import { API_CONFIG } from '@/config/api';

export async function PATCH(
  request: NextRequest,
  props: { params: Promise<{ toolId: string }> }
) {
  const authorization = request.headers.get('Authorization');
  const { toolId } = await props.params;
  const targetPath = `${API_CONFIG.chat.tool}/${toolId}`;

  try {
    console.log(
      `[API Route Proxy] Forwarding PATCH ${targetPath} with Auth: ${!!authorization}`
    );

    // Get request body
    const body = await request.json();

    const apiResponse = await axiosInstanceApi.patch(targetPath, body, {
      headers: {
        ...(authorization && { Authorization: authorization }),
      },
    });

    // Return the response from the backend service
    return NextResponse.json(apiResponse.data, { status: apiResponse.status });
  } catch (error: any) {
    console.error(
      `[API Route Proxy Error] PATCH ${targetPath}:`,
      error.response?.data || error.message
    );
    const status = error.response?.status || 500;
    const message = error.response?.data?.message || 'Proxy error';
    return NextResponse.json({ message }, { status });
  }
}

export async function GET(
  request: NextRequest,
  props: { params: Promise<{ toolId: string }> }
) {
  const authorization = request.headers.get('Authorization');
  const { toolId } = await props.params;
  const targetPath = `${API_CONFIG.chat.tool}/${toolId}`;

  try {
    console.log(
      `[API Route Proxy] Forwarding GET ${targetPath} with Auth: ${!!authorization}`
    );

    const apiResponse = await axiosInstanceApi.get(targetPath, {
      headers: {
        ...(authorization && { Authorization: authorization }),
      },
    });

    // Return the response from the backend service
    return NextResponse.json(apiResponse.data, { status: apiResponse.status });
  } catch (error: any) {
    console.error(
      `[API Route Proxy Error] GET ${targetPath}:`,
      error.response?.data || error.message
    );
    const status = error.response?.status || 500;
    const message = error.response?.data?.message || 'Proxy error';
    return NextResponse.json({ message }, { status });
  }
}
