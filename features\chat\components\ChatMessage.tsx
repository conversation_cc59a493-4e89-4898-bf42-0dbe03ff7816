'use client';

import { useState } from 'react';
import { cn } from '@/lib/utils';
import { MessageRoleEnum, IMessage } from '@/types/chat';
import ChatMessageContentRender from './ChatMessageContentRender';
import ChatFeedback from './ChatFeedback';
import { isEmpty } from 'lodash-es';
import { Button } from '@/components/ui/button';
import { Hammer, Bot } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useFetchModelById } from '@/features/workspace/api/useFetchModelById';
import { Badge } from '@/components/ui/badge';

interface ChatMessageProps {
  chatId: string;
  message: IMessage;
  isLoading?: boolean;
  isStreaming?: boolean;
  useFeedback?: boolean;
}

export default function ChatMessage({
  chatId,
  message,
  isLoading,
  isStreaming,
  useFeedback = true,
}: ChatMessageProps) {
  const [showToolDialog, setShowToolDialog] = useState(false);
  const isUser = message.role === MessageRoleEnum.USER;
  const modelId = message.config?.modelId;
  const { data: modelData } = useFetchModelById(modelId);

  // Format JSON for better display
  const formatJSON = (json: any) => {
    try {
      return JSON.stringify(json, null, 2);
    } catch (error) {
      return JSON.stringify(json);
    }
  };

  return (
    <div
      className={cn(
        'flex items-start',
        isUser ? 'justify-end' : 'justify-start'
      )}
    >
      <div
        className={cn(
          {
            'max-w-[90%]': isUser,
          },
          {
            ['group']: !isUser,
          }
        )}
      >
        {!isUser && message.tool_calls && (
          <>
            <div className="flex min-h-[50px] translate-y-[10px] items-end">
              <Button
                className="text-x4 h-7"
                variant="outline"
                onClick={() => setShowToolDialog(true)}
              >
                <Hammer size={10} />
                Tools
              </Button>
            </div>

            <Dialog open={showToolDialog} onOpenChange={setShowToolDialog}>
              <DialogContent className="sm:max-w-[1000px]">
                <DialogHeader>
                  <DialogTitle>Tool Calls</DialogTitle>
                  <DialogDescription>
                    Details of the tools used to generate this response
                  </DialogDescription>
                </DialogHeader>
                <ScrollArea className="bg-muted h-[60vh] w-[950px] rounded-lg">
                  <pre className="text-muted-foreground h-[59vh] rounded-md p-4 text-xs text-wrap">
                    {formatJSON(message.tool_calls)}
                  </pre>
                </ScrollArea>
              </DialogContent>
            </Dialog>
          </>
        )}

        <ChatMessageContentRender message={message} isStreaming={isStreaming} />

        {!isUser && !isEmpty(message.content) && message.config && modelId && (
          <div className="text-muted-foreground mb-4 flex items-center gap-1 text-xs">
            <Bot size={12} />
            <Badge variant="outline" className="px-2 py-0 text-xs font-normal">
              {modelData?.name}
            </Badge>
          </div>
        )}

        {!isUser && !isEmpty(message.content) && useFeedback && (
          <ChatFeedback
            chatId={chatId}
            messageId={message.id}
            feedback={message.feedback}
            isLoading={isLoading}
          />
        )}
      </div>
    </div>
  );
}
