'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { CloudUpload, Upload, X } from 'lucide-react';
import * as React from 'react';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

import { Button } from '@/components/ui/button';
import {
  FileUpload,
  FileUploadDropzone,
  FileUploadItem,
  FileUploadItemDelete,
  FileUploadItemMetadata,
  FileUploadItemPreview,
  FileUploadList,
  FileUploadTrigger,
} from '@/components/ui/file-upload';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { useUploadDocuments } from '@/features/workspace/api/useUploadDocuments';

const MAX_FILE_SIZE_MB = 20;
const MAX_FILE_COUNT = 50;
const MAX_FILE_SIZE = MAX_FILE_SIZE_MB * 1024 * 1024; // 20 MB
const ACCEPT_FILE_TYPES =
  '.pdf,.txt,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.csv,image/*';

const formSchema = z.object({
  files: z
    .array(z.custom<File>())
    .min(1, 'Please select at least one file')
    .max(MAX_FILE_COUNT, `Please select up to ${MAX_FILE_COUNT} files`)
    .refine((files) => files.every((file) => file.size <= MAX_FILE_SIZE), {
      message: `File size must be less than ${MAX_FILE_SIZE_MB}MB`,
      path: ['files'],
    }),
});

type FormValues = z.infer<typeof formSchema>;

interface CollectionFileUploadProps {
  collectionId: string;
  disabled?: boolean;
}

export default function CollectionFileUpload({
  collectionId,
  disabled = false,
}: CollectionFileUploadProps) {
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      files: [],
    },
  });
  // Use the upload documents mutation hook
  const { mutate: uploadDocuments, isPending: isUploading } =
    useUploadDocuments({
      collectionId,
      onSuccessCallback: () => {
        // Reset the form after successful upload
        form.reset();
      },
    });

  const onSubmit = React.useCallback(
    (data: FormValues) => {
      // Call the mutation with the files
      uploadDocuments(data.files);
    },
    [uploadDocuments]
  );

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <FormField
          control={form.control}
          name="files"
          render={({ field }) => (
            <FormItem>
              <div className="flex justify-between">
                <FormLabel>
                  <Upload size={20} />
                  <div>Upload Files</div>
                </FormLabel>

                <Button
                  variant={
                    field.value.length > 0 &&
                    form.formState.errors.files === undefined
                      ? 'default'
                      : 'outline'
                  }
                  type="submit"
                  size="sm"
                  disabled={
                    field.value.length === 0 ||
                    form.formState.errors.files !== undefined ||
                    isUploading ||
                    disabled
                  }
                >
                  {isUploading ? 'Uploading...' : 'Submit Files'}
                </Button>
              </div>
              <FormControl>
                <FileUpload
                  value={field.value}
                  onValueChange={(files) => {
                    if (disabled) return; // Don't update if disabled

                    field.onChange(files);

                    // Set error if more than MAX_FILE_COUNT files
                    if (files.length > MAX_FILE_COUNT) {
                      form.setError('files', {
                        message: `Please select up to ${MAX_FILE_COUNT} files`,
                      });
                    }
                    // Clear existing errors if the validation should now pass
                    else if (
                      files.length > 0 &&
                      files.every((file) => file.size <= MAX_FILE_SIZE)
                    ) {
                      form.clearErrors('files');
                    }

                    // Trigger validation after files change (including removal)
                    form.trigger('files');
                  }}
                  accept={ACCEPT_FILE_TYPES}
                  // Remove maxFiles limit to allow uploading more than 5 files
                  maxSize={MAX_FILE_SIZE}
                  onFileReject={(_, message) => {
                    // Only set error for file size issues, not for count
                    if (!message.includes('Maximum')) {
                      form.setError('files', {
                        message,
                      });
                    }
                  }}
                  multiple
                  disabled={disabled}
                >
                  <FileUploadDropzone className="bg-background flex-row border-dotted">
                    <CloudUpload className="size-4" />
                    Drop or
                    <FileUploadTrigger asChild>
                      <Button variant="outline" size="sm" disabled={disabled}>
                        choose files
                      </Button>
                    </FileUploadTrigger>
                  </FileUploadDropzone>
                  <FileUploadList>
                    {field.value.map((file, index) => (
                      <FileUploadItem key={index} value={file}>
                        <FileUploadItemPreview />
                        <FileUploadItemMetadata />
                        <FileUploadItemDelete asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="size-7"
                            disabled={disabled}
                          >
                            <X />
                            <span className="sr-only">Delete</span>
                          </Button>
                        </FileUploadItemDelete>
                      </FileUploadItem>
                    ))}
                  </FileUploadList>
                </FileUpload>
              </FormControl>
              <FormDescription>
                Each upload up to {MAX_FILE_COUNT} files (PDF, Office documents,
                CSV, TXT, or images) up to {MAX_FILE_SIZE_MB}MB each.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      </form>
    </Form>
  );
}
