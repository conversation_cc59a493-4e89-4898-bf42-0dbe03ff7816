import { ICollectionCreate, ICollectionData } from '@/types/collection';

// Generates the default structure for the collection form (ICollectionCreate)
// Optionally merges data from an existing collection for editing.
export const getCollectionTemplate = (
  collection: Partial<ICollectionData> = {}
): ICollectionCreate => {
  // Define the default structure for the form
  const defaults: ICollectionCreate = {
    name: '',
    description: '',
    groupIds: [],
  };

  // Extract groupIds from groups array if available
  const groupIds = collection?.groups?.map((group) => group.id) || [];

  // Merge defaults with provided collection data, prioritizing existing values
  return {
    ...defaults,
    name: collection?.name ?? defaults.name,
    description: collection?.description ?? defaults.description,
    groupIds: groupIds.length > 0 ? groupIds : defaults.groupIds,
  };
};
