// No imports needed

export interface IChatMetric {
  date: string;
  count: number;
}

export interface IModelCount {
  id: string;
  name: string;
  count: number;
}

export interface IUserCount {
  id: string;
  name: string;
  chatCount: number;
  message: number;
}

export interface IDashboardData {
  id: string;
  ts: string;
  pk: string;
  totalModels: number;
  totalCollections: number;
  totalDocuments: number;
  totalUsers: number;
  totalGroups: number;
  totalChats: number;
  totalMes: number;
  totalPos: number;
  totalNeg: number;
  totalRes: {
    positive: Record<string, number>;
    negative: Record<string, number>;
  };
  totalComments: number;
  chatsLast30Days?: number;
  chatMetricsDetails?: IChatMetric[];
  modelCounts?: IModelCount[];
  userCounts?: IUserCount[];
}

export interface IDashboardActivity {
  id: string;
  pk: string;
  ts: string;
  userId: string;
  userName: string;
  activityType: string;
  summary: string;
  details: any; // Can be either a string or an object
  _rid?: string;
  _self?: string;
  _etag?: string;
  _attachments?: string;
  _ts?: number;
}

export interface ActivityPagination {
  page: number;
  limit: number;
  total_items: number;
  total_pages: number;
}

export interface ActivityResponse {
  items: IDashboardActivity[];
  pagination: ActivityPagination;
}
