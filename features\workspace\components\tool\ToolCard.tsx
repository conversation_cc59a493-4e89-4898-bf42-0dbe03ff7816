'use client';

import React from 'react';
import {
  <PERSON>,
  Card<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>ontent,
  CardFooter,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { IToolData } from '@/types/model';
import { Wren<PERSON>, Users } from 'lucide-react';

interface ToolCardProps {
  tool: IToolData;
  onEdit: (tool: IToolData) => void;
}

const ToolCard: React.FC<ToolCardProps> = ({ tool, onEdit }) => (
  <Card className="group relative gap-2 rounded-md py-4 shadow-none hover:shadow">
    <CardHeader className="px-4">
      <div className="flex items-center gap-2">
        <CardTitle className="text-base font-bold">{tool.name}</CardTitle>
      </div>
    </CardHeader>

    <CardContent className="flex flex-1 flex-col gap-4 px-4">
      {tool.description && (
        <p className="text-muted-foreground line-clamp-2 text-sm break-words">
          {tool.description}
        </p>
      )}

      {tool.groups && tool.groups.length > 0 && (
        <div>
          <div className="flex flex-wrap gap-1.5">
            {tool.groups.map((group) => (
              <Badge
                key={group.id}
                variant="outline"
                className="flex items-center gap-2 bg-slate-50/10 text-xs"
              >
                <Users size={10} />
                {group.name}
              </Badge>
            ))}
          </div>
        </div>
      )}
    </CardContent>

    <CardFooter className="flex h-[30px] items-center justify-between px-4 py-4">
      <div className="flex items-center gap-2">
        <Wrench className="text-muted-foreground h-4 w-4" />
        <span className="text-muted-foreground text-xs">Tool</span>
      </div>
      <Button
        className="block md:hidden md:group-hover:block"
        variant="outline"
        size="sm"
        onClick={() => onEdit(tool)}
      >
        Edit
      </Button>
    </CardFooter>
  </Card>
);

export default ToolCard;
