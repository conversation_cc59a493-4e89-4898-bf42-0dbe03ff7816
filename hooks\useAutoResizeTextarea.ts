import { useEffect, RefObject } from 'react';

/**
 * A hook that automatically resizes a textarea element as the user types.
 * Works across all browsers including Chrome, Firefox, and Safari.
 *
 * @param textAreaRef - A React ref object pointing to the textarea element
 * @param value - The current value of the textarea (optional)
 */
export function useAutoResizeTextarea(
  textAreaRef: RefObject<HTMLTextAreaElement | null>,
  value?: string
): void {
  useEffect(() => {
    const textArea = textAreaRef.current;
    if (!textArea) return;

    // Function to adjust the height
    const adjustHeight = () => {
      // Reset the height to auto to get the correct scrollHeight
      textArea.style.height = 'auto';

      // Set the height to the scrollHeight to fit the content
      textArea.style.height = `${textArea.scrollHeight}px`;
    };

    // Set initial height
    adjustHeight();

    // Add event listener for input events
    textArea.addEventListener('input', adjustHeight);

    // Create a ResizeObserver to handle window resize events
    const resizeObserver = new ResizeObserver(() => {
      adjustHeight();
    });

    // Start observing the textarea
    resizeObserver.observe(textArea);

    // Clean up
    return () => {
      textArea.removeEventListener('input', adjustHeight);
      resizeObserver.disconnect();
    };
  }, [textAreaRef, value]);
}
