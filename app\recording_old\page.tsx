'use client';
import { useState, useEffect, useRef } from 'react';
import { AudioRecorderService } from '@/services/audio-recorder.service';
import { TranscriptionService } from '@/services/transcription.service';
import { getSpeechToken } from '@/features/stt/api/useGetSpeechToken';
import { Button } from '@/components/ui/button';

// Define type for recognized speech items
interface RecognizedSpeech {
  text: string;
  speakerId: string;
  timestamp: Date;
}

export default function RecordingPage() {
  const [isRecording, setIsRecording] = useState(false);
  const [statusMessage, setStatusMessage] = useState('Ready to record');
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [recognizedItems, setRecognizedItems] = useState<RecognizedSpeech[]>(
    []
  );
  const audioRecorderRef = useRef<AudioRecorderService | null>(null);
  const transcriptionServiceRef = useRef<TranscriptionService | null>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // TODO: change this, move to toggle logic.
  useEffect(() => {
    const inner = async () => {
      const speechToken = await getSpeechToken();

      // Create the recorder service
      audioRecorderRef.current = new AudioRecorderService(
        // Replace with your WebSocket endpoint for audio streaming
        `${process.env.NEXT_PUBLIC_WS_SERVICE_URL}/api/chat/record/123`
      );
      transcriptionServiceRef.current = new TranscriptionService(
        speechToken.token,
        speechToken.region,
        (sender, event) => {
          console.log('Transcribing:', event);
        },
        (sender, event) => {
          setRecognizedItems((prev) => [
            ...prev,
            {
              text: event.result.text,
              speakerId: event.result.speakerId,
              timestamp: new Date(),
            },
          ]);
        }
      );
    };

    inner();

    // Clean up on unmount
    return () => {
      if (transcriptionServiceRef.current) {
        transcriptionServiceRef.current.dispose();
      }
      if (audioRecorderRef.current) {
        audioRecorderRef.current.dispose();
      }
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  const handleToggleRecording = async () => {
    if (isRecording) {
      // Stop recording
      await transcriptionServiceRef.current?.stop();
      audioRecorderRef.current?.stopRecording();
      setIsRecording(false);
      setStatusMessage('Recording saved');

      // Clear the timer
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    } else {
      // Start recording
      try {
        setStatusMessage('Starting recording...');
        await transcriptionServiceRef.current?.start();
        await audioRecorderRef.current?.startRecording();
        setIsRecording(true);
        setStatusMessage('Recording in progress');
        setRecordingDuration(0);

        // Start a timer to track recording duration
        timerRef.current = setInterval(() => {
          setRecordingDuration((prev) => prev + 1);
        }, 1000);
      } catch (error) {
        console.error('Failed to start recording:', error);
        setStatusMessage('Failed to start recording');
      }
    }
  };

  // Format seconds to mm:ss
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="mx-auto max-w-3xl p-4">
      <h1 className="mb-6 text-center text-2xl font-bold">Recording Page</h1>

      <div className="flex flex-col items-center space-y-6">
        {/* <button
          className={`rounded p-2 ${isRecording ? 'bg-red-500' : 'bg-green-500'}`}
          onClick={handleToggleRecording}
        /> */}

        <Button
          variant={isRecording ? 'destructive' : 'default'}
          className={`${isRecording ? 'bg-red-500 hover:bg-red-600' : 'bg-green-500 hover:bg-green-600'}`}
          onClick={handleToggleRecording}
        >
          {isRecording ? 'Stop' : 'Start'}
        </Button>

        <div className="text-center">
          <p className="text-lg font-medium">{statusMessage}</p>
          {isRecording && (
            <p className="mt-2 font-mono text-xl text-red-500">
              {formatDuration(recordingDuration)}
            </p>
          )}
        </div>

        <div className="bg-gray-background w-full rounded-lg border p-4">
          <h2 className="mb-2 text-lg font-semibold">Instructions</h2>
          <p>
            Click the microphone button above to start recording. Click again to
            stop. The audio will be streamed to the server as you speak.
          </p>
        </div>

        {/* Display recognized speech */}
        <div className="bg-background w-full rounded-lg border p-4">
          <h2 className="mb-2 text-lg font-semibold">Recognized Speech</h2>
          {recognizedItems.length === 0 ? (
            <p className="text-gray-500">No speech recognized yet</p>
          ) : (
            <ul className="space-y-2">
              {recognizedItems.map((item, index) => (
                <li key={index} className="border-b border-gray-100 pb-2">
                  <span className="font-semibold">{item.speakerId}: </span>
                  <span>{item.text}</span>
                </li>
              ))}
            </ul>
          )}
        </div>
      </div>
    </div>
  );
}
