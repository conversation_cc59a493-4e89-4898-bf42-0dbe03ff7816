import { NextRequest, NextResponse } from 'next/server';
import axiosInstanceApi from '@/lib/axiosInstance.api';

/**
 * GET handler for fetching a shared chat by token
 * This endpoint proxies the request to the backend API
 */
export async function GET(
  request: NextRequest,
  props: { params: Promise<{ chatToken: string }> }
) {
  try {
    const { chatToken } = await props.params;

    if (!chatToken) {
      return NextResponse.json(
        { message: 'Chat token is required' },
        { status: 400 }
      );
    }

    // Extract Authorization header from the incoming request (if any)
    const authorization = request.headers.get('Authorization');

    console.log(
      `[API Route Proxy] Forwarding GET /chat/shared/${chatToken} with Auth: ${!!authorization}`
    );

    // Forward the request to the backend API
    const apiResponse = await axiosInstanceApi.get(
      `/chat/shared/${chatToken}`,
      {
        headers: {
          'Content-Type': 'application/json',
          ...(authorization && { Authorization: authorization }),
        },
      }
    );

    // Return the response from the backend service
    return NextResponse.json(apiResponse.data, { status: apiResponse.status });
  } catch (error: any) {
    // Log the error for debugging
    console.error(
      `[API Route Proxy Error] GET /chat/shared/${props.params.then((p) => p.chatToken)}:`,
      error.response?.data || error.message
    );

    // Handle 404 errors specifically
    if (error.response?.status === 404) {
      return NextResponse.json(
        { message: 'Shared chat not found' },
        { status: 404 }
      );
    }

    // Handle other errors
    const status = error.response?.status || 500;
    const message = error.response?.data?.message || 'Internal Server Error';
    return NextResponse.json({ message }, { status });
  }
}
