import * as React from 'react';

import { cn } from '@/lib/utils';

// 1. Define the extended props interface including 'outline'
export interface TextareaProps extends React.ComponentProps<'textarea'> {
  outline?: boolean;
}

// 2. Use the new interface and destructure 'outline'
function Textarea({ className, outline = false, ...props }: TextareaProps) {
  return (
    <div
      className={cn('flex w-full items-center justify-center', {
        // 'border border-input rounded-md': outline // Example: Border on wrapper
      })}
    >
      <textarea
        data-slot="textarea"
        className={cn(
          'placeholder:text-muted-foreground',
          'flex field-sizing-content min-h-16 w-full resize-none',
          'bg-transparent px-3 py-2 text-base transition-[color,box-shadow]',
          'outline-none focus:outline-none focus-visible:ring-offset-0',
          'disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',

          outline
            ? 'border-input focus-visible:border-input focus:border-input rounded-md border focus:ring-0 focus-visible:ring-0'
            : 'border-none focus:border-transparent focus-visible:border-none',

          'aria-invalid:border-destructive aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40',

          className
        )}
        {...props}
      />
    </div>
  );
}

export { Textarea };
