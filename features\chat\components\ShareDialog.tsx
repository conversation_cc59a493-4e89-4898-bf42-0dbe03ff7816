'use client';

import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Check, Copy, Loader2 } from 'lucide-react';
import { toast } from 'sonner';

interface ShareDialogProps {
  isOpen: boolean;
  onClose: () => void;
  shareToken: string;
  isLoading: boolean;
}

export default function ShareDialog({
  isOpen,
  onClose,
  shareToken,
  isLoading,
}: ShareDialogProps) {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(getFullShareUrl());
      setCopied(true);
      toast.success('The share link has been copied to your clipboard.');
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy:', error);
      toast.error('Failed to copy the link. Please try again.');
    }
  };

  const getFullShareUrl = () => {
    if (!shareToken) return '';

    const baseUrl = window.location.origin;
    return `${baseUrl}/share/${shareToken}`;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Share Chat</DialogTitle>
          <DialogDescription>
            Anyone with this link will be able to view this chat.
          </DialogDescription>
        </DialogHeader>
        <div className="flex items-center space-x-2 py-4">
          {isLoading ? (
            <div className="flex w-full items-center justify-center py-4">
              <Loader2 className="text-primary h-6 w-6 animate-spin" />
            </div>
          ) : (
            <>
              <Input value={getFullShareUrl()} readOnly className="flex-1" />
              <Button
                type="button"
                size="icon"
                onClick={handleCopy}
                disabled={copied}
              >
                {copied ? (
                  <Check className="h-4 w-4" />
                ) : (
                  <Copy className="h-4 w-4" />
                )}
              </Button>
            </>
          )}
        </div>
        <DialogFooter className="sm:justify-start">
          <Button type="button" variant="secondary" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
