import { NextRequest, NextResponse } from 'next/server';
import axiosInstanceApi from '@/lib/axiosInstance.api';
import { IModelVersionsResponse } from '@/types/model';

export async function GET(
  request: NextRequest,
  props: { params: Promise<{ modelId: string }> }
) {
  try {
    // Extract Authorization header from the incoming request
    const authorization = request.headers.get('Authorization');
    const { modelId } = await props.params;

    if (!authorization) {
      return NextResponse.json(
        { error: 'Authorization header is required' },
        { status: 401 }
      );
    }

    // Extract query parameters
    const { searchParams } = new URL(request.url);
    const limit = searchParams.get('limit') || '10'; // Default to 10 as requested
    const page = searchParams.get('page') || '1';

    // Build query string
    const queryString = new URLSearchParams({
      limit,
      page,
    }).toString();

    // Make request to backend API
    const response = await axiosInstanceApi.get<IModelVersionsResponse>(
      `/model/${modelId}/versions?${queryString}`,
      {
        headers: {
          Authorization: authorization,
        },
        timeout: 10 * 60 * 1000, // 10 minutes timeout as per memory
      }
    );

    return NextResponse.json(response.data);
  } catch (error: any) {
    console.error('Error fetching model versions:', error);

    // Handle different error types
    if (error.response) {
      return NextResponse.json(
        { error: error.response.data?.message || 'Backend error' },
        { status: error.response.status }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
