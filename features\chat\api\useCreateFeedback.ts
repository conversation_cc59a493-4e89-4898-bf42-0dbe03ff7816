import { useMutation } from '@tanstack/react-query';
import axiosInstanceUi from '@/lib/axiosInstance.ui';
import { IFeedbackData } from '@/types/feedback';
import { toast } from 'sonner';
import { queryClient } from '@/lib/queryClient';
import { API_CONFIG } from '@/config/api';

// Define the payload type for the mutation function
interface CreateFeedbackPayload {
  chatId: string;
  messageId: string;
  feedback: IFeedbackData;
}

// Function to create feedback via POST request
const createFeedback = async ({
  chatId,
  messageId,
  feedback,
}: CreateFeedbackPayload): Promise<void> => {
  console.log('Submitting feedback:', feedback);

  // Clean up the payload if type is undefined
  const payload = { ...feedback };
  if (payload.type === undefined) {
    payload.reasons = undefined;
    payload.comment = undefined;
  }

  // Use the client-side instance that includes the auth token
  await axiosInstanceUi.post<void>(
    // Use the endpoint format from the requirements
    `/chat/${chatId}/messages/${messageId}/feedback`,
    payload
  );
};

export function useCreateFeedback({
  onSuccessCallback,
}: {
  onSuccessCallback?: () => void;
} = {}) {
  return useMutation<void, Error, CreateFeedbackPayload>({
    mutationFn: createFeedback,
    onSuccess: (_, variables) => {
      toast.success('Thank you for your feedback');

      // invalidate the chat query to refresh the UI
      queryClient.invalidateQueries({
        queryKey: [API_CONFIG.chat.chat, variables.chatId],
      });

      // Call the success callback if provided
      onSuccessCallback?.();
    },
    onError: (error) => {
      console.error('Error submitting feedback:', error);
      toast.error(`Failed to submit feedback: ${error.message}`);
    },
  });
}
