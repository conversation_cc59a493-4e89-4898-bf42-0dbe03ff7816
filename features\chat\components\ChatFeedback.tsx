import { useEffect, useState, useRef } from 'react';
import { cn } from '@/lib/utils';
import { ThumbsUp, ThumbsDown, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import {
  FeedbackTypeEnum,
  FeedbackReason,
  IFeedbackData,
} from '@/types/feedback';
import {
  negativeFeedbackReasons,
  positiveFeedbackReasons,
} from '@/config/feedback';
import { useCreateFeedback } from '@/features/chat/api/useCreateFeedback';
import { cloneDeep } from 'lodash-es';

export interface IChatFeedbackProps {
  feedback?: IFeedbackData;
  className?: string;
  isLoading?: boolean;
  chatId: string;
  messageId: string;
  onFeedbackChange?: (feedback: IFeedbackData) => void;
}

export default function ChatFeedback({
  feedback: initialFeedback,
  className,
  isLoading,
  chatId,
  messageId,
  onFeedbackChange,
}: IChatFeedbackProps) {
  const [showFeedbackForm, setShowFeedbackForm] = useState(false);
  const feedbackFormRef = useRef<HTMLDivElement>(null);

  // Use the feedback mutation hook
  const createFeedbackMutation = useCreateFeedback({
    onSuccessCallback: () => setShowFeedbackForm(false),
  });

  // Auto-scroll to the feedback form when it becomes visible
  useEffect(() => {
    if (showFeedbackForm && feedbackFormRef.current) {
      // Use a small timeout to ensure the DOM has updated
      setTimeout(() => {
        feedbackFormRef.current?.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        });
      }, 100);
    }
  }, [showFeedbackForm]);

  // Use a single feedbackData object for state
  const [feedbackData, setFeedbackData] = useState<IFeedbackData>(
    initialFeedback || {
      type: undefined,
      reasons: [],
      comment: '',
    }
  );

  // Update local state if prop changes
  useEffect(() => {
    if (initialFeedback) {
      setFeedbackData(initialFeedback);
    }
  }, [initialFeedback]);

  // Update feedback data and notify parent if callback exists
  const updateFeedbackData = (updates: Partial<IFeedbackData>) => {
    const updatedFeedback = {
      ...feedbackData,
      ...updates,
    } as IFeedbackData;

    setFeedbackData(updatedFeedback);

    if (onFeedbackChange) {
      onFeedbackChange(updatedFeedback);
    }
  };

  const handlePositiveFeedback = () => {
    // Toggle the feedback type
    const type = FeedbackTypeEnum.POSITIVE;
    if (feedbackData.type === type) {
      return;
    }

    // Update the feedback data with reset reasons when changing to positive
    updateFeedbackData({
      type,
      // Reset reasons when changing feedback type
      reasons: type === FeedbackTypeEnum.POSITIVE ? [] : feedbackData.reasons,
      comment: initialFeedback?.comment || '',
    });

    // Show or hide the form based on the type
    setShowFeedbackForm(true);
  };

  const handleNegativeFeedback = () => {
    // Toggle the feedback type
    const type = FeedbackTypeEnum.NEGATIVE;
    if (feedbackData.type === type) {
      return;
    }

    // Update the feedback data with reset reasons when changing to negative
    updateFeedbackData({
      type,
      // Reset reasons when changing feedback type
      reasons: type === FeedbackTypeEnum.NEGATIVE ? [] : feedbackData.reasons,
      comment: initialFeedback?.comment || '',
    });

    // Show or hide the form based on the type
    setShowFeedbackForm(true);
  };

  const toggleReason = (reason: FeedbackReason) => {
    if (!feedbackData) return;

    const currentReasons = feedbackData.reasons || [];
    const updatedReasons = currentReasons.includes(reason)
      ? currentReasons.filter((r) => r !== reason)
      : [...currentReasons, reason];

    updateFeedbackData({ reasons: updatedReasons });
  };

  const removeFeedback = () => {
    const feedback = { type: undefined, reasons: [], comment: '' };
    updateFeedbackData(feedback);
    setShowFeedbackForm(false);

    createFeedbackMutation.mutate({
      chatId,
      messageId,
      feedback,
    });
  };

  const handleCommentChange = (comment: string) => {
    updateFeedbackData({ comment });
  };

  const submitFeedback = () => {
    if (!feedbackData) return;

    // Use the mutation hook to submit feedback
    createFeedbackMutation.mutate({
      chatId,
      messageId,
      feedback: feedbackData,
    });
  };

  const hideFeedback = () => {
    if (initialFeedback) updateFeedbackData(cloneDeep(initialFeedback));
    else updateFeedbackData({ type: undefined, reasons: [], comment: '' });
    setShowFeedbackForm(false);
  };

  const toggleFeedback = () => {
    setShowFeedbackForm(!showFeedbackForm);
  };

  // Render the feedback form (used for both positive and negative feedback)
  const renderFeedbackForm = () => {
    if (!feedbackData) return null;

    const isPositive = feedbackData.type === FeedbackTypeEnum.POSITIVE;
    const reasons = isPositive
      ? positiveFeedbackReasons
      : negativeFeedbackReasons;
    const promptText = isPositive
      ? 'What did you like about this response?'
      : 'What could be improved in this response?';

    return (
      <div
        ref={feedbackFormRef}
        className={cn(
          'mb-8 max-w-[90%] space-y-4 rounded-lg border p-4',
          className
        )}
      >
        <div className="flex items-center justify-between">
          <div className="font-medium">{promptText}</div>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={hideFeedback}
              aria-label="Close feedback"
            >
              <X size={16} />
            </Button>
          </div>
        </div>

        <div>
          <div className="mb-3 flex flex-wrap gap-2">
            {reasons.map((reason) => (
              <Badge
                key={reason.value}
                variant={
                  feedbackData.reasons?.includes(reason.value)
                    ? 'default'
                    : 'outline'
                }
                className={cn(
                  'cursor-pointer',
                  feedbackData.type === FeedbackTypeEnum.POSITIVE &&
                    (feedbackData.reasons?.includes(reason.value)
                      ? 'bg-green-600 hover:bg-green-600'
                      : 'hover:border-green-600 hover:text-green-600'),
                  feedbackData.type === FeedbackTypeEnum.NEGATIVE &&
                    (feedbackData.reasons?.includes(reason.value)
                      ? 'bg-red-500 hover:bg-red-600'
                      : 'hover:border-red-600 hover:text-red-600')
                )}
                onClick={() => toggleReason(reason.value)}
              >
                {reason.label}
              </Badge>
            ))}
          </div>
        </div>

        <Textarea
          placeholder="Feel free to add specific details"
          value={feedbackData.comment || ''}
          onChange={(e) => handleCommentChange(e.target.value)}
          className="resize-none"
          rows={3}
          outline
        />

        <div className="flex items-center justify-end gap-2">
          <Button
            variant="ghost"
            size="sm"
            className="text-muted-foreground"
            onClick={removeFeedback}
            aria-label="Remove Feedback"
          >
            Remove
          </Button>
          <Button
            onClick={submitFeedback}
            disabled={createFeedbackMutation.isPending}
            size="sm"
          >
            {createFeedbackMutation.isPending ? 'Submitting...' : 'Submit'}
          </Button>
        </div>
      </div>
    );
  };

  // Initial state with thumbs up/down buttons
  return (
    <div className="min-h-[50px]">
      {!isLoading && (
        <div
          className={cn(
            'space-y-4 group-hover:block',
            `${showFeedbackForm || initialFeedback ? 'block' : 'hidden'}`
          )}
        >
          <div className={cn('flex items-center gap-1', className)}>
            <Button
              variant="ghost"
              size="icon"
              onClick={handlePositiveFeedback}
              disabled={createFeedbackMutation.isPending}
              aria-label="Thumbs up"
              className={cn(
                'text-muted-foreground hover:bg-transparent hover:text-green-600',
                feedbackData?.type === FeedbackTypeEnum.POSITIVE &&
                  '!bg-green-300/10 text-green-600'
              )}
            >
              <ThumbsUp size={16} />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleNegativeFeedback}
              disabled={createFeedbackMutation.isPending}
              aria-label="Thumbs down"
              className={cn(
                'text-muted-foreground hover:bg-transparent hover:text-red-600',
                feedbackData?.type === FeedbackTypeEnum.NEGATIVE &&
                  '!bg-red-300/10 text-red-600'
              )}
            >
              <ThumbsDown size={16} />
            </Button>

            {feedbackData.type && !showFeedbackForm && (
              <Button
                variant="ghost"
                onClick={toggleFeedback}
                aria-label="Toggle feedback"
                size="sm"
              >
                Show Feedback
              </Button>
            )}
          </div>

          {feedbackData?.type && showFeedbackForm && renderFeedbackForm()}
        </div>
      )}
    </div>
  );
}
