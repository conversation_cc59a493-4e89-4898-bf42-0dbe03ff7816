'use client';

import { useState } from 'react';
import { Share } from 'lucide-react';
import { Button } from '@/components/ui/button';
import ChatModelSelector from './ChatModelSelector';
import ShareDialog from './ShareDialog';
import { useCreateChatSharedLink } from '@/features/chat/api/useCreateChatSharedLink';
import { useParams } from 'next/navigation';
import { toast } from 'sonner';

export default function ChatToolbar() {
  const [isShareDialogOpen, setIsShareDialogOpen] = useState(false);
  const [shareToken, setShareToken] = useState('');
  const { chatId } = useParams();
  const createSharedLinkMutation = useCreateChatSharedLink();

  const handleShareClick = async () => {
    if (!chatId) {
      toast.error('Cannot share this chat.');
      return;
    }

    try {
      setIsShareDialogOpen(true);
      const result = await createSharedLinkMutation.mutateAsync(
        chatId as string
      );
      setShareToken(result.token);
    } catch (error) {
      console.error('Failed to create shared link:', error);
      toast.error('Failed to create shared link. Please try again.');
    }
  };

  return (
    <div className="relative flex h-full w-full items-center justify-center px-2 xl:justify-between">
      <ChatModelSelector />
      {chatId && (
        <div className="to-0 absolute right-2 flex h-full items-center">
          <Button
            variant="outline"
            className="rounded-full"
            onClick={handleShareClick}
            disabled={createSharedLinkMutation.isPending}
          >
            <Share className="mr-2 h-4 w-4" />
            <span>Share</span>
          </Button>
        </div>
      )}

      <ShareDialog
        isOpen={isShareDialogOpen}
        onClose={() => setIsShareDialogOpen(false)}
        shareToken={shareToken}
        isLoading={createSharedLinkMutation.isPending}
      />
    </div>
  );
}
