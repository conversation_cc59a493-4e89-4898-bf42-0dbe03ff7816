'use client';

import React from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { ThumbsDown, ThumbsUp } from 'lucide-react';
import { IDashboardData } from '@/types/dashboard';
import { cn } from '@/lib/utils';

interface FeedbackDetailsCardProps {
  data: IDashboardData;
  isLoading?: boolean;
}

export default function FeedbackDetailsCard({
  data,
  isLoading = false,
}: FeedbackDetailsCardProps) {
  // Format reason text for display (convert snake_case to Title Case)
  const formatReason = (reason: string): string => {
    return reason
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Get positive and negative reasons
  const positiveReasons = Object.entries(data.totalRes.positive || {}).sort(
    ([, a], [, b]) => b - a
  );

  const negativeReasons = Object.entries(data.totalRes.negative || {}).sort(
    ([, a], [, b]) => b - a
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle>Feedback Details</CardTitle>
        <CardDescription>Detailed breakdown of user feedback</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {isLoading ? (
          <div className="space-y-4">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
          </div>
        ) : (
          <>
            {/* Positive Feedback Section */}
            <div>
              <div className="mb-2 flex items-center gap-2">
                <Badge className="text-foreground bg-[var(--chart-3)]">
                  <ThumbsUp className="mr-1 h-3 w-3" />
                  Positive
                </Badge>
                <span className="text-sm font-medium">
                  {data.totalPos} responses
                </span>
              </div>

              {positiveReasons.length > 0 ? (
                <div className="mt-2 space-y-2">
                  {positiveReasons.map(([reason, count]) => (
                    <div
                      key={reason}
                      className="flex items-center justify-between p-2"
                    >
                      <div className="flex items-center gap-2">
                        <div
                          className={cn(
                            'h-2 rounded-full bg-green-600',
                            count === 1
                              ? 'w-[20px]'
                              : count === 2
                                ? 'w-[40px]'
                                : count === 3
                                  ? 'w-[60px]'
                                  : count === 4
                                    ? 'w-[80px]'
                                    : 'w-[100px]'
                          )}
                        ></div>
                        <span className="text-sm font-semibold">{count}</span>
                      </div>

                      <span className="text-sm font-medium">
                        {formatReason(reason)}
                      </span>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground text-sm">
                  No positive feedback details available
                </p>
              )}
            </div>

            {/* Negative Feedback Section */}
            <div>
              <div className="mb-2 flex items-center gap-2">
                <Badge className="bg-[var(--chart-4)] text-white">
                  <ThumbsDown className="mr-1 h-3 w-3" />
                  Negative
                </Badge>
                <span className="text-sm font-medium">
                  {data.totalNeg} responses
                </span>
              </div>

              {negativeReasons.length > 0 ? (
                <div className="mt-2 space-y-2">
                  {negativeReasons.map(([reason, count]) => (
                    <div
                      key={reason}
                      className="flex items-center justify-between rounded-md p-2"
                    >
                      <div className="flex items-center gap-2">
                        <div
                          className={cn(
                            'h-2 rounded-full bg-red-600',
                            count === 1
                              ? 'w-[20px]'
                              : count === 2
                                ? 'w-[40px]'
                                : count === 3
                                  ? 'w-[60px]'
                                  : count === 4
                                    ? 'w-[80px]'
                                    : 'w-[100px]'
                          )}
                        ></div>
                        <span className="text-sm font-semibold">{count}</span>
                      </div>

                      <span className="text-sm font-medium">
                        {formatReason(reason)}
                      </span>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground text-sm">
                  No negative feedback details available
                </p>
              )}
            </div>

            {/* Summary Section */}
            {(positiveReasons.length > 0 || negativeReasons.length > 0) && (
              <div className="bg-muted/30 mt-4 rounded-md border p-3">
                <h4 className="mb-2 text-sm font-semibold">Feedback Summary</h4>
                <p className="text-muted-foreground text-sm">
                  {data.totalPos > data.totalNeg
                    ? `Users are generally satisfied, with ${Math.round((data.totalPos / (data.totalPos + data.totalNeg)) * 100)}% positive feedback.`
                    : data.totalPos < data.totalNeg
                      ? `Users are experiencing some issues, with ${Math.round((data.totalNeg / (data.totalPos + data.totalNeg)) * 100)}% negative feedback.`
                      : 'User feedback is evenly split between positive and negative.'}
                  {positiveReasons.length > 0 &&
                    ` Top positive reason: ${formatReason(positiveReasons[0][0])}.`}
                  {negativeReasons.length > 0 &&
                    ` Top area for improvement: ${formatReason(negativeReasons[0][0])}.`}
                </p>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
}
