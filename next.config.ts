import type { NextConfig } from 'next';

const nextConfig: NextConfig = {
  /* config options here */
  devIndicators: false,
  reactStrictMode: false,
  output: 'standalone',

  // Configure headers to allow iframe embedding in Microsoft Teams
  async headers() {
    return [
      {
        // Apply to all routes
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'ALLOWALL', // Allow iframe embedding from any domain
          },
          {
            key: 'Content-Security-Policy',
            value: "frame-ancestors 'self' https://*.teams.microsoft.com https://*.teams.office.com https://*.office.com https://*.microsoft.com https://*.microsoftonline.com;",
          },
        ],
      },
    ];
  },
};

export default nextConfig;
