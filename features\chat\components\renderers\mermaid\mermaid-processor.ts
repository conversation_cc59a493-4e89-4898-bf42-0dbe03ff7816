import {
  ContentProcessor,
  ContentProcessorContext,
  ProcessedContent,
} from '../../content-processors/types';
import mermaid from 'mermaid';
import { MermaidRendererOptions } from './types';

// Track last used theme to avoid unnecessary re-initialization
let lastUsedTheme: string | undefined;
let isInitialized = false;

export class MermaidProcessor implements ContentProcessor {
  initialize(ctx: ContentProcessorContext): void {
    const theme = ctx.theme;
    const mermaidTheme = theme === 'dark' ? 'dark' : 'default';

    // Only initialize once or when theme changes
    if (!isInitialized || lastUsedTheme !== theme) {
      try {
        // Initialize mermaid with comprehensive configuration
        mermaid.initialize({
          startOnLoad: false, // We'll handle rendering manually
          theme: mermaidTheme,
          securityLevel: 'loose',
          fontFamily: 'sans-serif',
          logLevel: 'error',
          flowchart: {
            useMaxWidth: true,
            htmlLabels: true,
            curve: 'cardinal',
          },
          sequence: {
            diagramMarginX: 50,
            diagramMarginY: 10,
            actorMargin: 50,
          },
          gantt: {
            titleTopMargin: 25,
            barHeight: 20,
            barGap: 4,
          },
          pie: {
            useWidth: 800,
          },
          er: {},
          state: {},
        });

        lastUsedTheme = theme;
        isInitialized = true;
        console.log('Mermaid initialized with theme:', mermaidTheme);
      } catch (error) {
        console.error('Failed to initialize mermaid:', error);
      }
    }
  }

  canProcess(ctx: ContentProcessorContext): boolean {
    return ctx.lang === 'mermaid';
  }

  process(ctx: ContentProcessorContext): ProcessedContent {
    this.initialize(ctx);

    const uniqueId = `mermaid-diagram-${Date.now()}-${Math.floor(
      Math.random() * 10000
    )}`;

    // Create a mermaid diagram container
    // The actual rendering will be handled by the init method
    return {
      html: `<div class="mermaid-container">
              <div class="mermaid" id="${uniqueId}">${ctx.code}</div>
            </div>`,
      skipNextProcessors: true,
    };
  }

  /**
   * Static method to render mermaid diagrams in the DOM
   * This is separate from the initialize method because it needs DOM access
   */
  public static init({
    container,
    theme,
    isStreaming = false,
  }: MermaidRendererOptions): () => void {
    if (isStreaming || !container) {
      return () => {};
    }

    // Ensure mermaid is initialized
    const tempProcessor = new MermaidProcessor();
    tempProcessor.initialize({ code: '', theme: theme });

    const timeoutId = setTimeout(() => {
      try {
        // Find all mermaid diagrams and initialize them
        if (typeof mermaid !== 'undefined') {
          const mermaidDiagrams =
            container.querySelectorAll<HTMLElement>('.mermaid');

          console.log('Found mermaid diagrams:', mermaidDiagrams.length);

          if (mermaidDiagrams.length > 0) {
            // Log the content of the first diagram for debugging
            if (mermaidDiagrams[0]) {
              const diagramContent = mermaidDiagrams[0].textContent;
              console.log(
                'Rendering diagram with content length:',
                diagramContent?.length
              );
            }

            try {
              mermaid
                .run({
                  nodes: Array.from(mermaidDiagrams),
                })
                .catch((err) => {
                  console.error('Mermaid run error:', err);
                });
            } catch (runError) {
              console.error('Error during mermaid.run:', runError);

              // Fallback: try to render each diagram individually
              mermaidDiagrams.forEach((diagram, index) => {
                try {
                  // Create a container element for the rendered diagram
                  const container = document.createElement('div');
                  container.id = `diagram-${index}-container`;

                  // Using a safer approach to render individual diagrams
                  if (diagram.textContent) {
                    mermaid.parse(diagram.textContent);
                    diagram.innerHTML = `<div class="mermaid-rendered">${diagram.textContent}</div>`;
                  }
                } catch (renderError) {
                  console.error(
                    `Failed to render diagram ${index}:`,
                    renderError
                  );
                }
              });
            }
          }
        }
      } catch (e) {
        console.error('Failed to initialize mermaid diagrams:', e);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }
}

/**
 * Helper function to render mermaid diagrams
 * This is a standalone function for compatibility with existing code
 */
export function renderMermaidDiagrams(
  options: MermaidRendererOptions
): () => void {
  return MermaidProcessor.init(options);
}
