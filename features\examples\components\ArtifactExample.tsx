'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { IArtifact } from '@/types/chat';
import ArtifactRenderer from '@/features/chat/components/ArtifactRenderer';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function ArtifactExample() {
  const [artifacts, setArtifacts] = useState<IArtifact[]>([
    {
      type: 'html',
      title: 'Simple HTML Example',
      content: `
<div style="padding: 20px; background-color: #f0f0f0; border-radius: 8px;">
  <h2 style="color: #333;">HTML Artifact Example</h2>
  <p>This is a simple HTML artifact that demonstrates the feature.</p>
  <button style="background-color: #4CAF50; color: white; padding: 10px 15px; border: none; border-radius: 4px; cursor: pointer;">
    Click me!
  </button>
</div>
      `,
    },
    {
      type: 'html',
      title: 'Interactive Chart Example',
      content: `
<div style="padding: 20px; background-color: #f0f0f0; border-radius: 8px;">
  <h2 style="color: #333;">Interactive Chart</h2>
  <div id="chart-container" style="width: 100%; height: 300px;"></div>
  <script>
    // Create a simple bar chart using div elements
    const container = document.getElementById('chart-container');
    const data = [65, 40, 85, 30, 55, 70];
    const labels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];

    // Create chart
    const chartHtml = data.map((value, index) => {
      const height = value * 2;
      return \`
        <div style="display: inline-block; width: 40px; margin: 0 10px; text-align: center;">
          <div style="background-color: #4CAF50; height: \${height}px; margin-bottom: 5px;"></div>
          <div>\${labels[index]}</div>
          <div>\${value}</div>
        </div>
      \`;
    }).join('');

    container.innerHTML = \`
      <div style="display: flex; justify-content: center; align-items-end; height: 250px;">
        \${chartHtml}
      </div>
    \`;
  </script>
</div>
      `,
    },
  ]);

  const addNewArtifact = () => {
    const newArtifact: IArtifact = {
      type: 'html',
      title: 'Dynamic HTML Artifact',
      content: `
<div style="padding: 20px; background-color: #e0f7fa; border-radius: 8px;">
  <h2 style="color: #00796b;">Dynamic HTML Artifact</h2>
  <p>This artifact was added dynamically at ${new Date().toLocaleTimeString()}</p>
  <div style="display: flex; gap: 10px; margin-top: 15px;">
    <div style="background-color: #4CAF50; color: white; padding: 10px; border-radius: 4px; flex: 1; text-align: center;">Item 1</div>
    <div style="background-color: #2196F3; color: white; padding: 10px; border-radius: 4px; flex: 1; text-align: center;">Item 2</div>
    <div style="background-color: #F44336; color: white; padding: 10px; border-radius: 4px; flex: 1; text-align: center;">Item 3</div>
  </div>
</div>
      `,
    };
    setArtifacts([...artifacts, newArtifact]);
  };

  const markdownExample = `
Here's how to create an HTML artifact in markdown:

\`\`\`html
<div style="padding: 20px; background-color: #f0f0f0; border-radius: 8px;">
  <h2 style="color: #333;">HTML from Markdown</h2>
  <p>This HTML was embedded in a markdown code block with the language set to 'html'.</p>
  <button style="background-color: #4CAF50; color: white; padding: 10px 15px; border: none; border-radius: 4px; cursor: pointer;">
    Click me!
  </button>
</div>
\`\`\`

The code block above will be detected and converted to an HTML artifact.
`;

  return (
    <div className="container mx-auto p-4">
      <h1 className="mb-4 text-2xl font-bold">HTML Artifact Examples</h1>

      <Tabs defaultValue="examples" className="mb-6">
        <TabsList>
          <TabsTrigger value="examples">Examples</TabsTrigger>
          <TabsTrigger value="usage">Usage in Chat</TabsTrigger>
        </TabsList>

        <TabsContent value="examples">
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>HTML Artifacts</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                This example demonstrates how HTML artifacts can be rendered in
                the chat interface. The code is displayed in a code block with a
                "Render HTML" button that opens a sheet from the right side when
                clicked.
              </p>

              <Button onClick={addNewArtifact} className="mb-6">
                Add New Artifact
              </Button>

              <div className="grid gap-4">
                {artifacts.map((artifact, index) => (
                  <ArtifactRenderer key={index} artifact={artifact} />
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="usage">
          <Card>
            <CardHeader>
              <CardTitle>Using HTML Artifacts in Chat</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="prose dark:prose-invert max-w-none">
                <p>
                  When writing a message in markdown, you can create an HTML
                  artifact by using a code block with the language set to{' '}
                  <code>html</code> or <code>html-artifact</code>:
                </p>

                <pre className="bg-muted/50 overflow-x-auto rounded-md p-4">
                  <code className="text-sm">{markdownExample}</code>
                </pre>

                <p className="mt-4">
                  The system will automatically detect this code block, display
                  the HTML code, and add a "Render HTML" button that opens the
                  rendered content in a sheet when clicked.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
