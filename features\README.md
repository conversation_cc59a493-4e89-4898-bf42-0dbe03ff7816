# Features Directory

This directory contains feature-based modules for the application. Each feature folder contains code specific to that feature, keeping things neatly separated.

## Structure

Each feature has the following structure:

```
features/feature-name/
  ├── components/       # Components specific to this feature
  ├── api/              # API requests related to this feature (optional)
  ├── hooks/            # Hooks specific to this feature (optional)
  ├── utils/            # Utility functions for this feature (optional)
  └── types/            # TypeScript types for this feature (optional)
```

## Current Features

- `admin`: Admin dashboard, user management, and group management
- `auth`: Authentication and permission components
- `chat`: Chat interface and related components
- `examples`: Example components
- `stt`: Speech-to-text components
- `support`: Support form and related components
- `workspace`: Workspace components for collections, models, and tools

## Import Examples

### Importing from a feature

```tsx
// Import a component from a feature
import ChatInput from '@/features/chat/components/ChatInput';

// Import a utility from a feature
import { someUtil } from '@/features/feature-name/utils/someUtil';
```

### Importing shared components

```tsx
// Import a shared UI component
import { Button } from '@/components/ui/button';

// Import a shared layout component
import MainLayout from '@/components/MainLayout';
```

## Guidelines

1. Feature components should only be used within their feature or at the app level
2. Don't import across features (e.g., don't import from `features/chat` in `features/workspace`)
3. Shared components in the `components` folder can be used anywhere
4. If a component is used in multiple features, consider moving it to the shared `components` folder
