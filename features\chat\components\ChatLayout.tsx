'use client';

import React from 'react';
import ChatSidebar from './ChatSidebar';
import ChuckContextProvider from '@/contexts/ChuckContext';
import ChatToolbar from './ChatToolbar';
import { useSidebar } from '@/contexts/SidebarContext';
import { cn } from '@/lib/utils';

interface ChatLayoutProps {
  children: React.ReactNode;
  // messages: Message[];
  // onSendMessage: (content: string) => void;
}

export default function ChatLayout({
  children,
  // messages,
  // onSendMessage,
}: ChatLayoutProps) {
  const { store: sidebarStore } = useSidebar();

  return (
    <ChuckContextProvider>
      <div className="flex h-screen">
        <ChatSidebar />
        <div
          className={cn(
            'w-[calc(100%-300px) fixed top-0 left-[300px] z-1 h-[53px] transition-all duration-300 ease-in-out',
            'bg-background border-b 2xl:border-none 2xl:bg-transparent',
            sidebarStore.isMobile
              ? 'bg-background left-0 w-full'
              : 'left-[320px] w-[calc(100%-320px)]'
          )}
        >
          <ChatToolbar />
        </div>
        {children}
      </div>
    </ChuckContextProvider>
  );
}
