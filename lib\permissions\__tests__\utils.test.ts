import { describe, it, expect } from 'vitest';
import {
  hasPermission,
  hasAnyPermission,
  hasAllPermissions,
  isAdmin,
  getUserResources,
  getUserOperations,
} from '../utils';
import { IUserData } from '@/types/user';
import { ResourceType, OperationType } from '../types';

describe('Permission Utilities', () => {
  // Create a mock user with permissions
  const mockUser: IUserData = {
    id: 'user-1',
    name: 'Test User',
    email: '<EMAIL>',
    profile_picture: null,
    groups: [
      {
        id: 'group-1',
        name: 'Admin Group',
        description: 'Group with admin permissions',
        meta: {
          perms: [
            { res: 'admin', ops: ['read'] },
            { res: 'chat', ops: ['read', 'write', 'delete'] },
          ],
        },
      },
      {
        id: 'group-2',
        name: 'User Group',
        description: 'Group with user permissions',
        meta: {
          perms: [
            { res: 'model', ops: ['read'] },
            { res: 'collection', ops: ['read', 'write'] },
          ],
        },
      },
    ],
  };

  // User with no permissions
  const userWithNoPermissions: IUserData = {
    id: 'user-2',
    name: 'No Permissions User',
    email: '<EMAIL>',
    profile_picture: null,
    groups: [
      {
        id: 'group-3',
        name: 'Empty Group',
        description: 'Group with no permissions',
        meta: {
          perms: [],
        },
      },
    ],
  };

  // User with no meta
  const userWithNoMeta: IUserData = {
    id: 'user-3',
    name: 'No Meta User',
    email: '<EMAIL>',
    profile_picture: null,
    groups: [
      {
        id: 'group-4',
        name: 'No Meta Group',
        description: 'Group with no meta',
      },
    ],
  };

  describe('hasPermission', () => {
    it('should return true when user has the permission', () => {
      expect(hasPermission(mockUser, 'admin', 'read')).toBe(true);
      expect(hasPermission(mockUser, 'chat', 'write')).toBe(true);
      expect(hasPermission(mockUser, 'collection', 'read')).toBe(true);
    });

    it('should return false when user does not have the permission', () => {
      expect(hasPermission(mockUser, 'admin', 'write')).toBe(false);
      expect(hasPermission(mockUser, 'user', 'read')).toBe(false);
    });

    it('should return false for null user', () => {
      expect(hasPermission(null, 'admin', 'read')).toBe(false);
    });

    it('should return false for user with no permissions', () => {
      expect(hasPermission(userWithNoPermissions, 'admin', 'read')).toBe(false);
    });

    it('should return false for user with no meta', () => {
      expect(hasPermission(userWithNoMeta, 'admin', 'read')).toBe(false);
    });
  });

  describe('hasAnyPermission', () => {
    it('should return true when user has any of the permissions', () => {
      expect(
        hasAnyPermission(mockUser, [
          { resource: 'admin', operation: 'write' },
          { resource: 'chat', operation: 'read' },
        ])
      ).toBe(true);
    });

    it('should return false when user has none of the permissions', () => {
      expect(
        hasAnyPermission(mockUser, [
          { resource: 'admin', operation: 'delete' },
          { resource: 'user', operation: 'read' },
        ])
      ).toBe(false);
    });

    it('should return false for null user', () => {
      expect(
        hasAnyPermission(null, [{ resource: 'admin', operation: 'read' }])
      ).toBe(false);
    });
  });

  describe('hasAllPermissions', () => {
    it('should return true when user has all of the permissions', () => {
      expect(
        hasAllPermissions(mockUser, [
          { resource: 'admin', operation: 'read' },
          { resource: 'chat', operation: 'read' },
        ])
      ).toBe(true);
    });

    it('should return false when user does not have all of the permissions', () => {
      expect(
        hasAllPermissions(mockUser, [
          { resource: 'admin', operation: 'read' },
          { resource: 'admin', operation: 'write' },
        ])
      ).toBe(false);
    });

    it('should return false for null user', () => {
      expect(
        hasAllPermissions(null, [{ resource: 'admin', operation: 'read' }])
      ).toBe(false);
    });
  });

  describe('isAdmin', () => {
    it('should return true when user has admin read permission', () => {
      expect(isAdmin(mockUser)).toBe(true);
    });

    it('should return false when user does not have admin read permission', () => {
      const nonAdminUser = {
        ...mockUser,
        groups: [mockUser.groups[1]], // Only include the non-admin group
      };
      expect(isAdmin(nonAdminUser)).toBe(false);
    });

    it('should return false for null user', () => {
      expect(isAdmin(null)).toBe(false);
    });
  });

  describe('getUserResources', () => {
    it('should return all resources the user has access to', () => {
      const resources = getUserResources(mockUser);
      expect(resources).toContain('admin');
      expect(resources).toContain('chat');
      expect(resources).toContain('model');
      expect(resources).toContain('collection');
      expect(resources).not.toContain('user');
      expect(resources.length).toBe(4);
    });

    it('should return empty array for null user', () => {
      expect(getUserResources(null)).toEqual([]);
    });

    it('should return empty array for user with no permissions', () => {
      expect(getUserResources(userWithNoPermissions)).toEqual([]);
    });

    it('should return empty array for user with no meta', () => {
      expect(getUserResources(userWithNoMeta)).toEqual([]);
    });
  });

  describe('getUserOperations', () => {
    it('should return all operations the user can perform on a specific resource', () => {
      const chatOperations = getUserOperations(mockUser, 'chat');
      expect(chatOperations).toContain('read');
      expect(chatOperations).toContain('write');
      expect(chatOperations).toContain('delete');
      expect(chatOperations.length).toBe(3);

      const modelOperations = getUserOperations(mockUser, 'model');
      expect(modelOperations).toContain('read');
      expect(modelOperations).not.toContain('write');
      expect(modelOperations.length).toBe(1);
    });

    it('should return empty array for resource the user does not have access to', () => {
      expect(getUserOperations(mockUser, 'user')).toEqual([]);
    });

    it('should return empty array for null user', () => {
      expect(getUserOperations(null, 'admin')).toEqual([]);
    });

    it('should return empty array for user with no permissions', () => {
      expect(getUserOperations(userWithNoPermissions, 'admin')).toEqual([]);
    });

    it('should return empty array for user with no meta', () => {
      expect(getUserOperations(userWithNoMeta, 'admin')).toEqual([]);
    });
  });
});
