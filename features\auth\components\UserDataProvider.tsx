'use client';

import { useEffect } from 'react';
import { useFetchMe } from '@/features/auth/api/useFetchMe';
import { useGlobal } from '@/contexts/GlobalContext';
import Cookies from 'js-cookie';

/**
 * This component fetches user data and updates the global context.
 * It should be rendered inside the QueryClientProvider but outside of
 * components that need access to user data.
 */
export function UserDataProvider({ children }: { children: React.ReactNode }) {
  const { store, setStore } = useGlobal();
  const { data: userData, isLoading, error } = useFetchMe();

  // Update user data in global store when it changes
  useEffect(() => {
    if (userData) {
      console.log('User data fetched:', userData);

      // Save user ID to cookies with no expiration
      if (userData.id) {
        try {
          Cookies.set('userId', userData.id);
          console.log('User ID saved to cookies:', userData.id);
        } catch (err) {
          console.error('Failed to save user ID to cookies:', err);
        }
      }

      setStore((prevStore) => ({
        ...prevStore,
        user: userData,
        isLoadingUser: false,
      }));
    } else {
      setStore((prevStore) => ({
        ...prevStore,
        isLoadingUser: isLoading && !error,
      }));
    }

    if (error) {
      console.error('Error fetching user data:', error);
    }
  }, [userData, isLoading, error, setStore]);

  // This component doesn't render anything itself
  return <>{children}</>;
}
