import { NextRequest, NextResponse } from 'next/server';
import axiosInstanceApi from '@/lib/axiosInstance.api';

// Helper function to forward requests
async function forwardRequest(
  request: NextRequest,
  method: string,
  path: string[]
) {
  try {
    const authorization = request.headers.get('Authorization');
    const targetPath = `/${path.join('/')}`;

    // Extract query parameters
    const url = new URL(request.url);
    const searchParams = url.searchParams;

    // Prepare request config
    const config: any = {
      headers: {
        ...(authorization && { Authorization: authorization }),
      },
      params: Object.fromEntries(searchParams.entries()),
    };

    // Handle request body for methods that support it
    let data;
    if (['POST', 'PUT', 'PATCH'].includes(method)) {
      const contentType = request.headers.get('content-type') || '';

      if (contentType.includes('application/json')) {
        data = await request.json();
        config.headers['Content-Type'] = 'application/json';
      } else if (contentType.includes('multipart/form-data')) {
        data = await request.formData();
        // Let axios handle multipart content-type header
      } else if (contentType.includes('application/x-www-form-urlencoded')) {
        data = await request.text();
        config.headers['Content-Type'] = 'application/x-www-form-urlencoded';
      } else {
        data = await request.text();
      }
    }

    console.log(
      `[BFF Proxy] Forwarding ${method} ${targetPath} with Auth: ${!!authorization}`
    );

    // Make the API call
    const response = await axiosInstanceApi.request({
      method: method.toLowerCase(),
      url: targetPath,
      data,
      ...config,
    });

    return NextResponse.json(response.data, { status: response.status });
  } catch (error: any) {
    console.error(
      `[BFF Proxy Error] ${method} ${path.join('/')}:`,
      error.response?.data || error.message
    );

    const status = error.response?.status || 500;
    const message =
      error.response?.data?.message || `Proxy error for ${method} request`;

    return NextResponse.json({ message }, { status });
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  const { path } = await params;
  return forwardRequest(request, 'GET', path);
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  const { path } = await params;
  return forwardRequest(request, 'POST', path);
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  const { path } = await params;
  return forwardRequest(request, 'PUT', path);
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  const { path } = await params;
  return forwardRequest(request, 'PATCH', path);
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  const { path } = await params;
  return forwardRequest(request, 'DELETE', path);
}
