import { IGroupDataBase } from '../group';

export enum ModelTypeEnum {
  MODEL = 'model',
  ASSISTANT = 'assistant',
}

export interface ICollectionData {
  id: string;
  name: string;
  description: string;
  config: Record<string, any>;
  created_at: string;
  modified_at: string;
  owner_id: string;
  owner_name: string;
  group_id: string;
  collection_id: string;
  user_id: string;
  documents?: ICollectionDocument[];
  groups?: IGroupDataBase[];
}

export interface ICollectionDocument {
  id: string;
  collection_id: string;
  created_at: string;
  modified_at: string;
  metadata: {
    name: string;
    tags?: string[];
    ingestion: {
      parsed: boolean;
      status: string;
      llamaJobId: string;
      indexedNodes: number;
    };
  };
}

export interface ICollectionCreate {
  groupIds: string[];
  name: string;
  description: string;
}
