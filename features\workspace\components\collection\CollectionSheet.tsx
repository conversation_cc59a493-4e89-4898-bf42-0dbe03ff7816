'use client';

import React, { useEffect, useRef, useMemo } from 'react';

import {
  She<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTitle,
} from '@/components/ui/sheet';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useFetchCollectionById } from '@/features/workspace/api/useFetchCollectionById';
import { useFetchGroups } from '@/features/admin/api/useFetchGroups';
import LoadingSpinner from '@/components/LoadingSpinner';
import CollectionForm from './CollectionForm'; // Import the Collection form
import { ICollectionData, ICollectionDocument } from '@/types/collection';

interface CollectionSheetProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  collection?: ICollectionData | null; // Input collection for editing
}

export default function CollectionSheet({
  isOpen,
  onOpenChange,
  collection,
}: CollectionSheetProps) {
  const isEditMode = !!collection?.id;

  // Reference to store the polling interval
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Polling interval in milliseconds (5 seconds)
  const POLLING_INTERVAL = 5000;

  // Function to check if any documents are in pending state
  const hasPendingDocuments = (documents?: ICollectionDocument[]): boolean => {
    if (!documents || documents.length === 0) return false;
    return documents.some((doc) => doc.metadata.ingestion.status === 'pending');
  };

  // Fetch detailed data only in edit mode when the sheet is open
  // Use standard caching unless explicitly disabled elsewhere
  const {
    data: detailedCollectionData,
    isLoading: isFetchingDetailedData,
    isError,
    refetch,
  } = useFetchCollectionById(collection?.id, {
    enabled: isOpen && isEditMode,
  });

  // Set up polling when documents are in pending state
  useEffect(() => {
    // Clear any existing interval when component unmounts or dependencies change
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
      pollingIntervalRef.current = null;
    }

    // Only set up polling if we have pending documents and the sheet is open
    if (
      isOpen &&
      detailedCollectionData &&
      hasPendingDocuments(detailedCollectionData.documents)
    ) {
      console.log('Setting up polling for pending documents');

      // Set up new polling interval
      pollingIntervalRef.current = setInterval(() => {
        console.log('Polling for document status updates');
        refetch();
      }, POLLING_INTERVAL);
    }

    // Cleanup function to clear interval when component unmounts or dependencies change
    return () => {
      if (pollingIntervalRef.current) {
        console.log('Clearing document polling interval');
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }
    };
  }, [isOpen, detailedCollectionData, refetch]);

  // Check if there are pending documents
  const isPendingDocuments =
    detailedCollectionData &&
    hasPendingDocuments(detailedCollectionData.documents);

  // Set the sheet title with a processing indicator if needed
  const sheetTitle = isEditMode ? 'Edit Collection' : 'Create Collection';

  // Fetch groups for the selector
  const { data: groups, isLoading: isLoadingGroups } = useFetchGroups();

  // Create group options for the selector
  const groupOptions = useMemo(() => {
    if (!groups) return [];
    return groups.map((group) => ({ value: group.id, label: group.name }));
  }, [groups]);

  // Determine the data to pass to the form
  // In edit mode, wait for detailed data to load, otherwise pass null
  const formCollectionData = isEditMode ? detailedCollectionData : null;

  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetContent
        className="sm:max-w-[1000px]" // Adjust width as needed
        onEscapeKeyDown={(e: KeyboardEvent) => e.preventDefault()} // Keep ESC disabled
      >
        <ScrollArea className="h-full w-full pr-6">
          {' '}
          {/* Add padding-right for scrollbar */}
          <SheetHeader className="pt-4 pl-4">
            {' '}
            {/* Adjust padding */}
            <SheetTitle className="flex items-center gap-2">
              {sheetTitle}
            </SheetTitle>
          </SheetHeader>
          {/* Show loading spinner only in edit mode while fetching */}
          {(isEditMode && isFetchingDetailedData) || isLoadingGroups ? (
            <div className="flex h-[200px] items-center justify-center p-8">
              <LoadingSpinner />
            </div>
          ) : isEditMode && isError ? ( // Handle fetch error in edit mode
            <div className="text-destructive p-4">
              Failed to load collection details.
            </div>
          ) : (
            // Render form in create mode, or after data loads/fails in edit mode
            // Pass potentially loaded detailed data (or null) to the form
            <CollectionForm
              onOpenChange={onOpenChange}
              collection={formCollectionData}
              groupOptions={groupOptions}
            />
          )}
        </ScrollArea>
      </SheetContent>
    </Sheet>
  );
}
