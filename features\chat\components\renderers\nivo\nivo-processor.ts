import {
  ContentProcessor,
  ContentProcessorContext,
  ProcessedContent,
} from '@/features/chat/components/content-processors/types';

export class NivoProcessor implements ContentProcessor {
  initialize(ctx: ContentProcessorContext): void {}

  canProcess(ctx: ContentProcessorContext): boolean {
    if (!ctx.code) {
      return false;
    }

    if (ctx.lang === 'html' && ctx.code.includes('<nivoChart>')) {
      return true;
    }

    if (ctx.lang === 'json') {
      try {
        const data = JSON.parse(ctx.code);

        return !!(data.type && data.data && Array.isArray(data.data));
      } catch (error) {
        return false;
      }
    }

    return false;
  }

  process(ctx: ContentProcessorContext): ProcessedContent {
    try {
      if (ctx.lang === 'html' && ctx.code.includes('<nivoChart>')) {
        const nivoChartMatch = ctx.code.match(
          /<nivoChart>([\s\S]*?)<\/nivoChart>/
        );
        if (nivoChartMatch) {
          const jsonContent = nivoChartMatch[1].trim();
          try {
            const data = JSON.parse(jsonContent);
            if (data.type && data.data && Array.isArray(data.data)) {
              return {
                html: `<nivoChart>${jsonContent}</nivoChart>`,
                skipNextProcessors: true,
              };
            }
          } catch (jsonError) {
            return {
              html: `<div class="flex items-center justify-center p-4 text-red-500 border border-red-300 rounded">
                Invalid JSON in nivoChart: ${jsonError instanceof Error ? jsonError.message : 'Unknown error'}
              </div>`,
              skipNextProcessors: true,
            };
          }
        }
      }

      if (ctx.lang === 'json') {
        const data = JSON.parse(ctx.code);

        if (data.type && data.data && Array.isArray(data.data)) {
          return {
            html: `<nivoChart>${ctx.code}</nivoChart>`,
            skipNextProcessors: true,
          };
        }
      }
    } catch (error) {
      console.error('Error processing Nivo chart:', error);

      if (ctx.code.includes('"type"') && ctx.code.includes('"data"')) {
        return {
          html: `<div class="flex items-center justify-center p-4 text-red-500 border border-red-300 rounded">
            Invalid Nivo chart configuration: ${error instanceof Error ? error.message : 'Unknown error'}
          </div>`,
          skipNextProcessors: true,
        };
      }
    }

    return {
      html: '',
      skipNextProcessors: false,
    };
  }
}
