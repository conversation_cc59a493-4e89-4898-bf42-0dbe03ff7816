/**
 * Permission types and utilities for the application
 */

// Define the resource types that can be protected
export type ResourceType =
  | 'admin'
  | 'workspace'
  | 'chat'
  | 'collection'
  | 'model'
  | 'group'
  | 'user';

// Define the operation types that can be performed on resources
export type OperationType = 'read' | 'write' | 'delete' | 'create' | 'update';

// Define the permission structure
export interface Permission {
  res: ResourceType;
  ops: OperationType[];
}

// Define the group metadata structure
export interface GroupMeta {
  perms: Permission[];
}

// Define the group structure with metadata
export interface GroupWithPermissions {
  id: string;
  name: string;
  description: string | null;
  meta?: GroupMeta;
  owner_id: string;
}
