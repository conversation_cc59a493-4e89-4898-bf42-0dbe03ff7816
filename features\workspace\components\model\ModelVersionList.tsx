'use client';

import React from 'react';
import { IModelVersion } from '@/types/model';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Clock, User } from 'lucide-react';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import { cn } from '@/lib/utils';

// Extend dayjs with relative time plugin
dayjs.extend(relativeTime);

interface ModelVersionListProps {
  versions: IModelVersion[];
  selectedVersionId?: string;
  onVersionSelect: (version: IModelVersion) => void;
  currentVersionId?: string;
}

// Helper function to get user initials
const getInitials = (name: string): string => {
  return name
    .split(' ')
    .map((word) => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 3);
};

export default function ModelVersionList({
  versions,
  selectedVersionId,
  onVersionSelect,
  currentVersionId,
}: ModelVersionListProps) {
  const formatDate = (dateString: string) => {
    try {
      return dayjs(dateString).fromNow();
    } catch (error) {
      return 'Unknown date';
    }
  };

  const formatFullDate = (dateString: string) => {
    try {
      return dayjs(dateString).format('MMM D, YYYY h:mm A');
    } catch (error) {
      return dateString;
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">History</h3>
        <Badge variant="outline" className="text-xs">
          {versions.length} version{versions.length !== 1 ? 's' : ''}
        </Badge>
      </div>

      <div className="h-[calc(100vh-180px)] w-full">
        <div className="space-y-2">
          {versions.map((version, index) => {
            const isSelected = selectedVersionId === version.id;
            const isLatest = index === 0;

            return (
              <div
                key={version.id}
                className={cn(
                  'h-auto w-full cursor-pointer justify-start rounded-lg p-4',
                  isSelected && 'bg-accent text-foreground',
                  !isSelected && 'hover:bg-accent/50'
                )}
                onClick={() => onVersionSelect(version)}
              >
                <div className="flex w-full items-start gap-3">
                  <div className="min-w-0 flex-1 space-y-1">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">
                        Version {version.version_number}
                      </span>
                      {/* {isCurrent && (
                        <Badge variant="secondary" className="text-xs">
                          Current
                        </Badge>
                      )} */}
                      {isLatest && (
                        <Badge variant="default" className="text-xs">
                          Latest
                        </Badge>
                      )}
                    </div>

                    <div className="text-muted-foreground flex items-center gap-2 text-xs">
                      <span className="truncate">
                        {version.created_by_name}
                      </span>
                    </div>

                    {/* <div className="text-muted-foreground flex items-center gap-2 text-xs">
                      <Clock className="h-3 w-3" />
                      <span title={formatFullDate(version.created_at)}>
                        {formatDate(version.created_at)}
                      </span>
                    </div>

                    {version.change_summary && (
                      <p className="text-muted-foreground line-clamp-2 text-xs">
                        {version.change_summary}
                      </p>
                    )} */}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
