.pulse {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  pointer-events: none;

  &.pulse1 {
    animation: pulseBorder 2s infinite;
    animation-delay: 0.3s;
  }
  &.pulse2 {
    animation: pulseBorder 2s infinite;
    animation-delay: 0.7s;
  }
}

@keyframes pulseBorder {
  0% {
    transform: scale(0.4);
    opacity: 1;
    border: 1px solid var(--primary);
  }
  100% {
    transform: scale(1.15);
    opacity: 0;
  }
}
