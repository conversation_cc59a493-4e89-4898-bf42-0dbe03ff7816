import { useMutation, useQueryClient, QueryKey } from '@tanstack/react-query';
import axiosInstanceUi from '@/lib/axiosInstance.ui';
import { API_CONFIG } from '@/config/api';
import { toast } from 'sonner';

// Function to toggle a chat's pinned status
const toggleChatPin = async ({
  chatId,
  pinned,
}: ToggleChatPinInput): Promise<void> => {
  // Use the client-side instance that includes the auth token
  await axiosInstanceUi.patch(
    `${API_CONFIG.chat.chat}/${chatId}`,
    { pinned } // Send only the pinned status
  );
  // No return needed if successful
};

// Define the input type for the mutation
interface ToggleChatPinInput {
  chatId: string;
  pinned: boolean;
}

export function useToggleChatPin() {
  const queryClient = useQueryClient();

  return useMutation<void, Error, ToggleChatPinInput>({
    mutationFn: toggleChatPin,
    onSuccess: (data, variables) => {
      const { chatId, pinned } = variables;
      const action = pinned ? 'pinned' : 'unpinned';
      console.log(`Chat with ID ${chatId} ${action} successfully.`);
      toast.success(`Chat ${action} successfully!`);

      const chatListQueryKey: QueryKey = [API_CONFIG.chat.chat];
      const specificChatQueryKey: QueryKey = [API_CONFIG.chat.chat, chatId];

      // 1. Invalidate the chat list query to refresh the sidebar
      queryClient.invalidateQueries({ queryKey: chatListQueryKey });

      // 2. Invalidate the specific chat query to refresh the chat data
      queryClient.invalidateQueries({ queryKey: specificChatQueryKey });
    },
    onError: (error) => {
      console.error('Error toggling chat pin status:', error);
      toast.error('Failed to update chat pin status.');
    },
  });
}
