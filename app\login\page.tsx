'use client';

import { useGlobal } from '@/contexts/GlobalContext';
import { Button } from '@/components/ui/button'; // Assuming you have a Button component
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { useMsal } from '@azure/msal-react';
import Image from 'next/image';
import Cookies from 'js-cookie';
import axiosInstanceUi from '@/lib/axiosInstance.ui';
import { API_CONFIG } from '@/config/api';
import { Loader2 } from 'lucide-react';
import { useTheme } from 'next-themes';

export default function LoginPage() {
  const router = useRouter();
  const { instance, inProgress } = useMsal();
  const activeAccount = instance.getActiveAccount();
  const {
    store: { token },
    setStore,
  } = useGlobal();

  const handleRedirect = () => {
    instance
      .loginRedirect({
        scopes: [],
        prompt: 'create',
      })
      .catch((error) => console.log(error));
  };

  const login = async ({ msIdToken }: { msIdToken: string | undefined }) => {
    // sessionStorage.clear();
    //   localStorage.clear();
    await axiosInstanceUi
      .post(`${API_CONFIG.auth.signIn}`, { msIdToken })
      .then((response) => {
        const token = response.data.token;
        setStore((prevStore) => ({
          ...prevStore,
          token,
        }));
        Cookies.set('token', token, {
          expires: 1,
        });
      })
      .catch((error) => {
        console.error(error);
      });
  };

  useEffect(() => {
    if (token) {
      router.push('/c');
    }
  }, [token]);

  useEffect(() => {
    if (activeAccount !== null && inProgress === 'none') {
      login({ msIdToken: activeAccount.idToken });
    }
  }, [inProgress]);

  const { theme } = useTheme();

  return (
    <div className="flex min-h-screen min-w-screen items-center justify-center">
      {activeAccount === null && inProgress === 'none' ? (
        <div className="flex flex-col space-y-4">
          <div className="flex h-[80px] items-center justify-center">
            {theme === 'dark' ? (
              <Image
                src="/chuck-logo-pill.svg"
                className="ml-[-6px]"
                alt="Mawer Logo"
                width={200}
                height={60}
              />
            ) : (
              <Image
                src="/chuck-logo-pill.svg"
                className="ml-[-6px]"
                alt="Mawer Logo"
                width={200}
                height={60}
              />
            )}
          </div>
          <div className="flex justify-center">
            <Button
              variant="outline"
              className="rounded-full"
              onClick={handleRedirect}
              size="lg"
            >
              Start
            </Button>
          </div>
        </div>
      ) : (
        <Loader2 className="animate-spin" />
      )}
    </div>
  );
}
