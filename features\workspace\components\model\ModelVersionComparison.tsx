'use client';

import React from 'react';
import { IModelData, IModelVersion } from '@/types/model';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import dayjs from 'dayjs';

interface ModelVersionComparisonProps {
  currentModel: IModelData;
  selectedVersion?: IModelVersion;
}

interface ComparisonFieldProps {
  label: string;
  currentValue: any;
  versionValue?: any;
  isChanged?: boolean;
}

const ComparisonField: React.FC<ComparisonFieldProps> = ({
  label,
  currentValue,
  versionValue,
  isChanged = false,
}) => {
  const formatValue = (value: any): string => {
    if (value === null || value === undefined) return 'Not set';
    // if (Array.isArray(value)) {
    //   return value.length > 0 ? value.join(', ') : 'None';
    // }

    if (typeof value === 'object') {
      return JSON.stringify(value, null, 2);
    }
    // Convert literal escape sequences to actual characters for proper rendering in <pre>
    return String(value)
      .replace(/\\n/g, '\n')
      .replace(/\\t/g, '\t')
      .replace(/\\r/g, '\r');
  };

  return (
    <div className="space-y-2">
      <h4 className="flex items-center gap-2 text-sm font-medium">
        {label}
        {isChanged && <Badge className="text-xs">Changed</Badge>}
      </h4>
      <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
        <div className="space-y-1">
          <p className="text-xs font-bold font-medium text-blue-500">New</p>
          <div className="bg-muted rounded-md p-3">
            <pre className="text-xs break-words whitespace-pre-wrap">
              {formatValue(currentValue)}
            </pre>
          </div>
        </div>
        {versionValue !== undefined && (
          <div className="space-y-1">
            <p className="text-muted-foreground text-xs font-medium">Old</p>
            <div
              className={`rounded-md p-3 ${isChanged ? 'bg-blue-50 dark:bg-blue-950/20' : 'bg-muted'}`}
            >
              <pre className="text-xs break-words whitespace-pre-wrap">
                {formatValue(versionValue)}
              </pre>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default function ModelVersionComparison({
  currentModel,
  selectedVersion,
}: ModelVersionComparisonProps) {
  if (!selectedVersion) {
    return (
      <Card>
        <CardContent className="flex h-full items-center justify-center">
          <p className="text-muted-foreground">Select a version to compare</p>
        </CardContent>
      </Card>
    );
  }

  const versionSnapshot = selectedVersion.snapshot;
  const changes = selectedVersion.changes;

  const formatDate = (dateString: string) => {
    try {
      return dayjs(dateString).format('MMM D, YYYY h:mm A');
    } catch (error) {
      return dateString;
    }
  };

  return (
    <Card className="rounded-lg shadow-none">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg">
          Version {selectedVersion.version_number}
        </CardTitle>
        <div className="text-muted-foreground flex items-center gap-4 text-sm">
          <span>Created: {formatDate(selectedVersion.created_at)}</span>
          <span>By: {selectedVersion.created_by_name}</span>
        </div>
        {selectedVersion.change_summary && (
          <p className="text-muted-foreground text-sm">
            {selectedVersion.change_summary}
          </p>
        )}
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[850px]">
          <div className="space-y-6">
            {/* <ComparisonField
              label="Name"
              currentValue={currentModel.name}
              versionValue={versionSnapshot.name}
              isChanged={changes?.name !== undefined}
            />

            <Separator />

            <ComparisonField
              label="Description"
              currentValue={currentModel.description}
              versionValue={versionSnapshot.description}
              isChanged={changes?.description !== undefined}
            /> */}

            {/* <Separator /> */}

            {changes?.base_model && (
              <ComparisonField
                label="Base Model"
                currentValue={changes?.base_model?.to}
                versionValue={changes?.base_model?.from}
              />
            )}

            {changes?.config && (
              <ComparisonField
                label="Prompt"
                currentValue={changes?.config?.to?.prompt}
                versionValue={changes?.config?.from?.prompt}
              />
            )}

            {changes?.config && (
              <ComparisonField
                label="Prompt Suggestions"
                currentValue={changes?.config?.to?.prompt_suggestions}
                versionValue={changes?.config?.from?.prompt_suggestions}
              />
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
