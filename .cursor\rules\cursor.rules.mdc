---
description: 
globs: 
alwaysApply: false
---
# Cursor Rules for Chuck Studio UI

## Project Overview

**Project Name:** Chuck Studio UI
**Goal:** Front-end for the AI platform for Mawer.
**Framework:** Next.js (App Router)
**Language:** TypeScript

## Key Technologies

*   **Next.js:** App Router (`app/` directory), Server & Client Components, API Routes.
*   **Styling:** Tailwind CSS, configured via `tailwind.config.ts` (if created).
*   **UI Components:** Shadcn UI (using Radix UI + Tailwind), custom components in `components/`.
*   **Data Fetching (Client):** React Query (`@tanstack/react-query`), likely via custom hooks (e.g., `useFetchModels`, `useFetchChats`).
*   **HTTP Client:** Axios, with configured instances (`axiosInstance.ui.ts` for client, `axiosInstance.api.ts` for server API routes) and interceptors (token handling, error logging).
*   **Authentication:** MSAL (`@azure/msal-react`, `@azure/msal-browser`) via `MsalProvider` and hooks like `useMsal`.
*   **State Management:** React Context (e.g., `ChuckContext`), potentially combined with MSAL state.
*   **Formatting:** Prettier with `prettier-plugin-tailwindcss`, configured in `.prettierrc.js`.

## General Guidelines

1.  **Code Style:** Follow standard React/TypeScript best practices. Use functional components and hooks. Keep components focused and modular.
2.  **Imports:** Use absolute path aliases (`@/components/...`, `@/lib/...`). Group imports logically.
3.  **Comments:** Add comments only for complex or non-obvious logic. Avoid explaining trivial code.
4.  **Error Handling:** Use `try...catch` for async operations. Utilize React Query's `error` state for data fetching errors. Leverage Axios response interceptors for global API error handling (like 401s). Provide user feedback for errors where appropriate.
5.  **State Management:**
    *   Be mindful of Client vs. Server component boundaries when using state and hooks.
    *   Use established contexts (`ChuckContext`, MSAL via `useMsal`) where applicable.
    *   For shared client-side state, prefer Context or state management libraries over prop drilling for deep trees.

## Technology-Specific Guidelines

### Tailwind CSS

*   **Utility-First:** Prioritize using Tailwind utility classes directly in `className`.
*   **`cn` Utility:** Use the `cn` utility (from `lib/utils`) for conditional or merged class names, especially within components.
*   **Dynamic Classes:** Avoid constructing arbitrary value classes dynamically like `` `w-[${width}px]` `` directly in JSX if possible, as Tailwind's JIT might miss them. Prefer:
    *   CSS variables (`w-[var(--my-width)]` with the variable set via inline style).
    *   Extending the theme in `tailwind.config.ts` (requires creating the file if it doesn't exist).
*   **Theme:** Refer to `tailwind.config.ts` for any custom theme extensions (colors, spacing, etc.).

### Next.js (App Router)

*   **Component Types:** Use `'use client';` directive for components needing hooks (`useState`, `useEffect`, `useContext`, `useRouter`, etc.) or browser APIs. Default to Server Components where possible.
*   **Routing:**
    *   Use `next/navigation`'s `useRouter()` hook for programmatic navigation (`router.push`, `router.replace`).
    *   Use `next/navigation`'s `usePathname()` hook to get the current path.
    *   Use the `<Link>` component from `next/link` for declarative navigation.
*   **API Routes:**
    *   Located in `app/api/.../route.ts`.
    *   Use `NextRequest` and `NextResponse` from `next/server`.
    *   Use the server-side Axios instance (`@/lib/axiosInstance.api.ts`) or standard `fetch`/`axios` for making requests *from* the API route. Do not use the UI Axios instance here.
    *   Use environment variables (e.g., `process.env.API_BASE_URL`) for backend URLs accessed server-side.

### React Query (`@tanstack/react-query`)

*   **Custom Hooks:** Prefer using existing custom hooks (like `useFetchModels`, `useFetchChats`) over calling `useQuery` directly in components for data fetching. Create new hooks for new data types.
*   **Query Keys:** Use descriptive, unique array keys (e.g., `['chats']`, `['models']`).
*   **State Handling:** Handle `isPending`, `error`, and `data` states within the component's return JSX. **Do not cause early returns before all hooks are called.**

### Shadcn UI

*   **Use Existing:** Leverage components from `components/ui/` when available.
*   **Styling:** Modify appearance primarily via the `className` prop using Tailwind utilities and the `cn` function.
*   **Composition:** Follow the intended composition patterns (e.g., `Select > SelectTrigger + SelectContent > SelectItem`).

### Axios

*   **Use Instances:**
    *   Use `axiosInstanceUi` (from `lib/axiosInstance.ui.ts`) for requests made from **client-side** components/hooks (it handles attaching the `localStorage` token).
    *   Use `axiosInstanceApi` (from `lib/axiosInstance.api.ts`) for requests made from **server-side** Next.js API routes to the actual backend.
*   **Interceptors:** Be aware of the interceptors configured on `axiosInstanceUi` (attaching auth token, basic 401 handling).

### Authentication (MSAL)

*   **Provider:** The `MsalProvider` is configured in `ClientProviders.tsx`, wrapping `AuthGuard`.
*   **Hooks:** Use `useMsal()` hook within Client Components to access the `instance` and `accounts`.
*   **State:** Check `inProgress` from `useMsal()` before relying on auth state or making MSAL calls, especially after page loads/redirects.
*   **Token Management:** Use the centralized `lib/tokenStorage.ts` functions (`getStoredToken`, `setStoredToken`, `clearStoredToken`) for interacting with the custom backend token stored in `localStorage`. The initial exchange happens in `AuthGuard`.

## Code Formatting

*   **Prettier:** The project is configured with Prettier and `prettier-plugin-tailwindcss` (`.prettierrc.js`).
*   **Format on Save:** Ensure your IDE is configured to "Format on Save" using the Prettier extension (`esbenp.prettier-vscode`).

## Things to Avoid

*   Directly manipulating `localStorage` outside of `lib/tokenStorage.ts`.
*   Hardcoding API URLs or sensitive keys; use environment variables.
*   Complex logic directly within JSX; extract into helper functions or hooks.
*   Early returns in components before all top-level hooks have run.
*   Mixing client-side logic/hooks in Server Components without `'use client';`.
*   Using the client-side Axios instance (`axiosInstanceUi`) within server-side API routes.

---

These rules should provide a good starting point for collaborating on the Chuck Studio UI project. Let me know if you want any adjustments!
