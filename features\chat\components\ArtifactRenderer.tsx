'use client';

import { useState, useEffect, useRef } from 'react';
import { IArtifact } from '@/types/chat';
import { VisuallyHidden } from '@/components/ui/visually-hidden';
import { Button } from '@/components/ui/button';
import { ExternalLink } from 'lucide-react';
import hljs from 'highlight.js';
import 'highlight.js/styles/github-dark.css';
import { cn } from '@/lib/utils';

interface ArtifactRendererProps {
  artifact: IArtifact;
}

export default function ArtifactRenderer({ artifact }: ArtifactRendererProps) {
  const [isOpen, setIsOpen] = useState(false);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [iframeContent, setIframeContent] = useState<string>('');

  // Generate the HTML content for the iframe
  useEffect(() => {
    if (artifact.type === 'html') {
      const htmlContent = `<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <style>
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      line-height: 1.5;
      padding: 1rem;
      margin: 0;
      color: #333;
      background-color: white;
    }
    @media (prefers-color-scheme: dark) {
      body {
        color: #eee;
        background-color: #1a1a1a;
      }
    }
  </style>
</head>
<body>
  ${artifact.content}
</body>
</html>`;

      setIframeContent(htmlContent);
    }
  }, [artifact.type, artifact.content]);

  // Handle iframe loading
  useEffect(() => {
    if (isOpen && iframeRef.current) {
      // Set a small timeout to ensure the iframe is fully mounted
      const timeoutId = setTimeout(() => {
        const iframe = iframeRef.current;
        if (iframe && iframe.contentWindow) {
          try {
            // Create a blob URL from the HTML content
            const blob = new Blob([iframeContent], { type: 'text/html' });
            const url = URL.createObjectURL(blob);

            // Set the iframe src to the blob URL
            iframe.src = url;

            console.log('Iframe content set via blob URL');

            // Clean up the blob URL when the component unmounts
            return () => URL.revokeObjectURL(url);
          } catch (error) {
            console.error('Error setting iframe content:', error);
          }
        }
      }, 300);

      return () => clearTimeout(timeoutId);
    }
  }, [isOpen, iframeContent]);

  // We're not using DOMPurify for the iframe content anymore

  // Function to highlight HTML code
  const highlightHTML = (code: string) => {
    try {
      return hljs.highlight(code, {
        language: 'html',
        ignoreIllegals: true,
      }).value;
    } catch (e) {
      console.error('Failed to highlight HTML code:', e);
      return code;
    }
  };

  // We don't need to use useEffect to highlight code anymore
  // as we're handling the highlighting directly in the highlightHTML function

  // Handle different artifact types
  const renderArtifact = () => {
    switch (artifact.type) {
      case 'html':
        return (
          <div className="relative mb-8">
            <pre className="hljs github-dark bg-muted/50 relative my-2 overflow-x-auto rounded-md p-4">
              <code
                className="language-html font-mono text-sm"
                dangerouslySetInnerHTML={{
                  __html: highlightHTML(artifact.content),
                }}
              />
            </pre>
            <button
              title="Render HTML"
              type="button"
              className="render-html-button"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                setIsOpen(true);
              }}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="mr-2 h-4 w-4"
              >
                <polyline points="16 18 22 12 16 6"></polyline>
                <polyline points="8 6 2 12 8 18"></polyline>
              </svg>
            </button>
          </div>
        );
      case 'iframe':
        return (
          <div className="artifact-preview bg-background my-2 rounded-md border p-4">
            <div className="mb-2 flex items-center justify-between">
              <h3 className="font-medium">
                {artifact.title || 'External Content'}
              </h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => window.open(artifact.content, '_blank')}
              >
                <ExternalLink className="mr-2 h-4 w-4" />
                Open
              </Button>
            </div>
            <iframe
              src={artifact.content}
              className="h-[300px] w-full rounded border"
              title={artifact.title || 'Embedded content'}
              sandbox="allow-scripts allow-same-origin"
            />
          </div>
        );
      default:
        return (
          <div className="text-muted-foreground text-sm">
            Unknown artifact type: {artifact.type}
          </div>
        );
    }
  };

  return (
    <>
      {renderArtifact()}

      {/* Backdrop overlay */}
      {artifact.type === 'html' && (
        <>
          <div
            className={cn(
              'fixed inset-0 z-40 bg-black/50 transition-opacity duration-300',
              isOpen ? 'opacity-100' : 'pointer-events-none opacity-0'
            )}
            onClick={() => setIsOpen(false)}
            aria-hidden="true"
          />

          {/* Persistent right-side panel for HTML content */}
          <div
            className={cn(
              'bg-background fixed inset-y-0 right-0 z-50 flex w-1/2 min-w-[600px] transform flex-col shadow-lg transition-transform duration-300 ease-in-out',
              isOpen ? 'translate-x-0' : 'translate-x-full'
            )}
            aria-labelledby="panel-title"
            aria-describedby="panel-description"
          >
            <div className="flex items-center justify-between border-b p-4">
              <h2 id="panel-title" className="font-semibold">
                {artifact.title || 'HTML Content'}
              </h2>
              <button
                type="button"
                onClick={() => setIsOpen(false)}
                className="bg-background/80 hover:bg-accent flex h-8 w-8 items-center justify-center rounded-full backdrop-blur"
                aria-label="Close"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M18 6 6 18"></path>
                  <path d="m6 6 12 12"></path>
                </svg>
              </button>
            </div>
            <VisuallyHidden>
              <p id="panel-description">Rendered HTML content from the chat</p>
            </VisuallyHidden>
            <div className="flex-1 overflow-hidden bg-white dark:bg-gray-900">
              <iframe
                key={`iframe-${artifact.content.substring(0, 20) || ''}`}
                ref={iframeRef}
                className="h-full w-full border-0"
                sandbox="allow-scripts allow-same-origin allow-popups allow-forms"
                title={artifact.title || 'HTML Content'}
                onLoad={() => console.log('ArtifactRenderer - Iframe loaded')}
              />
            </div>
          </div>
        </>
      )}
    </>
  );
}
