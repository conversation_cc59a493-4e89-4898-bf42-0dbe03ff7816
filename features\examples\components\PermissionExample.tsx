'use client';

import { usePermissions } from '@/hooks/usePermissions';
import {
  PermissionedElement,
  AdminElement,
  PermissionedElementAny,
  PermissionedElementAll,
} from '@/features/auth/components/PermissionedElement';
import { Button } from '@/components/ui/button';

export function PermissionExample() {
  const { can, isAdmin, user } = usePermissions();

  return (
    <div className="space-y-4 p-4">
      <h2 className="text-xl font-bold">Permission Examples</h2>

      {/* Simple permission check */}
      <div>
        <h3 className="font-semibold">Direct Permission Check:</h3>
        {can('admin', 'read') ? (
          <p className="text-green-600">You have admin read permission</p>
        ) : (
          <p className="text-red-600">You don't have admin read permission</p>
        )}
      </div>

      {/* Using PermissionedElement */}
      <div>
        <h3 className="font-semibold">Using PermissionedElement:</h3>
        <PermissionedElement
          resource="admin"
          operation="write"
          fallback={
            <p className="text-red-600">
              You don't have admin write permission
            </p>
          }
        >
          <p className="text-green-600">You have admin write permission</p>
          <Button>Admin Action</Button>
        </PermissionedElement>
      </div>

      {/* Using AdminElement */}
      <div>
        <h3 className="font-semibold">Using AdminElement:</h3>
        <AdminElement
          fallback={<p className="text-red-600">You are not an admin</p>}
        >
          <p className="text-green-600">You are an admin</p>
          <Button>Admin Dashboard</Button>
        </AdminElement>
      </div>

      {/* Using PermissionedElementAny */}
      <div>
        <h3 className="font-semibold">Using PermissionedElementAny:</h3>
        <PermissionedElementAny
          permissions={[
            { resource: 'admin', operation: 'read' },
            { resource: 'group', operation: 'write' },
          ]}
          fallback={
            <p className="text-red-600">
              You don't have any of the required permissions
            </p>
          }
        >
          <p className="text-green-600">
            You have at least one of the required permissions
          </p>
        </PermissionedElementAny>
      </div>

      {/* Using PermissionedElementAll */}
      <div>
        <h3 className="font-semibold">Using PermissionedElementAll:</h3>
        <PermissionedElementAll
          permissions={[
            { resource: 'admin', operation: 'read' },
            { resource: 'group', operation: 'read' },
          ]}
          fallback={
            <p className="text-red-600">
              You don't have all required permissions
            </p>
          }
        >
          <p className="text-green-600">You have all required permissions</p>
        </PermissionedElementAll>
      </div>

      {/* Display user groups and their permissions */}
      <div>
        <h3 className="font-semibold">Your Groups and Permissions:</h3>
        <pre className="overflow-auto rounded bg-gray-100 p-2 text-xs">
          {JSON.stringify(
            user?.groups.map((group) => ({
              id: group.id,
              name: group.name,
              permissions: group.meta?.perms || [],
            })) || [],
            null,
            2
          )}
        </pre>
      </div>
    </div>
  );
}
