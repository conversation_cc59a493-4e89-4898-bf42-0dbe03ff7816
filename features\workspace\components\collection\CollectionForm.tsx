'use client';

import React, { useState, useMemo } from 'react';
import { useForm } from '@tanstack/react-form';
import { z } from 'zod';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { SheetFooter } from '@/components/ui/sheet'; // Only Footer needed
import { MultiSelect } from '@/components/ui/multi-select';
import { ICollectionData, ICollectionCreate } from '@/types/collection';
import { useCreateCollection } from '@/features/workspace/api/useCreateCollection';
import { useUpdateCollection } from '@/features/workspace/api/useUpdateCollection';
import { useDocumentDeleteById } from '@/features/workspace/api/useDocumentDeleteById';
import { useDeleteCollection } from '@/features/workspace/api/useDeleteCollection';
import { useDocumentDownload } from '@/features/workspace/api/useDocumentDownload';
import { usePermissions } from '@/hooks/usePermissions';
import { getCollectionTemplate } from './helpers/getCollectionTemplate';
import CollectionFileUpload from './CollectionFileUpload';
import { Card } from '@/components/ui/card';
import {
  Check,
  FileText,
  Loader2,
  Trash2,
  Info,
  Tag,
  Download,
} from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';

interface CollectionFormProps {
  onOpenChange: (open: boolean) => void;
  collection?: ICollectionData | null; // Optional data for editing
  groupOptions: { value: string; label: string }[];
}

// Zod schema for validation
const collectionFormSchema = z.object({
  name: z.string().min(3, 'Name must be at least 3 characters'),
  description: z.string().optional(),
  groupIds: z.array(z.string()),
});

// Format JSON for better display
const formatJSON = (json: any): string => {
  try {
    return JSON.stringify(json, null, 2);
  } catch (error) {
    return JSON.stringify(json) || '{}';
  }
};

export default function CollectionForm({
  onOpenChange,
  collection,
  groupOptions,
}: CollectionFormProps) {
  const isEditMode = !!collection?.id;
  const [documentToDelete, setDocumentToDelete] = useState<string | null>(null);
  const [deletingDocumentId, setDeletingDocumentId] = useState<string | null>(
    null
  );
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const { downloadDocument, isDownloading } = useDocumentDownload();

  // Get user permissions
  const { user, isAdmin } = usePermissions();

  // Check if the current user is the owner or an admin
  const canEdit =
    !isEditMode ||
    isAdmin() ||
    (collection?.owner_id && user?.id === collection.owner_id);

  // Find the user's personal group ID
  const userPersonalGroupId = useMemo(() => {
    if (!user || !user.groups || user.groups.length === 0) return null;

    // Find the personal group for the current user
    const personalGroup = user.groups.find((group) => {
      // Check if the group has meta.personal = true
      if (typeof group.meta === 'string') {
        try {
          const metaObj = JSON.parse(group.meta);
          return metaObj.personal === true;
        } catch (e) {
          return false;
        }
      } else if (group.meta && typeof group.meta === 'object') {
        return group.meta.personal === true;
      }
      return false;
    });

    return personalGroup?.id || null;
  }, [user]);

  // --- Mutation Hooks ---
  const handleSuccess = () => {
    onOpenChange(false); // Close sheet on success
  };

  const { mutate: createCollectionMutate, isPending: isCreating } =
    useCreateCollection({ onSuccessCallback: handleSuccess });
  const { mutate: updateCollectionMutate, isPending: isUpdating } =
    useUpdateCollection({ onSuccessCallback: handleSuccess });
  const { mutate: deleteDocumentMutate } = useDocumentDeleteById(
    collection?.id || ''
  );
  const { mutate: deleteCollectionMutate, isPending: isDeleting } =
    useDeleteCollection({ onSuccessCallback: handleSuccess });

  const isLoadingMutation = isCreating || isUpdating || isDeleting;

  // Handle collection deletion
  const handleDeleteCollection = () => {
    if (collection?.id) {
      deleteCollectionMutate(collection.id);
    }
  };

  // Handle document deletion
  const handleDeleteDocument = () => {
    if (documentToDelete) {
      // Set the deleting document ID to show loading state
      setDeletingDocumentId(documentToDelete);

      deleteDocumentMutate(
        { documentId: documentToDelete },
        {
          onSuccess: () => {
            // Clear both states on success
            setDeletingDocumentId(null);
            setDocumentToDelete(null);
          },
          onError: () => {
            // Clear deleting state on error, but keep the dialog open
            setDeletingDocumentId(null);
          },
        }
      );

      // Close the dialog immediately after confirming
      setDocumentToDelete(null);
    }
  };

  // Get default group IDs for new collection
  const getDefaultGroupIds = () => {
    // If editing an existing collection, use its groups
    if (collection?.groups) {
      return collection.groups.map((group) => group.id);
    }

    // For new collection, include user's personal group if available
    if (!isEditMode && userPersonalGroupId) {
      return [userPersonalGroupId];
    }

    return [];
  };

  // --- Form Setup ---
  const form = useForm({
    defaultValues: {
      ...getCollectionTemplate(collection ?? undefined),
      // Override groupIds with our custom logic
      groupIds: getDefaultGroupIds(),
    },
    onSubmit: async ({ value }) => {
      // Prepare payload - directly matches ICollectionCreate
      const payload: ICollectionCreate = {
        groupIds: value.groupIds.length > 0 ? value.groupIds : [],
        name: value.name,
        description: value.description ?? '', // Ensure description is a string
      };

      if (isEditMode && collection?.id) {
        updateCollectionMutate({ collectionId: collection.id, payload });
      } else {
        createCollectionMutate(payload);
      }
    },
  });

  // --- UI Variables ---
  const submitButtonText = isEditMode
    ? isLoadingMutation
      ? 'Updating...'
      : 'Update'
    : isLoadingMutation
      ? 'Saving...'
      : 'Create'; // Changed from 'Submit' for clarity

  return (
    <AlertDialog>
      <div className="flex flex-col gap-4">
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            if (canEdit) {
              form.handleSubmit();
            }
          }}
        >
          <div className="grid gap-6 p-4">
            <form.Field
              name="name"
              validators={{ onChange: collectionFormSchema.shape.name }}
            >
              {(field) => (
                <div className="grid gap-2">
                  <Label htmlFor={field.name}>
                    Name <span className="text-destructive">*</span>
                  </Label>
                  <Input
                    id={field.name}
                    name={field.name}
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="Collection name"
                    disabled={isEditMode && !canEdit}
                  />
                  {field.state.meta.isTouched &&
                  field.state.meta.errors.length > 0 ? (
                    <p className="text-destructive text-xs">
                      {field.state.meta.errors[0]?.message}
                    </p>
                  ) : null}
                </div>
              )}
            </form.Field>

            {/* Groups Field */}
            <form.Field name="groupIds">
              {(field) => {
                const currentValue = field.state.value ?? [];
                return (
                  <div className="grid gap-2">
                    <Label>Groups</Label>
                    <MultiSelect
                      options={groupOptions}
                      onValueChange={field.handleChange}
                      defaultValue={currentValue}
                      placeholder={'Select groups'}
                      animation={0}
                      className="w-full"
                      onBlur={field.handleBlur}
                      disabled={isEditMode && !canEdit}
                    />
                  </div>
                );
              }}
            </form.Field>

            {/* Description Field */}
            <form.Field name="description">
              {(field) => (
                <div className="grid gap-2">
                  <Label htmlFor={field.name}>Description</Label>
                  <Textarea
                    id={field.name}
                    name={field.name}
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="Provide an optional description"
                    className="min-h-[100px] break-words"
                    outline
                    disabled={isEditMode && !canEdit}
                  />
                  {field.state.meta.isTouched &&
                  field.state.meta.errors.length > 0 ? (
                    <p className="text-destructive text-xs">
                      {field.state.meta.errors.join(', ')}
                    </p>
                  ) : null}
                </div>
              )}
            </form.Field>
          </div>

          {/* Footer with Buttons */}
          <SheetFooter className="flex flex-row items-center justify-between gap-2 p-4">
            <div className="flex items-center gap-2">
              {isEditMode && canEdit && (
                <AlertDialog
                  open={isDeleteDialogOpen}
                  onOpenChange={setIsDeleteDialogOpen}
                >
                  <AlertDialogTrigger asChild>
                    <Button
                      variant="destructive"
                      type="button"
                      disabled={isDeleting}
                    >
                      {isDeleting ? (
                        'Deleting...'
                      ) : (
                        <>
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </>
                      )}
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Delete Collection</AlertDialogTitle>
                      <AlertDialogDescription>
                        This action cannot be undone. This will permanently
                        delete the collection "{collection?.name}" and all its
                        documents.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={handleDeleteCollection}
                        className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                      >
                        Delete
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              )}
              {isEditMode && !canEdit && (
                <div className="text-foreground/50 text-xs">
                  Readonly (Only Owner or Admin can edit)
                </div>
              )}
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                type="button"
                onClick={() => onOpenChange(false)}
              >
                Cancel
              </Button>
              <form.Subscribe selector={(state) => [state.canSubmit]}>
                {([canSubmit]) => (
                  <Button
                    type="submit"
                    disabled={
                      !canSubmit ||
                      isLoadingMutation ||
                      (isEditMode && !canEdit)
                    }
                  >
                    {submitButtonText}
                  </Button>
                )}
              </form.Subscribe>
            </div>
          </SheetFooter>
        </form>

        {isEditMode && collection?.documents && (
          <div className="grid gap-8 p-4">
            <div className="grid gap-2">
              <div className="flex items-center justify-between">
                <Label>Files ({collection.documents.length})</Label>
                {collection.documents.some(
                  (doc) => doc.metadata.ingestion.status === 'pending'
                ) && (
                  <div className="flex items-center gap-1 text-xs text-blue-600">
                    <Loader2 className="h-3 w-3 animate-spin" />
                    <span className="animate-pulse">
                      Processing documents...
                    </span>
                  </div>
                )}
              </div>

              {collection.documents.map((document) => (
                <Card
                  className="hover:bg-accent gap-2 overflow-hidden rounded-md p-2 shadow-none"
                  key={document.id}
                >
                  <div className="flex w-full min-w-0 items-center justify-between gap-2">
                    <div className="flex min-w-0 flex-1 items-center gap-2 overflow-hidden">
                      <div className="bg-muted text-muted-foreground flex h-8 w-8 items-center justify-center rounded-md">
                        <FileText size={16} />
                      </div>
                      <div className="flex flex-col gap-2">
                        <div
                          className="max-w-[200px] truncate text-sm font-medium sm:max-w-[400px] md:max-w-[700px]"
                          key={document.id}
                          title={document.metadata.name}
                        >
                          {document.metadata.name}
                        </div>

                        {/* Display tags if they exist */}
                        {document.metadata.tags &&
                          document.metadata.tags.length > 0 && (
                            <div className="flex flex-col gap-1">
                              {document.metadata.tags.map((tag, index) => (
                                <Badge
                                  key={index}
                                  variant="outline"
                                  className="flex items-center gap-1 bg-slate-50/10 text-xs font-thin"
                                >
                                  {tag}
                                </Badge>
                              ))}
                            </div>
                          )}
                      </div>
                    </div>
                    <div className="flex flex-shrink-0 items-center gap-2">
                      {document.metadata.ingestion.status === 'pending' && (
                        <Badge
                          className="rounded-full border-blue-600 text-blue-600"
                          variant="outline"
                        >
                          <Loader2 className="mr-1 h-3 w-3 animate-spin" />
                          <span className="animate-pulse">Processing</span>
                        </Badge>
                      )}
                      {document.metadata.ingestion.status === 'failed' && (
                        <Badge
                          className="rounded-full border-red-600 text-red-600"
                          variant="outline"
                        >
                          <span>Failed</span>
                        </Badge>
                      )}
                      {document.metadata.ingestion.parsed &&
                        document.metadata.ingestion.status !== 'pending' &&
                        document.metadata.ingestion.status !== 'failed' && (
                          <Badge
                            className="rounded-full border-green-600 text-green-600"
                            variant="outline"
                          >
                            <Check />
                            Ready
                          </Badge>
                        )}

                      {/* Download button */}
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-7 w-7 rounded-full hover:bg-green-100 hover:text-green-600"
                        aria-label="Download document"
                        onClick={() =>
                          downloadDocument(
                            collection.id,
                            document.id,
                            document.metadata.name
                          )
                        }
                        disabled={isDownloading === document.id}
                      >
                        {isDownloading === document.id ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <Download size={16} />
                        )}
                      </Button>

                      {/* Metadata info button */}
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-7 w-7 rounded-full hover:bg-blue-100 hover:text-blue-600"
                            aria-label="View metadata"
                          >
                            <Info size={16} />
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent
                          className="w-[400px] p-0"
                          align="end"
                          side="left"
                        >
                          <div className="flex items-center justify-between border-b px-4 py-2">
                            <h4 className="font-medium">Document Metadata</h4>
                          </div>
                          <ScrollArea className="h-[300px]">
                            <div className="p-4">
                              <div className="bg-accent rounded-md p-4">
                                <pre className="text-foreground overflow-x-auto text-xs break-words whitespace-pre-wrap">
                                  {formatJSON(document.metadata)}
                                </pre>
                              </div>
                            </div>
                          </ScrollArea>
                        </PopoverContent>
                      </Popover>

                      {/* Delete button or loading spinner */}
                      {deletingDocumentId === document.id ? (
                        <div className="flex h-7 w-7 items-center justify-center">
                          <Loader2 className="h-4 w-4 animate-spin text-red-600" />
                        </div>
                      ) : (
                        <AlertDialogTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-7 w-7 rounded-full hover:bg-red-100 hover:text-red-600"
                            onClick={() => setDocumentToDelete(document.id)}
                            aria-label="Delete document"
                            disabled={!canEdit}
                          >
                            <Trash2 size={16} />
                          </Button>
                        </AlertDialogTrigger>
                      )}
                    </div>
                  </div>
                </Card>
              ))}
            </div>

            {/* Delete confirmation dialog */}
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Delete Document</AlertDialogTitle>
                <AlertDialogDescription>
                  Are you sure you want to delete this document? This action
                  cannot be undone.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel onClick={() => setDocumentToDelete(null)}>
                  Cancel
                </AlertDialogCancel>
                <AlertDialogAction
                  onClick={handleDeleteDocument}
                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                >
                  Delete
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>

            <Card className="gap-2 rounded-md p-4 shadow-none">
              <CollectionFileUpload
                collectionId={collection.id}
                disabled={!canEdit}
              />
            </Card>
          </div>
        )}
      </div>
    </AlertDialog>
  );
}
