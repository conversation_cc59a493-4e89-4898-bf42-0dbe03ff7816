import { Chart, ChartConfiguration } from 'chart.js';
import { ChartElementWithConfig, ChartRenderOptions } from './types';
import { applyDarkTheme } from './applyDarkTheme';

/**
 * Parses chart configuration from a chart element
 *
 * @param chartElement - The DOM element containing chart configuration
 * @returns The parsed Chart.js configuration or null if parsing fails
 */
function parseChartConfig(
  chartElement: ChartElementWithConfig
): ChartConfiguration | null {
  try {
    let configStr = chartElement.getAttribute('data-config');

    if (!configStr) {
      const content = chartElement.innerHTML.trim();

      // Try to extract J<PERSON><PERSON> from markdown code blocks first
      const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch && jsonMatch[1]) {
        configStr = jsonMatch[1].trim();
        chartElement.innerHTML = '';
      } else {
        // If no markdown code block, try to parse the content directly as <PERSON><PERSON><PERSON>
        try {
          // Verify if the content itself is valid JSON
          JSON.parse(content);
          configStr = content;
          chartElement.innerHTML = '';
        } catch (jsonError) {
          // Not valid JSON, continue with other attempts
        }
      }
    }

    if (!configStr) {
      throw new Error('No chart configuration found');
    }

    return JSON.parse(decodeURIComponent(configStr)) as ChartConfiguration;
  } catch (error) {
    console.error('Error parsing chart configuration:', error);
    return null;
  }
}

/**
 * Renders charts within a container
 *
 * @param options - Chart rendering options
 */
export function renderCharts(options: ChartRenderOptions): () => void {
  const { container, theme, instanceRef, isStreaming } = options;

  if (isStreaming || !container) {
    return () => {};
  }

  const cleanupCharts = () => {
    instanceRef.current.forEach((chart) => {
      chart.destroy();
    });
    instanceRef.current.clear();
  };

  const chartElements = container.querySelectorAll('.chart-js.chart-hidden');

  if (chartElements.length === 0) {
    return cleanupCharts;
  }

  chartElements.forEach((chartElement, index) => {
    const typedElement = chartElement as ChartElementWithConfig;
    const chartId = `chart-${Date.now()}-${index}`;

    typedElement.id = chartId;

    const config = parseChartConfig(typedElement);

    if (!config) {
      return;
    }

    const canvas = document.createElement('canvas');
    canvas.id = `canvas-${chartId}`;
    canvas.style.maxHeight = '400px';
    canvas.style.width = '100%';

    let finalConfig = config;
    if (theme === 'dark') {
      canvas.style.color = 'white';
      canvas.style.boxShadow = '0 0 10px 0 rgba(0, 0, 0, 0.1)';
      canvas.style.borderRadius = '8px';

      finalConfig = applyDarkTheme(config, {
        gridColor: 'rgba(255,255,255,0.1)',
        fontColor: 'white',
      });
    }

    typedElement.appendChild(canvas);

    setTimeout(() => {
      try {
        const ctx = canvas.getContext('2d');
        if (!ctx) {
          throw new Error('Could not get canvas context');
        }

        const chartInstance = new Chart(ctx, finalConfig);
        instanceRef.current.set(chartId, chartInstance);
        typedElement.classList.remove('chart-hidden');
      } catch (renderError) {
        console.error('Error rendering chart:', renderError);
      }
    }, 100);
  });

  return cleanupCharts;
}
