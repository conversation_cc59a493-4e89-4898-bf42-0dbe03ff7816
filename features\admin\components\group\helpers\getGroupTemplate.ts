import { IGroupCreate, IGroupData } from '@/types/group';

// Generates the default structure for the group form (IGroupCreate)
// Optionally merges data from an existing group for editing.
export const getGroupTemplate = (
  group: Partial<IGroupData> = {}
): IGroupCreate => {
  // Define the default structure for the form
  const defaults: IGroupCreate = {
    name: '',
    description: '',
    userIds: [],
  };

  // Extract userIds from either userIds array or users array
  let userIds = defaults.userIds;

  if (group?.userIds && group.userIds.length > 0) {
    // If userIds is already available, use it
    userIds = group.userIds;
  } else if (group?.users && group.users.length > 0) {
    // If only users array is available, extract the IDs
    userIds = group.users.map((user) => user.id);
  }

  // Merge defaults with provided group data, prioritizing existing values
  return {
    ...defaults,
    name: group?.name ?? defaults.name,
    description: group?.description ?? defaults.description,
    userIds: userIds,
  };
};
