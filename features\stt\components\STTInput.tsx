import React, { useState, useRef } from 'react';
import { STTService } from '@/services/stt.service';
import styles from './STTInput.module.css';
import { getSpeechToken } from '@/features/stt/api/useGetSpeechToken';
import { Button } from '@/components/ui/button';
import { Loader2, Mic, MicOff } from 'lucide-react';
import { cn } from '@/lib/utils';

interface STTInputProps {
  textAreaRef: React.RefObject<HTMLTextAreaElement>;
  onChange?: (text: string) => void;
  size?: string;
}

const STTInput: React.FC<STTInputProps> = ({
  textAreaRef,
  onChange,
  size = 'md',
}) => {
  const [isMicActive, setIsMicActive] = useState<boolean>(false);
  const [isMicLoading, setIsMicLoading] = useState<boolean>(false);
  const sttCurrentTextPosRef = useRef<{
    start: number | undefined;
    end: number | undefined;
  }>({
    start: undefined,
    end: undefined,
  });
  const sttServiceRef = useRef<STTService | null>(null);
  const lastRecognizingRef = useRef<number>(0);

  const onSTTText = (text: string, isFinal: boolean) => {
    lastRecognizingRef.current = Date.now();

    if (!textAreaRef.current) return;

    if (sttCurrentTextPosRef.current.start === undefined) {
      sttCurrentTextPosRef.current = {
        start: textAreaRef.current.selectionStart,
        end: textAreaRef.current.selectionStart,
      };
    }

    const textArea = textAreaRef.current;
    const originalText = textArea.value;

    const beforeText = originalText.slice(
      0,
      sttCurrentTextPosRef.current.start
    );
    const afterText = originalText.slice(sttCurrentTextPosRef.current.end);

    const newText = beforeText + text + afterText;
    textArea.value = newText;

    // Dispatch input event to notify React of the change
    const inputEvent = new Event('input', {
      bubbles: true,
      cancelable: true,
    });
    textArea.dispatchEvent(inputEvent);

    if (onChange) {
      onChange(newText);
    }

    sttCurrentTextPosRef.current.end =
      (sttCurrentTextPosRef.current.start as number) + text.length;
    textArea.setSelectionRange(
      sttCurrentTextPosRef.current.end,
      sttCurrentTextPosRef.current.end
    );

    if (isFinal) {
      sttCurrentTextPosRef.current = {
        start: undefined,
        end: undefined,
      };
    }
  };

  const toggleMic = async () => {
    console.log('Toggle mic');
    if (isMicActive) {
      console.log('Stopping STT');
      sttServiceRef.current?.dispose();
      await sttServiceRef.current?.stop();
      setIsMicActive(false);
      setIsMicLoading(false);
    } else {
      console.log('Starting STT');
      setIsMicLoading(true);
      const { token, region } = await getSpeechToken();
      if (!token || !region) {
        setIsMicLoading(false);
        return;
      }
      sttServiceRef.current = new STTService(
        token,
        region,
        (s, e) => {
          onSTTText(e.result.text, false);
        },
        (s, e) => {
          onSTTText(e.result.text, true);
        }
      );
      await sttServiceRef.current?.start();
      setIsMicActive(true);
      setIsMicLoading(false);
    }
  };

  return (
    <div className="relative flex">
      <div className={cn(styles.pulse, { [styles.pulse1]: isMicActive })}></div>
      <div className={cn(styles.pulse, { [styles.pulse2]: isMicActive })}></div>
      <Button
        variant={isMicActive ? 'ghost' : 'outline'}
        size="icon"
        disabled={isMicLoading}
        onClick={toggleMic}
      >
        {isMicLoading ? (
          <Loader2 className="animate-spin" />
        ) : isMicActive ? (
          <Mic />
        ) : (
          <MicOff />
        )}
      </Button>
    </div>
  );
};

export default STTInput;
