'use client';

import React from 'react';
import ChatLayout from '@/features/chat/components/ChatLayout';
import { ProtectedRoute } from '@/contexts/GlobalContext';
import MainLayout from '@/components/MainLayout';

export default function Layout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ProtectedRoute>
      <MainLayout>
        <ChatLayout>{children}</ChatLayout>
      </MainLayout>
    </ProtectedRoute>
  );
}
