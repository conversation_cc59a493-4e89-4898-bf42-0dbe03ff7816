'use client';

import Link from 'next/link';
import { useSidebar } from '@/contexts/SidebarContext';
import { ReactNode } from 'react';

interface SidebarLinkProps {
  href: string;
  className?: string;
  title?: string;
  children: ReactNode;
}

export default function SidebarLink({
  href,
  className,
  title,
  children,
}: SidebarLinkProps) {
  const { store: sidebarStore } = useSidebar();

  const handleClick = () => {
    // Only close the sidebar on mobile
    if (sidebarStore.isMobile) {
      sidebarStore.closeSidebar();
    }
  };

  return (
    <Link href={href} className={className} title={title} onClick={handleClick}>
      {children}
    </Link>
  );
}
