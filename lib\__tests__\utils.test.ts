import { describe, it, expect } from 'vitest';
import { cn } from '../utils';

describe('cn utility', () => {
  it('should merge class names correctly', () => {
    expect(cn('class1', 'class2')).toBe('class1 class2');
  });

  it('should handle conditional classes', () => {
    const condition = true;
    expect(cn('class1', condition && 'class2')).toBe('class1 class2');
    expect(cn('class1', !condition && 'class2')).toBe('class1');
  });

  it('should handle objects', () => {
    expect(cn('class1', { class2: true, class3: false })).toBe('class1 class2');
  });

  it('should handle arrays', () => {
    expect(cn('class1', ['class2', 'class3'])).toBe('class1 class2 class3');
  });

  it('should handle nested arrays and objects', () => {
    expect(cn('class1', ['class2', { class3: true, class4: false }])).toBe(
      'class1 class2 class3'
    );
  });

  it('should handle empty inputs', () => {
    expect(cn()).toBe('');
    expect(cn('')).toBe('');
    expect(cn(null)).toBe('');
    expect(cn(undefined)).toBe('');
  });

  it('should handle tailwind class conflicts', () => {
    // Tailwind merge should resolve conflicts by using the last class
    expect(cn('p-4', 'p-6')).toBe('p-6');
    expect(cn('text-red-500', 'text-blue-500')).toBe('text-blue-500');
    // The order might vary based on the tailwind-merge implementation
    const result = cn('flex items-center', 'grid');
    expect(result.includes('grid')).toBe(true);
    expect(result.includes('items-center')).toBe(true);
  });

  it('should handle complex tailwind combinations', () => {
    const result = cn(
      'px-2 py-1 bg-blue-500',
      'hover:bg-blue-700',
      { 'text-white': true, 'font-bold': true, 'rounded-none': false },
      'rounded'
    );

    expect(result).toBe(
      'px-2 py-1 bg-blue-500 hover:bg-blue-700 text-white font-bold rounded'
    );
  });
});
