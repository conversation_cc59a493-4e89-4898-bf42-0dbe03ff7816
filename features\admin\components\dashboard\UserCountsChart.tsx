'use client';

import React, { useEffect, useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  LabelList,
  Cell,
} from 'recharts';
import { Skeleton } from '@/components/ui/skeleton';
import { IUserCount } from '@/types/dashboard';
import { IUserData } from '@/types/user';
import { useFetchUsers } from '@/features/admin/api/useFetchUsers';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

interface UserCountsChartProps {
  data: IUserCount[];
  isLoading?: boolean;
}

export default function UserCountsChart({
  data,
  isLoading = false,
}: UserCountsChartProps) {
  const { data: users } = useFetchUsers();
  const [chartData, setChartData] = useState<
    Array<IUserCount & { avatar?: string }>
  >([]);

  // Sort data by message in descending order and merge with user data
  useEffect(() => {
    if (data && users) {
      const sortedData = [...data]
        .sort((a, b) => b.message - a.message)
        .map((user) => {
          // Find the user in the users array to get the profile picture
          // Try to match by ID first, then by name if ID doesn't match
          const userDetails = users.find(
            (u) =>
              u.id === user.id ||
              (u.name &&
                user.name &&
                u.name.toLowerCase() === user.name.toLowerCase())
          );

          return {
            ...user,
            // Ensure we have the correct name from the user details if available
            name: userDetails?.name || user.name,
            avatar: userDetails?.profile_picture || undefined,
          };
        });
      // Log the data for debugging purposes
      console.log('UserCountsChart data:', {
        originalData: data,
        usersData: users,
        mergedData: sortedData,
      });

      setChartData(sortedData);
    }
  }, [data, users]);

  const chartConfig = {
    message: {
      label: 'Messages',
      color: 'var(--chart-3)',
    },
  };

  // Get user initials from name (first letter of first and last name)
  const getUserInitials = (name: string): string => {
    if (!name || typeof name !== 'string') return '';

    // Remove any special characters and split by spaces
    const cleanName = name.replace(/[^\w\s]/gi, '').trim();
    if (!cleanName) return '';

    const nameParts = cleanName.split(/\s+/).filter(Boolean);
    if (nameParts.length === 0) return '';

    if (nameParts.length === 1) {
      // If only one part, use the first character
      return nameParts[0].charAt(0).toUpperCase();
    }

    // Get first character of first and last parts
    const firstInitial = nameParts[0].charAt(0);
    const lastInitial = nameParts[nameParts.length - 1].charAt(0);

    // Make sure we have valid characters (only use A-Z)
    const validFirstInitial = /[A-Za-z]/.test(firstInitial) ? firstInitial : '';
    const validLastInitial = /[A-Za-z]/.test(lastInitial) ? lastInitial : '';

    // If we don't have valid initials, try to get at least one valid character from the name
    if (!validFirstInitial && !validLastInitial) {
      for (const part of nameParts) {
        for (const char of part) {
          if (/[A-Za-z]/.test(char)) {
            return char.toUpperCase();
          }
        }
      }
      return '';
    }

    // Return the valid initials (or just one if the other is invalid)
    return `${validFirstInitial}${validLastInitial}`.toUpperCase();
  };

  // Custom label component for the Y-axis
  const CustomizedAxisTick = (props: any) => {
    const { x, y, payload, data } = props;
    // Find the user by exact name match or by checking if the name contains the payload value
    // This ensures we can match users even if there's a slight difference in formatting
    const user = data.find(
      (item: any) =>
        item.name === payload.value ||
        (typeof payload.value === 'string' &&
          typeof item.name === 'string' &&
          item.name.includes(payload.value))
    );

    // Get the display name - either from the user object or from the payload
    const displayName = user?.name || payload.value || 'Unknown User';

    return (
      <g transform={`translate(${x},${y})`}>
        <foreignObject x="-180" y="-10" width="220" height="30">
          <div className="flex items-center gap-1">
            <Avatar className="h-6 w-6">
              {user?.avatar ? (
                <AvatarImage src={user.avatar} alt={displayName} />
              ) : (
                <AvatarFallback className="text-[7px]">
                  {getUserInitials(displayName)}
                </AvatarFallback>
              )}
            </Avatar>
            <span
              className="max-w-[180px] truncate text-[10px]"
              title={displayName}
            >
              {displayName}
            </span>
          </div>
        </foreignObject>
      </g>
    );
  };

  // Find the maximum count to set a reasonable X-axis domain
  const maxCount = React.useMemo(() => {
    if (!chartData.length) return 100;
    const max = Math.max(...chartData.map((item) => item.message));
    return max > 0 ? Math.ceil(max * 1.05) : 100; // Add 5% padding, minimum 100
  }, [chartData]);

  // Calculate dynamic height based on number of users
  const chartHeight = React.useMemo(() => {
    // Base height for chart components (header, margins, etc.)
    const baseHeight = 100;
    // Height per user row (reduced by half for more compact display)
    const rowHeight = 30;
    // Calculate total height based on number of users
    const calculatedHeight = baseHeight + chartData.length * rowHeight;

    // Set minimum height to ensure chart is visible even with few users
    const minHeight = 300;
    // Set maximum height to prevent excessive scrolling
    const maxHeight = 6000;

    // If we have more than 20 users, we need to increase the height more aggressively
    if (chartData.length > 20) {
      return Math.min(chartData.length * 30, maxHeight);
    }

    return Math.max(minHeight, Math.min(calculatedHeight, maxHeight));
  }, [chartData.length]);

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">User Activity</CardTitle>
        <CardDescription className="text-xs">
          Message count by user
        </CardDescription>
      </CardHeader>
      <CardContent className="px-2" style={{ height: `${chartHeight}px` }}>
        {isLoading ? (
          <div className="flex h-full w-full items-center justify-center">
            <Skeleton
              className="w-full"
              style={{ height: `${Math.min(chartHeight, 500)}px` }}
            />
          </div>
        ) : (
          <div className="h-full w-full">
            <ChartContainer config={chartConfig} className="h-full w-full">
              <BarChart
                data={chartData}
                layout="vertical"
                margin={{ top: 0, right: 40, bottom: 20, left: 20 }}
                barGap={1}
                barCategoryGap={4}
                height={chartHeight - 50} // Subtract padding/margins
              >
                <CartesianGrid
                  strokeDasharray="3 3"
                  horizontal={true}
                  vertical={false}
                  opacity={0.2}
                  stroke="var(--border)"
                />
                <XAxis
                  type="number"
                  tickLine={false}
                  axisLine={false}
                  tickMargin={10}
                  tick={{ fontSize: 9, fill: 'var(--muted-foreground)' }}
                  domain={[0, maxCount]}
                />
                <YAxis
                  type="category"
                  dataKey="name"
                  tickLine={false}
                  axisLine={false}
                  tickMargin={10}
                  tick={<CustomizedAxisTick data={chartData} />}
                  width={200}
                />
                <ChartTooltip
                  content={({ active, payload }) => {
                    if (active && payload && payload.length) {
                      const data = payload[0].payload;
                      return (
                        <ChartTooltipContent
                          active={active}
                          payload={payload}
                          formatter={(value) => value.toLocaleString()}
                          label={data.name}
                        />
                      );
                    }
                    return null;
                  }}
                />
                <Bar
                  dataKey="message"
                  radius={[0, 4, 4, 0]}
                  maxBarSize={12}
                  fill="var(--chart-3)"
                  opacity={0.8}
                >
                  <LabelList
                    dataKey="message"
                    position="right"
                    formatter={(value: number) => value.toLocaleString()}
                    style={{ fontSize: 9, fill: 'var(--muted-foreground)' }}
                  />
                </Bar>
              </BarChart>
            </ChartContainer>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
