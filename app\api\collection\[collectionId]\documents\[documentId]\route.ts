import { NextRequest, NextResponse } from 'next/server';
import axiosInstanceApi from '@/lib/axiosInstance.api';
import { API_CONFIG } from '@/config/api';

// DELETE handler for deleting a specific document from a collection
export async function DELETE(
  request: NextRequest,
  props: { params: Promise<{ collectionId: string; documentId: string }> }
) {
  const authorization = request.headers.get('Authorization');
  const { collectionId, documentId } = await props.params;

  if (!collectionId || !documentId) {
    return NextResponse.json(
      { message: 'Collection ID and Document ID are required' },
      { status: 400 }
    );
  }

  const targetPath = `${API_CONFIG.workspace.collection}/${collectionId}/documents/${documentId}`;

  try {
    console.log(
      `[API Route Proxy] Forwarding DELETE ${targetPath} with Auth: ${!!authorization}`
    );
    const apiResponse = await axiosInstanceApi.delete(targetPath, {
      headers: { ...(authorization && { Authorization: authorization }) },
    });
    return NextResponse.json(apiResponse.data, { status: apiResponse.status });
  } catch (error: any) {
    console.error(
      `[API Route Proxy Error] DELETE ${targetPath}:`,
      error.response?.data || error.message
    );
    const status = error.response?.status || 500;
    const message =
      error.response?.data?.message || 'Proxy error deleting document';
    return NextResponse.json({ message }, { status });
  }
}
