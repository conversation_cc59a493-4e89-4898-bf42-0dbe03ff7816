import { useMutation, useQueryClient, QueryKey } from '@tanstack/react-query';
import axiosInstanceUi from '@/lib/axiosInstance.ui';
import { API_CONFIG } from '@/config/api';
import { IGroupCreate } from '@/types/group';
import { toast } from 'sonner';

// Function to create a new group via POST request
const createGroup = async (payload: IGroupCreate): Promise<void> => {
  console.log('Creating group:', payload);
  // Assuming API returns the created group
  await axiosInstanceUi.post<void>(
    // Use the client-side proxy route
    API_CONFIG.admin.group,
    payload
  );
};

// Define the query key for the group list (consistent with useFetchGroups)
const groupListQueryKey: QueryKey = [API_CONFIG.admin.group];

export function useCreateGroup({
  onSuccessCallback,
}: {
  onSuccessCallback?: () => void;
} = {}) {
  const queryClient = useQueryClient();

  return useMutation<void, Error, IGroupCreate>({
    mutationFn: createGroup,
    onSuccess: () => {
      toast.success(`Group created successfully!`);

      // Invalidate the group list query to refresh relevant UI parts
      queryClient.invalidateQueries({ queryKey: groupListQueryKey });

      // Call the success callback if provided
      onSuccessCallback?.();
    },
    onError: (error) => {
      console.error('Error creating new group:', error);
      toast.error(`Error creating group: ${error.message}`);
    },
  });
}
