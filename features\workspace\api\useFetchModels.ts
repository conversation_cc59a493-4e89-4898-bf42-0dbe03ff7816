import { useQuery } from '@tanstack/react-query';
import axiosInstanceUi from '@/lib/axiosInstance.ui'; // Use the UI axios instance
import { API_CONFIG } from '@/config/api';
import { IModelData, IGroupedModelData, ModelTypeEnum } from '@/types/model';
import { UI_CONFIG } from '@/config/ui';

// Function to fetch and return raw model data (ungrouped)
const fetchRawModels = async (): Promise<IModelData[]> => {
  const response = await axiosInstanceUi.get<IModelData[]>(
    API_CONFIG.chat.model
  );
  // Sort the data based on type and name
  return response.data.sort((a, b) => {
    // Prioritize type === 'assistant'
    if (
      a.type === ModelTypeEnum.ASSISTANT &&
      b.type !== ModelTypeEnum.ASSISTANT
    ) {
      return -1; // a comes first
    }
    if (
      a.type !== ModelTypeEnum.ASSISTANT &&
      b.type === ModelTypeEnum.ASSISTANT
    ) {
      return 1; // b comes first
    }
    // If types are the same, sort by name alphabetically
    return a.name.localeCompare(b.name);
  });
};

// Function to fetch and group models by type
const fetchGroupedModels = async (): Promise<IGroupedModelData> => {
  const models = await fetchRawModels();

  // Group models by type
  const modelsByType = models.reduce(
    (acc, model) => {
      const type = model.type;
      if (!acc[type]) {
        acc[type] = [];
      }
      acc[type].push(model);
      return acc;
    },
    {} as Record<string, IModelData[]>
  );

  // Convert to array format with name and data properties
  const groupedData: IGroupedModelData = [];

  // Add assistant models first if they exist
  if (modelsByType[ModelTypeEnum.ASSISTANT]) {
    // Sort assistant models to prioritize "chuck" model
    const sortedAssistantModels = [
      ...modelsByType[ModelTypeEnum.ASSISTANT],
    ].sort((a, b) => {
      // Check if model name is "chuck" (case insensitive)
      const isAChuck = a.name.toLowerCase() === UI_CONFIG.KEYS.CHUCK.name;
      const isBChuck = b.name.toLowerCase() === UI_CONFIG.KEYS.CHUCK.name;

      if (isAChuck && !isBChuck) {
        return -1; // Chuck comes first
      }
      if (!isAChuck && isBChuck) {
        return 1; // Chuck comes first
      }
      // Keep original order for other models
      return a.name.localeCompare(b.name);
    });

    groupedData.push({
      name: 'Agents',
      type: ModelTypeEnum.ASSISTANT,
      data: sortedAssistantModels,
    });
  }

  // Add other model types
  Object.entries(modelsByType).forEach(([type, models]) => {
    if (type !== ModelTypeEnum.ASSISTANT) {
      groupedData.push({
        name:
          type === ModelTypeEnum.MODEL
            ? 'Models'
            : type.charAt(0).toUpperCase() + type.slice(1) + 's',
        type: type as ModelTypeEnum,
        data: models,
      });
    }
  });

  return groupedData;
};

// Hook to fetch raw models (for backward compatibility)
export function useFetchRawModels() {
  return useQuery<IModelData[], Error>({
    queryKey: [API_CONFIG.chat.model],
    queryFn: fetchRawModels,
  });
}

// Hook to fetch grouped models
export function useFetchGroupedModels() {
  return useQuery<IGroupedModelData, Error>({
    queryKey: [API_CONFIG.chat.model, 'grouped'],
    queryFn: fetchGroupedModels,
  });
}

// Default hook (for backward compatibility)
// export function useFetchModels() {
//   return useFetchRawModels();
// }
