module.exports = {
  semi: true, // Add semicolons at the end of statements
  singleQuote: true, // Use single quotes instead of double quotes
  jsxSingleQuote: false, // Use double quotes in JSX
  trailingComma: 'es5', // Add trailing commas where valid in ES5 (objects, arrays, etc.)
  printWidth: 80, // Specify the line length that the printer will wrap on
  tabWidth: 2, // Specify the number of spaces per indentation-level
  useTabs: false, // Indent lines with spaces instead of tabs
  plugins: ['prettier-plugin-tailwindcss'], // Use the Tailwind CSS plugin
};
