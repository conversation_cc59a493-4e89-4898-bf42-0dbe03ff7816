import { useQuery } from '@tanstack/react-query';
import axiosInstanceUi from '@/lib/axiosInstance.ui';
import { API_CONFIG } from '@/config/api';
import { IChat } from '@/types/chat';

/**
 * Fetches a shared chat by its token
 * @param token The shared chat token
 * @returns The chat data
 */
export const fetchSharedChatByToken = async (token: string): Promise<IChat> => {
  const response = await axiosInstanceUi.get<IChat>(
    `${API_CONFIG.chat.chat}/shared/${token}`
  );

  if (!response.data) {
    throw new Error('Shared chat not found or empty response');
  }

  return response.data;
};

/**
 * React Query hook to fetch a shared chat by its token
 * @param token The shared chat token
 * @param enabled Whether the query should be enabled
 * @returns The query result with chat data
 */
export function useFetchSharedChat(
  token: string | null | undefined,
  enabled = true
) {
  return useQuery<IChat, Error>({
    queryKey: ['sharedChat', token],
    queryFn: () => {
      if (!token) {
        return Promise.reject(new Error('Chat token is required'));
      }
      return fetchSharedChatByToken(token);
    },
    enabled: !!token && enabled,
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
  });
}
