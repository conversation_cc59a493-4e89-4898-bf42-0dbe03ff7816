import React from 'react';
import { createRoot, Root } from 'react-dom/client';
import { NivoRenderOptions } from './types';

const chartRoots = new Map<string, Root>();
const processingContainers = new Set<string>();
const renderedCharts = new Map<string, Set<string>>(); // messageId -> Set of chartIds

async function loadDynamicNivoChart() {
  try {
    const module = await import('./DynamicNivoChart');
    return module.default;
  } catch (error) {
    console.error('Failed to load DynamicNivoChart:', error);
    return null;
  }
}

/**
 * Processes <nivoChart> elements and renders them as React components
 */
function processNivoChartElements(
  container: HTMLElement,
  messageId?: string
): void {
  const nivoChartElements = [
    ...Array.from(container.querySelectorAll('nivochart')),
    ...Array.from(container.querySelectorAll('NIVOCHART')),
    ...Array.from(container.getElementsByTagName('nivoChart')),
  ];

  nivoChartElements.forEach((element, index) => {
    const htmlElement = element as HTMLElement;

    if (htmlElement.classList.contains('nivo-chart-processed')) {
      return;
    }

    htmlElement.style.display = 'none';
    htmlElement.style.visibility = 'hidden';

    const jsonContent = htmlElement.textContent || htmlElement.innerHTML || '';

    if (!jsonContent.trim()) {
      console.warn('Empty nivoChart element found');
      htmlElement.classList.add('nivo-chart-processed');
      return;
    }

    const messageHash =
      messageId || container.id || container.className || 'unknown';

    const chartId = `nivo-chart-${messageHash}-${index}`;

    const existingContainer = htmlElement.nextElementSibling;
    if (
      existingContainer &&
      existingContainer.classList.contains('nivo-chart-container')
    ) {
      const existingElement = existingContainer as HTMLElement;
      existingElement.dataset.chartJson = jsonContent;
      htmlElement.classList.add('nivo-chart-processed');
      return;
    }

    const chartContainer = document.createElement('div');
    chartContainer.id = chartId;
    chartContainer.className = 'nivo-chart-container nivo-chart-processed';
    chartContainer.style.width = '100%';
    chartContainer.style.height = '400px';
    chartContainer.style.minHeight = '300px';
    chartContainer.style.marginBottom = '16px';

    chartContainer.style.opacity = '0';
    chartContainer.style.transition = 'opacity 0.3s ease-in-out';

    chartContainer.dataset.chartJson = jsonContent;

    htmlElement.parentNode?.insertBefore(
      chartContainer,
      htmlElement.nextSibling
    );

    if (messageId) {
      if (!renderedCharts.has(messageId)) {
        renderedCharts.set(messageId, new Set());
      }
      renderedCharts.get(messageId)!.add(chartId);
    }

    htmlElement.classList.add('nivo-chart-processed');
  });
}

/**
 * Renders React components for processed chart containers
 */
async function renderChartComponents(
  container: HTMLElement,
  theme?: 'light' | 'dark',
  messageId?: string
): Promise<void> {
  const chartContainers = container.querySelectorAll(
    '.nivo-chart-container:not(.nivo-chart-rendered)'
  );

  if (chartContainers.length === 0) {
    return;
  }

  const DynamicNivoChart = await loadDynamicNivoChart();

  if (!DynamicNivoChart) {
    console.error('Failed to load DynamicNivoChart component');
    return;
  }

  const isDark =
    theme === 'dark' ||
    (!theme &&
      (document.documentElement.classList.contains('dark') ||
        document.body.classList.contains('dark')));

  chartContainers.forEach((chartContainer) => {
    const htmlElement = chartContainer as HTMLElement;
    const jsonContent = htmlElement.dataset.chartJson;

    if (!jsonContent) {
      console.warn('No JSON content found in chart container');
      return;
    }

    if (htmlElement.classList.contains('nivo-chart-rendered')) {
      const currentJson = htmlElement.dataset.chartJson;
      const existingRoot = chartRoots.get(htmlElement.id);

      if (existingRoot && currentJson === jsonContent) {
        return;
      }

      if (existingRoot) {
        try {
          existingRoot.unmount();
        } catch (error) {
          console.warn('Error unmounting existing chart:', error);
        }
        chartRoots.delete(htmlElement.id);
      }
      htmlElement.classList.remove('nivo-chart-rendered');
    }

    if (
      htmlElement.children.length > 0 &&
      !htmlElement.innerHTML.includes('Waiting for chart data') &&
      !htmlElement.classList.contains('nivo-chart-rendered')
    ) {
      htmlElement.classList.add('nivo-chart-rendered');
      return;
    }

    try {
      const containerId = htmlElement.id || `chart-${Date.now()}`;
      htmlElement.id = containerId;

      if (processingContainers.has(containerId)) {
        return;
      }

      processingContainers.add(containerId);

      let root = chartRoots.get(containerId);

      if (!root) {
        htmlElement.innerHTML = '';

        root = createRoot(htmlElement);
        chartRoots.set(containerId, root);
      }

      const handleError = (error: string) => {
        console.error('Chart rendering error:', error);
        const errorElement = React.createElement(
          'div',
          {
            style: {
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '16px',
              color: '#ef4444',
              backgroundColor: '#fef2f2',
              border: '1px solid #fecaca',
              borderRadius: '8px',
              fontSize: '14px',
            },
          },
          React.createElement('strong', { key: 'error-label' }, 'Error: '),
          error
        );

        root.render(errorElement);
      };

      const chartElement = React.createElement(DynamicNivoChart, {
        jsonString: jsonContent,
        width: htmlElement.offsetWidth || 800,
        height: 400,
        isDark: isDark,
        onError: handleError,
      });

      try {
        root.render(chartElement);

        setTimeout(() => {
          if (chartRoots.has(containerId)) {
            htmlElement.style.opacity = '1';
          }
        }, 100);
      } catch (error) {
        console.warn(
          'Error rendering chart (root may have been unmounted):',
          error
        );

        chartRoots.delete(containerId);
        processingContainers.delete(containerId);
        return;
      }

      htmlElement.classList.add('nivo-chart-rendered');
      processingContainers.delete(containerId);
    } catch (error) {
      console.error('Error creating React root for chart:', error);
      const containerId = htmlElement.id;
      if (containerId) {
        processingContainers.delete(containerId);
      }
      htmlElement.innerHTML = `
        <div style="
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 16px;
          color: #ef4444;
          background-color: #fef2f2;
          border: 1px solid #fecaca;
          border-radius: 8px;
        ">
          Failed to render chart: ${error instanceof Error ? error.message : 'Unknown error'}
        </div>
      `;
    }
  });
}

/**
 * Main function to render Nivo charts within a container
 */
export function renderNivoCharts(
  options: NivoRenderOptions & { messageId?: string }
): () => void {
  const { container, theme, isStreaming, messageId } = options;

  if (isStreaming) {
    const nivoChartElements = [
      ...Array.from(container.querySelectorAll('nivochart')),
      ...Array.from(container.querySelectorAll('NIVOCHART')),
      ...Array.from(container.getElementsByTagName('nivoChart')),
    ];

    nivoChartElements.forEach((element) => {
      const htmlElement = element as HTMLElement;
      if (!htmlElement.classList.contains('nivo-chart-processed')) {
        htmlElement.style.display = 'none';
        htmlElement.style.visibility = 'hidden';
      }
    });

    return () => {};
  }

  if (!container) {
    return () => {};
  }

  try {
    processNivoChartElements(container, messageId);

    renderChartComponents(container, theme, messageId).catch((error) => {
      console.error('Error in renderChartComponents:', error);
    });
  } catch (error) {
    console.error('Error in renderNivoCharts:', error);
  }

  return () => {
    if (messageId && renderedCharts.has(messageId)) {
      const chartIds = renderedCharts.get(messageId)!;
      chartIds.forEach((chartId) => {
        if (chartRoots.has(chartId)) {
          const root = chartRoots.get(chartId);
          chartRoots.delete(chartId);
          processingContainers.delete(chartId);

          setTimeout(() => {
            try {
              root?.unmount();
            } catch (error) {
              console.warn('Error unmounting React root:', error);
            }
          }, 0);
        }
      });
      renderedCharts.delete(messageId);
    }

    const chartContainers = container.querySelectorAll('.nivo-chart-rendered');
    chartContainers.forEach((chartContainer) => {
      const htmlElement = chartContainer as HTMLElement;
      try {
        const containerId = htmlElement.id;

        if (containerId && chartRoots.has(containerId)) {
          const root = chartRoots.get(containerId);
          chartRoots.delete(containerId);
          processingContainers.delete(containerId);

          setTimeout(() => {
            try {
              root?.unmount();
            } catch (error) {
              console.warn('Error unmounting React root:', error);
            }
          }, 0);
        }

        htmlElement.classList.remove('nivo-chart-rendered');
        htmlElement.classList.remove('nivo-chart-processed');
      } catch (error) {
        console.warn('Error cleaning up chart:', error);
      }
    });

    const hiddenElements = container.querySelectorAll(
      'nivochart[style*="display: none"], nivoChart[style*="display: none"], NIVOCHART[style*="display: none"]'
    );
    hiddenElements.forEach((element) => {
      const htmlElement = element as HTMLElement;
      try {
        htmlElement.style.display = '';
        htmlElement.style.visibility = '';
        htmlElement.classList.remove('nivo-chart-processed');
      } catch (error) {
        console.warn('Error cleaning up hidden element:', error);
      }
    });
  };
}
