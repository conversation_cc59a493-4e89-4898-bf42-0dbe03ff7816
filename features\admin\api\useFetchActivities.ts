import { useQuery } from '@tanstack/react-query';
import axiosInstanceUi from '@/lib/axiosInstance.ui';
import { API_CONFIG } from '@/config/api';
import { ActivityResponse } from '@/types/dashboard';

// Function to fetch paginated activity data
const fetchActivities = async (
  page: number = 1,
  limit: number = 10
): Promise<ActivityResponse> => {
  const response = await axiosInstanceUi.get(API_CONFIG.admin.activity, {
    params: {
      page,
      limit,
    },
  });

  if (!response.data) {
    throw new Error('No activity data returned');
  }

  // Ensure the response matches our expected format
  if (!response.data.items || !response.data.pagination) {
    console.error('Unexpected API response format:', response.data);
    throw new Error('Invalid activity data format');
  }

  return response.data;
};

export function useFetchActivities(page: number = 1, limit: number = 10) {
  return useQuery<ActivityResponse, Error>({
    queryKey: [API_CONFIG.admin.activity, page, limit],
    queryFn: () => fetchActivities(page, limit),
    placeholderData: (previousData) => previousData, // Keep previous data while fetching new data
  });
}
