export const API_CONFIG = {
  auth: {
    signIn: '/auth/sign-in',
    speechToken: '/auth/speech-token',
  },
  chat: {
    model: '/model',
    chat: '/api-service-proxy/chat',
    feedback: '/feedback',
    tool: '/tool',
  },
  workspace: {
    collection: '/collection',
    group: '/group',
    model: '/model',
    tool: '/tool',
  },
  admin: {
    user: '/user',
    userList: '/user/list',
    userMe: '/user/me',
    group: '/group',
    groupList: '/group/list',
    dashboard: '/dashboard',
    activity: '/activity',
  },
  ingestion: {
    upload: '/upload-documents',
  },
  support: {
    ticket: '/support',
  },
  recording: {
    recordings: '/api-service-proxy/recording',
    recording: '/api-service-proxy/recording',
    email: '/api-service-proxy/recording',
    stream: '/api-service-proxy/recording/stream',
    playback: '/api-service-proxy/recording/playback',
  },
};
