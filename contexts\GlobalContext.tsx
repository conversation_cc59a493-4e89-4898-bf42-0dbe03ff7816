'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import Cookies from 'js-cookie';
import { useRouter } from 'next/navigation';
import { IUserData } from '@/types/user';

interface IGlobalStore {
  token: string | undefined | null;
  user: IUserData | null;
  isLoadingUser: boolean;
}

interface IGlobalContext {
  store: IGlobalStore;
  setStore: React.Dispatch<React.SetStateAction<IGlobalStore>>;
}

const GlobalContext = createContext<IGlobalContext | null>(null);

export const GlobalContextProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const initialToken = undefined;

  // Initialize state with token, null user, and loading flag
  const [store, setStore] = useState<IGlobalStore>({
    token: initialToken,
    user: null,
    isLoadingUser: false,
  });

  // Load token from cookies on mount
  useEffect(() => {
    const loadToken = () => {
      const cookieToken = Cookies.get('token') || null;
      setStore((prevStore) => ({
        ...prevStore,
        token: cookieToken,
      }));
    };

    loadToken();
  }, []);

  return (
    <GlobalContext.Provider value={{ store, setStore }}>
      {children}
    </GlobalContext.Provider>
  );
};

export const useGlobal = () => {
  return useContext(GlobalContext) as IGlobalContext;
};

export const ProtectedRoute = ({ children }: { children: any }) => {
  const router = useRouter();
  const {
    store: { token },
  } = useGlobal();

  useEffect(() => {
    if (token === null) {
      router.push('/login');
    }
  }, [token]);

  if (token) {
    return children;
  }
};
