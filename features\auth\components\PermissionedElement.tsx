'use client';

import { usePermissions } from '@/hooks/usePermissions';
import { ResourceType, OperationType } from '@/lib/permissions/types';

interface PermissionedElementProps {
  children: React.ReactNode;
  resource: ResourceType;
  operation: OperationType;
  fallback?: React.ReactNode;
}

/**
 * Component to conditionally render UI elements based on user permissions
 * Renders fallback if the user doesn't have the required permission
 */
export function PermissionedElement({
  children,
  resource,
  operation,
  fallback = null,
}: PermissionedElementProps) {
  const { can } = usePermissions();
  const hasPermission = can(resource, operation);

  return hasPermission ? <>{children}</> : <>{fallback}</>;
}

/**
 * Component to conditionally render UI elements based on admin status
 * Renders fallback if the user is not an admin
 */
export function AdminElement({
  children,
  fallback = null,
}: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  const { isAdmin } = usePermissions();
  
  return isAdmin() ? <>{children}</> : <>{fallback}</>;
}

/**
 * Component to conditionally render UI elements based on multiple permissions (ANY)
 * Renders fallback if the user doesn't have ANY of the required permissions
 */
export function PermissionedElementAny({
  children,
  permissions,
  fallback = null,
}: {
  children: React.ReactNode;
  permissions: { resource: ResourceType; operation: OperationType }[];
  fallback?: React.ReactNode;
}) {
  const { canAny } = usePermissions();
  const hasPermission = canAny(permissions);

  return hasPermission ? <>{children}</> : <>{fallback}</>;
}

/**
 * Component to conditionally render UI elements based on multiple permissions (ALL)
 * Renders fallback if the user doesn't have ALL of the required permissions
 */
export function PermissionedElementAll({
  children,
  permissions,
  fallback = null,
}: {
  children: React.ReactNode;
  permissions: { resource: ResourceType; operation: OperationType }[];
  fallback?: React.ReactNode;
}) {
  const { canAll } = usePermissions();
  const hasPermission = canAll(permissions);

  return hasPermission ? <>{children}</> : <>{fallback}</>;
}
