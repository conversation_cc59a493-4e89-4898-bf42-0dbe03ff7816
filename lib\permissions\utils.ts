import { IUserData } from '@/types/user';
import {
  Permission,
  ResourceType,
  OperationType,
  GroupWithPermissions,
} from './types';

/**
 * Check if a user has a specific permission through any of their groups
 * @param user The user to check permissions for
 * @param resource The resource type to check
 * @param operation The operation type to check
 * @returns True if the user has the permission, false otherwise
 */
export function hasPermission(
  user: IUserData | null,
  resource: ResourceType,
  operation: OperationType
): boolean {
  // If no user or no groups, return false
  if (!user || !user.groups || user.groups.length === 0) {
    return false;
  }

  // Check if any of the user's groups has the permission
  return user.groups.some((group) => {
    // Check if the group has meta.perms
    if (!group.meta || !group.meta.perms) {
      return false;
    }

    // Check if any permission in the group matches the resource and operation
    return group.meta.perms.some(
      (perm) => perm.res === resource && perm.ops.includes(operation)
    );
  });
}

/**
 * Check if a user has any of the specified permissions through any of their groups
 * @param user The user to check permissions for
 * @param permissions Array of permission objects to check
 * @returns True if the user has any of the permissions, false otherwise
 */
export function hasAnyPermission(
  user: IUserData | null,
  permissions: { resource: ResourceType; operation: OperationType }[]
): boolean {
  return permissions.some(({ resource, operation }) =>
    hasPermission(user, resource, operation)
  );
}

/**
 * Check if a user has all of the specified permissions through any of their groups
 * @param user The user to check permissions for
 * @param permissions Array of permission objects to check
 * @returns True if the user has all of the permissions, false otherwise
 */
export function hasAllPermissions(
  user: IUserData | null,
  permissions: { resource: ResourceType; operation: OperationType }[]
): boolean {
  return permissions.every(({ resource, operation }) =>
    hasPermission(user, resource, operation)
  );
}

/**
 * Check if a user is an admin (has read permission on admin resource)
 * @param user The user to check
 * @returns True if the user is an admin, false otherwise
 */
export function isAdmin(user: IUserData | null): boolean {
  return hasPermission(user, 'admin', 'read');
}

/**
 * Get all resources a user has access to through any of their groups
 * @param user The user to check
 * @returns Array of resources the user has access to
 */
export function getUserResources(user: IUserData | null): ResourceType[] {
  if (!user || !user.groups || user.groups.length === 0) {
    return [];
  }

  // Collect all permissions from all groups
  const allPerms: Permission[] = [];
  user.groups.forEach((group) => {
    if (group.meta && group.meta.perms) {
      allPerms.push(...group.meta.perms);
    }
  });

  // Extract unique resources
  return [...new Set(allPerms.map((perm) => perm.res))];
}

/**
 * Get all operations a user can perform on a specific resource through any of their groups
 * @param user The user to check
 * @param resource The resource to check
 * @returns Array of operations the user can perform on the resource
 */
export function getUserOperations(
  user: IUserData | null,
  resource: ResourceType
): OperationType[] {
  if (!user || !user.groups || user.groups.length === 0) {
    return [];
  }

  // Collect all operations for the resource from all groups
  const operations: OperationType[] = [];

  user.groups.forEach((group) => {
    if (group.meta && group.meta.perms) {
      const resourcePerms = group.meta.perms.filter(
        (perm) => perm.res === resource
      );
      resourcePerms.forEach((perm) => {
        operations.push(...perm.ops);
      });
    }
  });

  // Return unique operations
  return [...new Set(operations)];
}
