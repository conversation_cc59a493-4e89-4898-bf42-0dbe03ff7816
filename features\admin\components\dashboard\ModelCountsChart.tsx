'use client';

import React from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  LabelList,
} from 'recharts';
import { Skeleton } from '@/components/ui/skeleton';
import { IModelCount } from '@/types/dashboard';

interface ModelCountsChartProps {
  data: IModelCount[];
  isLoading?: boolean;
}

export default function ModelCountsChart({
  data,
  isLoading = false,
}: ModelCountsChartProps) {
  // Sort data by count in descending order
  const sortedData = React.useMemo(() => {
    return [...data].sort((a, b) => b.count - a.count);
  }, [data]);

  const chartConfig = {
    count: {
      label: 'Usage Count',
      color: 'var(--chart-2)',
    },
  };

  // Find the maximum count to set a reasonable X-axis domain
  const maxCount = React.useMemo(() => {
    if (!sortedData.length) return 100;
    const max = Math.max(...sortedData.map((item) => item.count));
    return max > 0 ? Math.ceil(max * 1.05) : 100; // Add 5% padding, minimum 100
  }, [sortedData]);

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">Model Usage</CardTitle>
        <CardDescription className="text-xs">
          Distribution of chat sessions across different models
        </CardDescription>
      </CardHeader>
      <CardContent className="h-[700px] px-2">
        {isLoading ? (
          <div className="flex h-full w-full items-center justify-center">
            <Skeleton className="h-[300px] w-full" />
          </div>
        ) : (
          <div className="h-full w-full">
            <ChartContainer config={chartConfig} className="h-full w-full">
              <BarChart
                data={sortedData}
                layout="vertical"
                margin={{ top: 5, right: 30, bottom: 5, left: -50 }}
                barGap={2}
                barCategoryGap={8}
              >
                <CartesianGrid
                  strokeDasharray="3 3"
                  horizontal={true}
                  vertical={false}
                  opacity={0.2}
                  stroke="var(--border)"
                />
                <XAxis
                  type="number"
                  tickLine={false}
                  axisLine={false}
                  tickMargin={10}
                  tick={{ fontSize: 11, fill: 'var(--muted-foreground)' }}
                  domain={[0, maxCount]}
                />
                <YAxis
                  type="category"
                  dataKey="name"
                  tickLine={false}
                  axisLine={false}
                  tickMargin={10}
                  tick={{ fontSize: 11, fill: 'var(--muted-foreground)' }}
                  width={300}
                />
                <ChartTooltip
                  content={({ active, payload }) => {
                    if (active && payload && payload.length) {
                      const data = payload[0].payload;
                      return (
                        <ChartTooltipContent
                          active={active}
                          payload={payload}
                          formatter={(value) => value.toLocaleString()}
                          label={data.name}
                        />
                      );
                    }
                    return null;
                  }}
                />
                <Bar
                  dataKey="count"
                  radius={[0, 4, 4, 0]}
                  maxBarSize={20}
                  fill="var(--chart-2)"
                  opacity={0.8}
                >
                  <LabelList
                    dataKey="count"
                    position="right"
                    formatter={(value: number) => value.toLocaleString()}
                    style={{ fontSize: 11, fill: 'var(--muted-foreground)' }}
                  />
                </Bar>
              </BarChart>
            </ChartContainer>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
