import { useGlobal } from '@/contexts/GlobalContext';
import { 
  hasPermission, 
  hasAnyPermission, 
  hasAllPermissions, 
  isAdmin,
  getUserResources,
  getUserOperations
} from '@/lib/permissions/utils';
import { ResourceType, OperationType } from '@/lib/permissions/types';

/**
 * Hook to check user permissions
 * @returns Object with permission checking functions
 */
export function usePermissions() {
  const { store } = useGlobal();
  const { user } = store;

  return {
    /**
     * Check if the user has a specific permission
     */
    can: (resource: ResourceType, operation: OperationType) => 
      hasPermission(user, resource, operation),
    
    /**
     * Check if the user has any of the specified permissions
     */
    canAny: (permissions: { resource: ResourceType; operation: OperationType }[]) => 
      hasAnyPermission(user, permissions),
    
    /**
     * Check if the user has all of the specified permissions
     */
    canAll: (permissions: { resource: ResourceType; operation: OperationType }[]) => 
      hasAllPermissions(user, permissions),
    
    /**
     * Check if the user is an admin
     */
    isAdmin: () => isAdmin(user),
    
    /**
     * Get all resources the user has access to
     */
    getResources: () => getUserResources(user),
    
    /**
     * Get all operations the user can perform on a specific resource
     */
    getOperations: (resource: ResourceType) => getUserOperations(user, resource),
    
    /**
     * The current user
     */
    user
  };
}
