import { NextRequest, NextResponse } from 'next/server';
import axiosInstanceApi from '@/lib/axiosInstance.api';
import { API_CONFIG } from '@/config/api';

export async function GET(request: NextRequest) {
  try {
    const authorization = request.headers.get('Authorization');
    const targetPath = API_CONFIG.admin.activity;

    // Forward query parameters for pagination
    const { searchParams } = new URL(request.url);
    const page = searchParams.get('page');
    const limit = searchParams.get('limit');

    console.log(
      `[API Route Proxy] Forwarding GET ${targetPath} with Auth: ${!!authorization}, page: ${page}, limit: ${limit}`
    );

    const apiResponse = await axiosInstanceApi.get(targetPath, {
      headers: {
        ...(authorization && { Authorization: authorization }),
      },
      params: {
        ...(page && { page }),
        ...(limit && { limit }),
      },
    });

    console.log('API Response:', apiResponse.data);

    return NextResponse.json(apiResponse.data, { status: apiResponse.status });
  } catch (error: any) {
    console.error(
      `[API Route Proxy Error] GET dashboard/activity:`,
      error.response?.data || error.message
    );

    const status = error.response?.status || 500;
    const message =
      error.response?.data?.message || 'Error fetching activity data';
    return NextResponse.json({ message }, { status });
  }
}
