'use client';

import React, { useState, useRef, ReactNode, useEffect } from 'react';
import { IMessageAttachment, MessageBlockTypeEnum } from '@/types/chat';
import { v4 as uuidv4 } from 'uuid';

interface SimpleDropzoneProps {
  children?: ReactNode;
  onFilesAdded: (attachments: IMessageAttachment[]) => void;
  fullScreen?: boolean;
}

const ChatDropzone: React.FC<SimpleDropzoneProps> = ({
  children,
  onFilesAdded,
  fullScreen = false,
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const dragCounterRef = useRef(0);

  const processFiles = (files: File[]) => {
    console.log('Processing files:', files);
    if (files.length === 0) return;

    const newAttachments: IMessageAttachment[] = [];
    let processedFiles = 0;

    files.forEach((file) => {
      const attachment: IMessageAttachment = {
        name: file.name,
        type: file.type,
        size: file.size,
        file: file,
        id: uuidv4(),
      };
      if (attachment.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (event) => {
          const base64Content = event.target?.result;
          attachment.content = base64Content as string;
        };
        reader.readAsDataURL(file);
      }

      newAttachments.push(attachment);
      processedFiles++;

      if (processedFiles === files.length) {
        console.log('Sending attachments:', newAttachments);
        onFilesAdded(newAttachments);
      }
    });
  };

  // Event handlers
  const handleDragOver = (e: React.DragEvent<HTMLDivElement> | DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.dataTransfer) {
      e.dataTransfer.dropEffect = 'copy';
    }
  };

  const handleDragEnter = (e: React.DragEvent<HTMLDivElement> | DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    dragCounterRef.current += 1;
    if (dragCounterRef.current === 1) {
      setIsDragging(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement> | DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    dragCounterRef.current -= 1;
    if (dragCounterRef.current === 0) {
      setIsDragging(false);
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement> | DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    dragCounterRef.current = 0;
    setIsDragging(false);

    if (e.dataTransfer?.files && e.dataTransfer.files.length > 0) {
      console.log('Files dropped:', e.dataTransfer.files);
      processFiles(Array.from(e.dataTransfer.files));
    }
  };

  // For fullScreen mode, use document-level event listeners
  useEffect(() => {
    if (fullScreen) {
      // Convert React events to DOM events
      const documentDragOver = (e: DragEvent) => handleDragOver(e);
      const documentDragEnter = (e: DragEvent) => handleDragEnter(e);
      const documentDragLeave = (e: DragEvent) => handleDragLeave(e);
      const documentDrop = (e: DragEvent) => handleDrop(e);

      // Add the event listeners to document
      document.addEventListener('dragover', documentDragOver);
      document.addEventListener('dragenter', documentDragEnter);
      document.addEventListener('dragleave', documentDragLeave);
      document.addEventListener('drop', documentDrop);

      // Clean up
      return () => {
        document.removeEventListener('dragover', documentDragOver);
        document.removeEventListener('dragenter', documentDragEnter);
        document.removeEventListener('dragleave', documentDragLeave);
        document.removeEventListener('drop', documentDrop);
      };
    }
  }, [fullScreen]);

  // The drop overlay UI - same for both fullScreen and standard mode
  const dropOverlay = isDragging && (
    <div className="bg-background/50 fixed inset-0 z-[9999] flex items-center justify-center backdrop-blur-md transition-all duration-300">
      <div className="relative flex flex-col items-center justify-center rounded-xl bg-transparent p-12 transition-all duration-300">
        {/* Floating document icons */}
        <div className="animate-float-slow absolute top-[calc(50%-60px)] left-[calc(50%-120px)] rotate-[-8deg] opacity-80">
          <svg
            width="40"
            height="40"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="text-blue-400"
          >
            <path
              d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M14 2V8H20"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M16 13H8"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M16 17H8"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M10 9H9H8"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>
        <div className="animate-float absolute top-[calc(50%-80px)] right-[calc(50%-110px)] rotate-[5deg] opacity-80">
          <svg
            width="48"
            height="48"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="text-indigo-500"
          >
            <path
              d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M14 2V8H20"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M16 13H8"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M16 17H8"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M10 9H9H8"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>
        <div className="animate-float-slow absolute bottom-[calc(50%-90px)] left-[calc(50%-130px)] rotate-[-10deg] opacity-80">
          <svg
            width="44"
            height="44"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="text-purple-500"
          >
            <rect
              x="3"
              y="3"
              width="18"
              height="18"
              rx="2"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M8.5 7H15.5"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
            />
            <path
              d="M8.5 12H15.5"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
            />
            <path
              d="M8.5 17H15.5"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
            />
          </svg>
        </div>

        {/* Main upload icon with pulsing circle */}
        <div className="relative mb-6 flex h-20 w-20 items-center justify-center">
          <div className="animate-ping-slow bg-primary/20 absolute inset-0 rounded-full"></div>
          <div className="animate-ping-slow bg-primary/30 animation-delay-150 absolute inset-[3px] rounded-full"></div>
          <div className="bg-primary/10 relative flex h-16 w-16 items-center justify-center rounded-full p-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="32"
              height="32"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-primary"
            >
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
              <polyline points="17 8 12 3 7 8" />
              <line x1="12" y1="3" x2="12" y2="15" />
            </svg>
          </div>
        </div>

        {/* Text content */}
        <h3 className="mb-2 text-2xl font-medium">Drop Files</h3>
      </div>
    </div>
  );

  // For fullScreen mode, we don't need to render a wrapper component
  // We just need to show the overlay when dragging
  if (fullScreen) {
    return dropOverlay || <></>;
  }

  // Standard mode with children
  return (
    <div
      className="relative h-full w-full"
      onDragOver={handleDragOver}
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      {children}
      {dropOverlay}
    </div>
  );
};

export default ChatDropzone;
