/* eslint-disable @typescript-eslint/no-empty-function */
import { uploadDocuments } from '@/features/workspace/api/useUploadDocuments';
import { fetchCollectionById } from '@/features/workspace/api/useFetchCollectionById';
import Cookies from 'js-cookie';

export class ChatService {
  private ws: WebSocket | null = null;

  private audioQueue: ArrayBuffer[] = [];
  private audioContext: AudioContext | null = null;
  public gainNode: GainNode | null = null;

  private isPlayingAudio = false;
  private isStreaming = true;

  private endpoint: string;
  private onAgentInit: () => void;
  private onStreamEnd: () => void;
  private onMessage: (token: string) => void;
  private onToolCall: (data: any) => void;
  private onProgress: (data: any) => void;

  constructor({
    endpoint,
    onAgentInit = () => {},
    onStreamEnd = () => {},
    onMessage = (_: string) => {},
    onToolCall = (_: any) => {},
    onProgress: onProgress = (_: any) => {},
  }: {
    endpoint: string;
    onAgentInit?: () => void;
    onStreamEnd?: () => void;
    onMessage?: (token: string) => void;
    onToolCall?: (data: any) => void;
    onProgress?: (data: any) => void;
  }) {
    this.endpoint = endpoint;
    this.onAgentInit = onAgentInit;
    this.onStreamEnd = onStreamEnd;
    this.onMessage = onMessage;
    this.onToolCall = onToolCall;
    this.onProgress = onProgress;
  }

  public endStream() {
    this.isStreaming = false;
    this.isPlayingAudio = false;

    this.wsClose();
  }

  public async send(message: any) {
    await this.wsAssert();
    this.isStreaming = true;

    this.ws?.send(message);
  }

  public async sendMessage(message: any) {
    await this.wsAssert();
    this.isStreaming = true;
    const { attachments, ...messageWithoutAttachments } = message;
    const messageToSend = { ...messageWithoutAttachments };

    if (attachments && attachments.length > 0) {
      // Upload all non-image attachments to ingestion service
      const attachmentsToUpload = attachments.filter(
        (attachment: any) => !attachment.type.startsWith('image')
      );

      if (attachmentsToUpload.length > 0) {
        try {
          const userId = Cookies.get('userId') as string;

          const documentIds = attachmentsToUpload.map(
            (attachment: any) => attachment.id
          );
          const chatId = this.endpoint.match(/\/api\/chat\/([^/?]+)/)?.[1];

          await uploadDocuments({
            files: attachmentsToUpload.map((file: any) => file.file),
            collectionId: userId,
            documentIds: documentIds,
            chatId: chatId,
          });

          // Now poll for file status - will reject if timeout occurs
          await this.pollFileStatus(userId, documentIds);

          // Only proceed to next steps if polling completes successfully
        } catch (error) {
          console.error('Error uploading or processing files:', error);

          // End the stream on any error during file processing
          this.isStreaming = false;
          this.onStreamEnd();
          return; // Exit early without sending the message
        }
      }

      // Include ALL attachments in the message but only images have content field
      messageToSend.attachments = attachments.map((attachment: any) => ({
        id: attachment.id,
        name: attachment.name,
        type: attachment.type,
        size: attachment.size,
        content: attachment.type.startsWith('image')
          ? attachment.content
          : null,
      }));
    }

    this.ws?.send(
      JSON.stringify({
        e: 'message',
        d: messageToSend,
      })
    );
  }

  // New method for polling file status
  private async pollFileStatus(
    collectionId: string,
    documentIds: string[]
  ): Promise<void> {
    const POLLING_INTERVAL = 5000; // 5 seconds
    const MAX_POLLING_TIME = 1800000; // 30 minute max polling time
    const startTime = Date.now();

    // Continue polling until all documents are processed or timeout
    while (Date.now() - startTime < MAX_POLLING_TIME) {
      try {
        // Use fetchCollectionById to get document statuses
        const collectionData = await fetchCollectionById(collectionId);

        // Filter to only the documents we care about
        const relevantDocuments = collectionData.documents?.filter((doc) =>
          documentIds.includes(doc.id)
        );

        if (!relevantDocuments || relevantDocuments.length === 0) {
          console.warn('No matching documents found in collection');
          return; // Exit if we can't find the documents
        }

        // Map documents to their status objects
        const statuses = relevantDocuments.map((doc) => ({
          id: doc.id,
          status: doc.metadata.ingestion.status,
        }));

        // Check if all documents are processed (success or failed)
        const allProcessed = statuses.every(
          (doc) => doc.status === 'success' || doc.status === 'failed'
        );

        if (allProcessed) {
          console.log('All documents processed:', statuses);
          return;
        }

        await new Promise((resolve) => setTimeout(resolve, POLLING_INTERVAL));
      } catch (error) {
        console.error('Error checking document status:', error);
        // Continue polling even if there's an error
      }
    }

    // Return rejected promise instead of throwing
    return Promise.reject(
      new Error(
        'Document processing timed out after ' +
          MAX_POLLING_TIME / 60000 +
          ' minutes'
      )
    );
  }

  public muteAudio() {
    this.gainNode?.gain.setValueAtTime(0, this.audioContext!.currentTime);
  }

  public unMuteAudio() {
    this.gainNode?.gain.setValueAtTime(1, this.audioContext!.currentTime);
  }

  private async wsAssert() {
    if (!this.ws || this.ws.readyState === WebSocket.CLOSED) {
      await this.wsInit().catch((err) => {
        console.error(err);
      });
    }
  }

  private async wsInit() {
    return new Promise<void>((resolve, reject) => {
      this.ws = new WebSocket(this.endpoint);
      // TODO: change sessionId to agentId, store state in cosmos
      this.audioContext = new AudioContext();
      let bufferList: ArrayBuffer[] = [];

      this.ws.onopen = () => {
        resolve();
      };

      this.ws.onerror = (err) => {
        reject(err);
      };

      this.ws.onmessage = async (event) => {
        if (event.data instanceof Blob) {
          bufferList.push(await event.data.arrayBuffer());
        } else {
          const data = JSON.parse(event.data);
          switch (data.e) {
            case 'message':
              this.onMessage(data.d);
              break;
            case 'tool-call':
              this.onToolCall(data.d);
              break;
            case 'progress':
              this.onProgress(data.d);
              break;
            case 'agent-init':
              this.onAgentInit();
              break;
            case '':
              break;
            case 'stream-start':
              break;
            case 'stream-end':
              this.isStreaming = false;
              !this.isPlayingAudio && this.onStreamEnd();
              break;
            case 'audio-start':
              break;
            case 'audio-end':
              const totalLength = bufferList.reduce(
                (acc, buffer) => acc + buffer.byteLength,
                0
              );
              const combinedBuffer = new Uint8Array(totalLength);

              let offset = 0;
              bufferList.forEach((buffer) => {
                combinedBuffer.set(new Uint8Array(buffer), offset);
                offset += buffer.byteLength;
              });

              this.audioQueue.push(combinedBuffer.buffer);
              bufferList = [];

              !this.isPlayingAudio && this.playAudio();
              break;
            case 'exception':
              console.error(data.d);
              break;
            default:
              break;
          }
        }
      };

      this.ws.onclose = () => {
        this.wsClose();
      };
    });
  }

  private wsClose() {
    this.ws?.close();
    this.ws = null;

    this.audioQueue = [];
    this.audioContext?.close();
    this.audioContext = null;
    this.gainNode = null;

    this.onStreamEnd();
  }

  private playAudio() {
    if (this.audioQueue.length === 0) {
      if (!this.isStreaming) {
        this.isPlayingAudio = false;
        this.onStreamEnd();
        return;
      }
      setTimeout(() => {
        this.playAudio();
      }, 500);
    }
    if (!this.isPlayingAudio) this.isPlayingAudio = true;

    const audioBuffer = this.audioQueue.shift();
    audioBuffer &&
      this.audioContext!.decodeAudioData(audioBuffer, (buffer) => {
        const source = this.audioContext!.createBufferSource();
        source.buffer = buffer;

        if (!this.gainNode) {
          this.gainNode = this.audioContext!.createGain();
          this.gainNode.connect(this.audioContext!.destination);
        }

        source.connect(this.gainNode);
        source.start();

        source.onended = () => {
          setTimeout(() => {
            this.playAudio();
          }, 500);
        };
      });
  }

  public dispose() {
    this.wsClose();
  }
}
