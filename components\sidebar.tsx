'use client';

import Image from 'next/image';
import { usePathname, useRouter } from 'next/navigation';
import SidebarLink from './SidebarLink';
import { cn } from '@/lib/utils';
import {
  MessageSquare,
  GitBranch,
  LogOut,
  Atom,
  Mic,
  Sun,
  Moon,
  Settings,
  Headset,
  Menu,
} from 'lucide-react';
import { useTheme } from 'next-themes';
import { useSidebar } from '@/contexts/SidebarContext';
import { UserAvatar } from '@/features/auth/components/UserAvatar';
import { AdminElement } from '@/features/auth/components/PermissionedElement';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import SupportForm from '@/features/support/components/SupportForm';

// Define base and active classes for a dark theme sidebar
const linkBaseClasses =
  'flex h-10 w-10 items-center justify-center rounded-lg text-gray-400 transition-colors hover:text-white cursor-pointer'; // Adjusted for dark bg
const linkActiveClasses = 'bg-gray-700 text-white'; // Adjusted for dark bg

export default function Sidebar() {
  const { theme, setTheme } = useTheme();
  const router = useRouter();
  const pathname = usePathname();
  const { store: sidebarStore, setStore: setSidebarStore } = useSidebar();

  const handleAppLogout = async () => {
    router.replace('/logout');
  };

  return (
    <aside
      className={cn(
        'fixed inset-y-0 left-0 z-20 flex h-full w-[60px] flex-col bg-gray-900 py-2 transition-all duration-300 ease-in-out',
        // On mobile: hide by default, show when isOpen is true (as overlay)
        sidebarStore.isMobile && !sidebarStore.isOpen && '-translate-x-full',
        // On mobile: when visible, add shadow to indicate it's an overlay
        sidebarStore.isMobile && sidebarStore.isOpen && 'shadow-xl'
      )}
    >
      <nav className="flex flex-1 flex-col items-center space-y-2 p-2">
        {/* Mobile menu button */}
        {sidebarStore.isMobile && (
          <button
            type="button"
            className={cn(linkBaseClasses, 'mb-3')}
            title={sidebarStore.isOpen ? 'Close Menu' : 'Open Menu'}
            onClick={() =>
              setSidebarStore((prev: any) => ({
                ...prev,
                isOpen: !prev.isOpen,
              }))
            }
          >
            <Menu className="h-5 w-5" />
            <span className="sr-only">Toggle Menu</span>
          </button>
        )}
        <SidebarLink
          href="/c"
          className="mb-3 flex h-10 w-10 cursor-pointer items-center justify-center rounded-lg"
        >
          <Image src="/logo-icon.svg" alt="App Logo" width={32} height={32} />
          <span className="sr-only">App Logo</span>
        </SidebarLink>
        <SidebarLink
          href="/c" // Keep exact match for root Chat page
          className={cn(
            linkBaseClasses,
            (pathname === '/' || pathname.startsWith('/c')) && linkActiveClasses
          )}
          title="Chat"
        >
          <MessageSquare className="h-5 w-5" />
          <span className="sr-only">Chat</span>
        </SidebarLink>
        <SidebarLink
          href="/workspace/model" // Keep exact match for root Chat page
          className={cn(
            linkBaseClasses,
            (pathname === '/' || pathname.startsWith('/workspace')) &&
              linkActiveClasses
          )}
          title="Workspace"
        >
          <Atom className="h-4 w-4" />
          <span className="sr-only">Workspace</span>
        </SidebarLink>
        {/* Knowledge is part of WorkSpace, can remove this */}
        {/* <Link
          href="/knowledge" // Use prefix match for Knowledge section
          className={cn(
            linkBaseClasses,
            pathname.startsWith('/knowledge') && linkActiveClasses
          )}
          title="Knowledge"
        >
          <Library className="h-5 w-5" />
          <span className="sr-only">Knowledge</span>
        </Link> */}
        {/* <SidebarLink
          href="/workflow" // Use prefix match for Workflow section
          className={cn(
            linkBaseClasses,
            pathname.startsWith('/workflow') && linkActiveClasses
          )}
          title="Workflow"
        >
          <GitBranch className="h-5 w-5" />
          <span className="sr-only">Workflow</span>
        </SidebarLink> */}
        <SidebarLink
          href="/recording" // Use prefix match for Workflow section
          className={cn(
            linkBaseClasses,
            pathname.startsWith('/recording') && linkActiveClasses
          )}
          title="Recording"
        >
          <Mic className="h-5 w-5" />
          <span className="sr-only">Recording</span>
        </SidebarLink>
      </nav>
      <nav className="mt-auto flex flex-col items-center space-y-1 p-2">
        <Popover>
          <PopoverTrigger asChild>
            <button
              type="button"
              className={cn(linkBaseClasses)}
              title="Support"
            >
              <Headset className="h-5 w-5" />
              <span className="sr-only">Support</span>
            </button>
          </PopoverTrigger>
          <PopoverContent
            className="w-[400px] p-4"
            align="end"
            alignOffset={0}
            sideOffset={0}
            side="right"
          >
            <SupportForm
              onClose={() => {
                // Find the popover trigger button and click it to close the popover
                const popoverTrigger =
                  document.querySelector('[title="Support"]');
                if (popoverTrigger instanceof HTMLElement) {
                  popoverTrigger.click();
                }
              }}
            />
          </PopoverContent>
        </Popover>

        {theme === 'dark' ? (
          <button
            type="button"
            onClick={() => setTheme('light')}
            className={cn(linkBaseClasses)}
            title="Light Mode"
          >
            <Sun className="h-5 w-5" />
          </button>
        ) : (
          <button
            type="button"
            onClick={() => setTheme('dark')}
            className={cn(linkBaseClasses)}
            title="Dark Mode"
          >
            <Moon className="h-5 w-5" />
          </button>
        )}

        <div className="py-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <div className="cursor-pointer">
                <UserAvatar className="h-8 w-8" />
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" sideOffset={10}>
              <AdminElement>
                <DropdownMenuItem
                  className="cursor-pointer"
                  onClick={() => router.push('/admin/dashboard')}
                >
                  <Settings className="mr-2 h-4 w-4" />
                  Admin Panel
                </DropdownMenuItem>
              </AdminElement>
              <DropdownMenuItem
                className="cursor-pointer"
                onClick={handleAppLogout}
              >
                <LogOut className="mr-2 h-4 w-4" />
                Logout
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        {/* <button
          type="button"
          onClick={handleAppLogout}
          className={cn(linkBaseClasses)}
          title="Logout"
        >
          <LogOut className="h-5 w-5" />
          <span className="sr-only">Logout</span>
        </button> */}
      </nav>
    </aside>
  );
}
