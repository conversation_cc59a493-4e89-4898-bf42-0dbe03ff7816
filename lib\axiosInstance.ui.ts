import axios from 'axios';
import Cookies from 'js-cookie';

// Please don't change the timeout here, you can setup
// timeout for each API request, instead of the global setup
const axiosInstance = axios.create({
  baseURL: '/api',
  timeout: 60 * 1000, // 1 minutes timeout
  headers: {
    Accept: 'application/json',
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
  },
});

// Request Interceptor: Add Auth token before sending requests
axiosInstance.interceptors.request.use(
  async (config) => {
    const token = Cookies.get('token');
    config.headers.Authorization = `Bearer ${token}`;
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response Interceptor: Handle global errors
axiosInstance.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error('API Call Error:', error);
    if (error.response) {
      const status = error.response.status;
      if ([401, 403].includes(status)) {
        console.warn('Unauthorized or Forbidden:', status);
        window.location.href = '/logout';
      } else {
        console.error('Internal server error:', status);
      }
    } else if (error.request) {
      // The request was made but no response was received (e.g., network error)
      console.error('Network Error or No Response:', error.request);
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('Axios Config Error:', error.message);
    }

    return Promise.reject(error);
  }
);

export default axiosInstance;
