import { IModelCreate, IModelData } from '@/types/model';

export const getModelTemplate = (
  model?: Partial<IModelData> | null,
  defaultModelId?: string
): IModelCreate => {
  console.log('??? model', model);
  return {
    name: model?.name ?? 'New Agent',
    baseModel: model?.base_model ?? defaultModelId ?? '',
    description: model?.description ?? '',
    prompt: model?.config?.prompt ?? '',
    prompt_suggestions: model?.config?.prompt_suggestions ?? [],
    collectionIds: model?.collectionIds ?? [],
    toolIds: model?.tools?.map((tool) => tool.id) ?? [],
    groupIds: model?.groups?.map((group) => group.id) ?? [],
    image: model?.image ?? undefined,
  };
};
