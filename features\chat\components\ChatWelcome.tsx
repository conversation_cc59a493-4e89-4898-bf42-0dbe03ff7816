'use client';

import React, { useEffect, useState } from 'react';
import ChatInput from './ChatInput';
import { IMessage, MessageRoleEnum } from '@/types/chat';
import { v7 as uuidv7 } from 'uuid';
import { useChuckContext } from '@/contexts/ChuckContext';
import { useGlobal } from '@/contexts/GlobalContext';
import { useRouter } from 'next/navigation';
import { useFetchModelById } from '@/features/workspace/api/useFetchModelById';
import { Zap } from 'lucide-react';

const ChatWelcome = () => {
  const { store: chuckStore, setStore } = useChuckContext();
  const { store: globalStore } = useGlobal();
  const router = useRouter();
  const { data: modelData, isLoading: isLoadingModel } = useFetchModelById(
    chuckStore.modelId
  );
  const [showSuggestions, setShowSuggestions] = useState(false);

  // Add animation effect when suggestions are loaded
  useEffect(() => {
    if (modelData?.config?.prompt_suggestions && !isLoadingModel) {
      // Delay showing suggestions for a smoother experience
      const timer = setTimeout(() => {
        setShowSuggestions(true);
      }, 300);

      return () => clearTimeout(timer);
    }
  }, [modelData, isLoadingModel]);

  const handleSendMessage = async (message: IMessage) => {
    if (!message.content.trim() && message.attachments.length === 0) return;

    const chatId = uuidv7();
    setStore((prev) => ({
      ...prev,
      newChatMessage: message,
    }));
    router.push(`/c/${chatId}`);
  };

  // Get first name from full name
  const getFirstName = (fullName: string | undefined): string | null => {
    if (!fullName) return null;
    return fullName.split(' ')[0];
  };

  const handlePromptSuggestionClick = (suggestion: string) => {
    // Use a short delay to ensure the DOM is ready
    setTimeout(() => {
      const textarea = document.querySelector('textarea');
      if (textarea) {
        textarea.value = suggestion;
        textarea.focus();
        const nativeInputEvent = new Event('input', {
          bubbles: true,
          cancelable: true,
        });
        textarea.dispatchEvent(nativeInputEvent);
      }
    }, 100);
  };

  return (
    <div className="flex h-full w-full justify-center px-4 pt-[10%]">
      <div className="w-full max-w-[860px]">
        <div className="py-[20px]">
          <h1 className="text-center text-3xl font-bold">
            {globalStore.user && (
              <p>Hey {getFirstName(globalStore.user.name)}</p>
            )}
            <p>How can I help today?</p>
          </h1>
          {modelData?.description && (
            <div className="mt-4 flex justify-center">
              <div className="max-w-[600px] text-center">
                {modelData.description}
              </div>
            </div>
          )}
        </div>
        <ChatInput onSend={handleSendMessage} withImagePaste />

        {!isLoadingModel &&
          chuckStore.modelId &&
          modelData?.config?.prompt_suggestions &&
          modelData.config.prompt_suggestions.length > 0 && (
            <div
              className={`mt-8 flex justify-center transition-all duration-500 ease-in-out ${
                showSuggestions
                  ? 'translate-y-0 opacity-100'
                  : 'translate-y-4 opacity-0'
              }`}
            >
              <div>
                <h2 className="text-muted-foreground text-s1 mb-3 flex items-center gap-1">
                  <Zap size={16} className="animate-pulse text-yellow-500" />
                  <span>Suggested</span>
                </h2>
                <div className="flex flex-col gap-1 px-2">
                  {modelData.config.prompt_suggestions.map(
                    (suggestion, index) => (
                      <div
                        key={index}
                        className={`text-medium hover:bg-accent/70 cursor-pointer rounded-lg p-1 px-3 transition-all duration-200 hover:scale-[1.005] ${
                          showSuggestions
                            ? 'translate-x-0 opacity-100'
                            : 'translate-x-4 opacity-0'
                        } delay-${(index + 1) * 100}`}
                        onClick={() => handlePromptSuggestionClick(suggestion)}
                      >
                        {suggestion}
                      </div>
                    )
                  )}
                </div>
              </div>
            </div>
          )}
      </div>
    </div>
  );
};

export default ChatWelcome;
