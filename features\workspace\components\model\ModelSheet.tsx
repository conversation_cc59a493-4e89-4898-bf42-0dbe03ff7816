'use client';

import React, { use<PERSON>emo, useState, useEffect } from 'react';

import {
  Sheet,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>eader,
  Sheet<PERSON>it<PERSON>,
} from '@/components/ui/sheet';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { useFetchModelById } from '@/features/workspace/api/useFetchModelById'; // Import the detail fetch hook
import { useFetchModelVersions } from '@/features/workspace/api/useFetchModelVersions';
import LoadingSpinner from '@/components/LoadingSpinner';
import ModelForm from './ModelForm';
import ModelVersionList from './ModelVersionList';
import ModelVersionComparison from './ModelVersionComparison';
import { IModelData, IModelVersion, ModelTypeEnum } from '@/types/model';
import { useFetchRawModels } from '@/features/workspace/api/useFetchModels';
import { useFetchCollections } from '@/features/workspace/api/useFetchCollections';
import { useFetchTools } from '@/features/workspace/api/useFetchTools';
import { useFetchGroups } from '@/features/admin/api/useFetchGroups';
import { useSidebar } from '@/contexts/SidebarContext';

interface ModelFormProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  model?: IModelData | null;
}

export default function ModelSheet({
  isOpen,
  onOpenChange,
  model,
}: ModelFormProps) {
  const isEditMode = !!model?.id;
  const [selectedVersion, setSelectedVersion] = useState<
    IModelVersion | undefined
  >();
  const { store: sidebarStore } = useSidebar();

  const { data: collections, isLoading: isLoadingCollections } =
    useFetchCollections();
  const { data: allModels, isLoading: isLoadingAllModels } =
    useFetchRawModels();
  const { data: tools, isLoading: isLoadingTools } = useFetchTools();
  const { data: groups, isLoading: isLoadingGroups } = useFetchGroups();
  const { data: detailedModelData, isLoading: isFetchingDetailedModel } =
    useFetchModelById(model?.id ?? null);
  const { data: versionsData, isLoading: isLoadingVersions } =
    useFetchModelVersions(model?.id ?? null);

  const sheetTitle = isEditMode ? 'Edit Agent' : 'Create Agent';
  const showVersions = isEditMode && !sidebarStore.isMobile;

  // Auto-select the latest version when versions data is loaded
  useEffect(() => {
    if (
      versionsData?.versions &&
      versionsData.versions.length > 0 &&
      !selectedVersion
    ) {
      // Select the first version (latest) by default
      setSelectedVersion(versionsData.versions[0]);
    }
  }, [versionsData?.versions, selectedVersion]);

  // Reset selected version when modal is closed or model changes
  useEffect(() => {
    if (!isOpen || !model?.id) {
      setSelectedVersion(undefined);
    }
  }, [isOpen, model?.id]);
  const baseModels = useMemo(() => {
    if (!allModels) return [];
    return allModels.filter((m) => m.type === ModelTypeEnum.MODEL);
  }, [allModels]);

  const collectionOptions = useMemo(() => {
    if (!collections) return [];
    return collections.map((c) => ({ value: c.id, label: c.name }));
  }, [collections]);

  const toolOptions = useMemo(() => {
    if (!tools) return [];
    return tools.map((tool) => ({ value: tool.id, label: tool.name }));
  }, [tools]);

  const groupOptions = useMemo(() => {
    if (!groups) return [];
    return groups.map((group) => ({ value: group.id, label: group.name }));
  }, [groups]);

  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetContent
        className="sm:max-w-[1200px]"
        onEscapeKeyDown={(e: KeyboardEvent) => e.preventDefault()}
      >
        <ScrollArea className="h-full w-full">
          <SheetHeader>
            <SheetTitle>{sheetTitle}</SheetTitle>
          </SheetHeader>

          {isFetchingDetailedModel ||
          isLoadingAllModels ||
          isLoadingCollections ||
          isLoadingTools ||
          isLoadingGroups ? (
            <div className="p-8">
              <LoadingSpinner />
            </div>
          ) : showVersions ? (
            <Tabs defaultValue="edit" className="w-full px-4">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="edit">Edit</TabsTrigger>
                <TabsTrigger value="versions">Versions</TabsTrigger>
              </TabsList>

              <TabsContent value="edit" className="mt-2">
                <ModelForm
                  modelOptions={baseModels}
                  collectionOptions={collectionOptions}
                  toolOptions={toolOptions}
                  groupOptions={groupOptions}
                  onOpenChange={onOpenChange}
                  model={isEditMode ? detailedModelData : null}
                />
              </TabsContent>

              <TabsContent value="versions" className="mt-2">
                {isLoadingVersions ? (
                  <div className="p-8">
                    <LoadingSpinner />
                  </div>
                ) : (
                  <div className="flex gap-6">
                    <div className="min-w-[200px]">
                      <ModelVersionList
                        versions={versionsData?.versions || []}
                        selectedVersionId={selectedVersion?.id}
                        onVersionSelect={setSelectedVersion}
                        currentVersionId={detailedModelData?.id}
                      />
                    </div>
                    <div className="flex-1">
                      <ModelVersionComparison
                        currentModel={detailedModelData!}
                        selectedVersion={selectedVersion}
                      />
                    </div>
                  </div>
                )}
              </TabsContent>
            </Tabs>
          ) : (
            <div className="px-4">
              <ModelForm
                modelOptions={baseModels}
                collectionOptions={collectionOptions}
                toolOptions={toolOptions}
                groupOptions={groupOptions}
                onOpenChange={onOpenChange}
                model={isEditMode ? detailedModelData : null}
              />
            </div>
          )}
        </ScrollArea>
      </SheetContent>
    </Sheet>
  );
}
