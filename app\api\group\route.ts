import { NextRequest, NextResponse } from 'next/server';
import axiosInstanceApi from '@/lib/axiosInstance.api';
import { API_CONFIG } from '@/config/api';

export async function GET(request: NextRequest) {
  const authorization = request.headers.get('Authorization');
  const targetPath = API_CONFIG.admin.groupList;

  try {
    const apiResponse = await axiosInstanceApi.get(targetPath, {
      headers: {
        ...(authorization && { Authorization: authorization }),
      },
    });

    if (apiResponse.data) {
      // Sort groups by name
      apiResponse.data.sort((a: any, b: any) => a.name.localeCompare(b.name));
    }

    return NextResponse.json(apiResponse.data, { status: apiResponse.status });
  } catch (error: any) {
    console.error(
      `[API Route Proxy Error] GET ${targetPath}:`,
      error.response?.data || error.message
    );
    const status = error.response?.status || 500;
    const message =
      error.response?.data?.message || 'Proxy error fetching groups';
    return NextResponse.json({ message }, { status });
  }
}

export async function POST(request: NextRequest) {
  const authorization = request.headers.get('Authorization');
  const targetPath = API_CONFIG.admin.group;

  try {
    const payload = await request.json();

    console.log(
      `[API Route Proxy] Forwarding POST ${targetPath} with Auth: ${!!authorization}`
    );

    const apiResponse = await axiosInstanceApi.post(targetPath, payload, {
      headers: {
        'Content-Type': 'application/json',
        ...(authorization && { Authorization: authorization }),
      },
    });

    return NextResponse.json(apiResponse.data, { status: apiResponse.status });
  } catch (error: any) {
    console.error(
      `[API Route Proxy Error] POST ${targetPath}:`,
      error.response?.data || error.message
    );

    // Handle JSON parsing errors specifically
    if (error instanceof SyntaxError) {
      return NextResponse.json(
        { message: 'Invalid JSON payload' },
        { status: 400 }
      );
    }

    const status = error.response?.status || 500;
    const message =
      error.response?.data?.message || 'Proxy error creating group';
    return NextResponse.json({ message }, { status });
  }
}
