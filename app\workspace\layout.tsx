'use client';

import { ProtectedRoute } from '@/contexts/GlobalContext';
import MainLayout from '@/components/MainLayout';
import WorkspaceLayout from '@/features/workspace/components/WorkspaceLayout';

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ProtectedRoute>
      <MainLayout>
        <WorkspaceLayout>{children}</WorkspaceLayout>
      </MainLayout>
    </ProtectedRoute>
  );
}
