'use client';

import React from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from '@/components/ui/chart';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
} from 'recharts';
import { Skeleton } from '@/components/ui/skeleton';
import { IDashboardData, IChatMetric } from '@/types/dashboard';
import dayjs from 'dayjs';

interface ChatMetricsChartProps {
  data: IDashboardData;
  isLoading?: boolean;
}

export default function ChatMetricsChart({
  data,
  isLoading = false,
}: ChatMetricsChartProps) {
  // Format the data for the chart
  const chartData = React.useMemo(() => {
    if (!data.chatMetricsDetails) return [];

    return data.chatMetricsDetails.map((metric) => ({
      date: dayjs(metric.date).format('MMM DD'),
      count: metric.count,
      fullDate: metric.date, // Keep the original date for tooltip
    }));
  }, [data.chatMetricsDetails]);

  const chartConfig = {
    count: {
      label: 'Chats',
      color: 'var(--chart-1)',
    },
  };

  // Find the maximum count to set a reasonable Y-axis domain
  const maxCount = React.useMemo(() => {
    if (!chartData.length) return 100;
    const max = Math.max(...chartData.map((item) => item.count));
    return max > 0 ? Math.ceil(max * 1.05) : 100; // Add 5% padding, minimum 100
  }, [chartData]);

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">Chat Activity</CardTitle>
        <CardDescription className="text-xs">
          Daily chat activity over the last 30 days ({data.chatsLast30Days || 0}{' '}
          total)
        </CardDescription>
      </CardHeader>
      <CardContent className="h-[500px] px-2">
        {isLoading ? (
          <div className="flex h-full w-full items-center justify-center">
            <Skeleton className="h-[300px] w-full" />
          </div>
        ) : (
          <div className="h-full w-full">
            <ChartContainer config={chartConfig} className="h-full w-full">
              <BarChart
                data={chartData}
                margin={{ top: 5, right: 20, bottom: 15, left: 20 }}
                barGap={2}
                barCategoryGap={8}
                height={300}
              >
                <CartesianGrid
                  strokeDasharray="3 3"
                  vertical={false}
                  opacity={0.2}
                  stroke="var(--border)"
                />
                <XAxis
                  dataKey="date"
                  tickLine={false}
                  axisLine={false}
                  tickMargin={10}
                  tick={{ fontSize: 11, fill: 'var(--muted-foreground)' }}
                  interval="preserveStartEnd"
                  minTickGap={20}
                />
                <YAxis
                  tickLine={false}
                  axisLine={false}
                  tickMargin={10}
                  tick={{ fontSize: 11, fill: 'var(--muted-foreground)' }}
                  domain={[0, maxCount]}
                  width={40}
                />
                <ChartTooltip
                  content={({ active, payload }) => {
                    if (active && payload && payload.length) {
                      const data = payload[0].payload;
                      return (
                        <ChartTooltipContent
                          active={active}
                          payload={payload}
                          formatter={(value) => value.toLocaleString()}
                          label={`${dayjs(data.fullDate).format('MMMM D, YYYY')}`}
                        />
                      );
                    }
                    return null;
                  }}
                />
                <Bar
                  dataKey="count"
                  radius={[4, 4, 0, 0]}
                  maxBarSize={16}
                  fill="var(--chart-1)"
                  opacity={0.8}
                />
              </BarChart>
            </ChartContainer>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
