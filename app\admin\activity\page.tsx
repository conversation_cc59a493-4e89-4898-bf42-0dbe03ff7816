'use client';

import React, { useState } from 'react';
import { useFetchActivities } from '@/features/admin/api/useFetchActivities';
import { IDashboardActivity } from '@/types/dashboard';
import LoadingSpinner from '@/components/LoadingSpinner';
import dayjs from 'dayjs';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';

const PAGE_SIZES = [30, 50, 100];

export default function ActivityPage() {
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(PAGE_SIZES[0]);
  const { data, isLoading, error, isFetching } = useFetchActivities(
    page,
    limit
  );

  // Generate initials from name
  const getInitials = (name: string) => {
    if (!name || typeof name !== 'string') return '';

    // Remove any special characters and split by spaces
    const cleanName = name.replace(/[^\w\s]/gi, '').trim();
    if (!cleanName) return '';

    const nameParts = cleanName.split(/\s+/).filter(Boolean);
    if (nameParts.length === 0) return '';

    // Get first letter of each part, up to 2 letters
    const initials = nameParts
      .slice(0, 2)
      .map((part) => part.charAt(0))
      .filter((char) => /[A-Za-z]/.test(char))
      .join('')
      .toUpperCase();

    return initials || 'U'; // Default to 'U' for unknown if no valid initials
  };

  // Format timestamp
  const formatTimestamp = (timestamp: string) => {
    try {
      return dayjs(timestamp).format('MMM D, YYYY h:mm A');
    } catch (e) {
      return timestamp;
    }
  };

  // Handle page change
  const handlePageChange = (newPage: number) => {
    // Only change page if it's different from current page
    if (newPage !== page) {
      // Scroll to top immediately
      window.scrollTo(0, 0);

      // Update the page state
      setPage(newPage);
    }
  };

  // Get pagination data from API response
  const totalPages = data ? data.pagination.total_pages : 0;

  // Track if we're currently changing pages
  const [isChangingPage, setIsChangingPage] = React.useState(false);

  // Handle page changes and API synchronization
  React.useEffect(() => {
    // If we have data and the API page doesn't match our local state
    if (data && data.pagination.page !== page) {
      // If we're not in the middle of a manual page change
      if (!isChangingPage) {
        // Sync with the API's page
        setPage(data.pagination.page);
      }
    }

    // Reset the changing page flag once data is loaded
    if (isChangingPage && !isFetching) {
      setIsChangingPage(false);
    }
  }, [data, page, isFetching, isChangingPage]);

  // Generate page numbers to display
  const getPageNumbers = () => {
    const pages = [];
    const maxPagesToShow = 5;

    if (totalPages <= maxPagesToShow) {
      // Show all pages if there are fewer than maxPagesToShow
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Always show first page
      pages.push(1);

      // Calculate start and end of the middle section
      let startPage = Math.max(2, page - 1);
      let endPage = Math.min(totalPages - 1, page + 1);

      // Adjust if we're near the beginning
      if (page <= 3) {
        endPage = 4;
      }

      // Adjust if we're near the end
      if (page >= totalPages - 2) {
        startPage = totalPages - 3;
      }

      // Add ellipsis after first page if needed
      if (startPage > 2) {
        pages.push('ellipsis1');
      }

      // Add middle pages
      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }

      // Add ellipsis before last page if needed
      if (endPage < totalPages - 1) {
        pages.push('ellipsis2');
      }

      // Always show last page
      pages.push(totalPages);
    }

    return pages;
  };

  if (isLoading) {
    return (
      <div className="flex h-full w-full items-center p-4">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4">
        <p className="text-destructive">
          Error loading activity data: {error.message}
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="relative rounded-md border">
        {isFetching && !isLoading && (
          <div className="bg-background/80 absolute inset-0 z-10 flex items-center justify-center">
            <LoadingSpinner size="sm" />
          </div>
        )}
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[180px]">Timestamp</TableHead>
              <TableHead className="w-[200px]">User</TableHead>
              <TableHead className="w-[150px]">Activity Type</TableHead>
              <TableHead>Summary</TableHead>
              <TableHead>Details</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data?.items.map((activity: IDashboardActivity) => (
              <TableRow key={activity.id}>
                <TableCell className="whitespace-nowrap">
                  {formatTimestamp(activity.ts)}
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Avatar className="h-6 w-6">
                      <AvatarFallback className="bg-gray-600 text-xs text-white">
                        {getInitials(activity.userName)}
                      </AvatarFallback>
                    </Avatar>
                    <span>{activity.userName}</span>
                  </div>
                </TableCell>
                <TableCell>{activity.activityType}</TableCell>
                <TableCell>{activity.summary}</TableCell>
                <TableCell className="max-w-md">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" size="sm">
                        <span>Details</span>
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent
                      className="w-[500px] p-0"
                      align="start"
                      side="right"
                    >
                      <div className="flex items-center justify-between border-b px-4 py-2">
                        <h4 className="font-medium">Activity Details</h4>
                      </div>
                      <ScrollArea className="h-auto">
                        <div className="p-4">
                          {typeof activity.details === 'string' ? (
                            <p className="text-sm">{activity.details}</p>
                          ) : (
                            <div className="bg-accent rounded-md p-4">
                              <pre className="text-foreground overflow-x-auto text-xs break-words whitespace-pre-wrap">
                                {JSON.stringify(activity.details, null, 2)}
                              </pre>
                            </div>
                          )}
                        </div>
                      </ScrollArea>
                    </PopoverContent>
                  </Popover>
                </TableCell>
              </TableRow>
            ))}

            {(!data?.items || data.items.length === 0) && (
              <TableRow>
                <TableCell colSpan={5} className="h-24 text-center">
                  No activity data found
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {data && (
        <div className="mt-4 flex flex-col justify-between gap-2 sm:flex-row sm:items-center">
          <div className="text-muted-foreground text-xs text-nowrap">
            Showing {data.items.length} of {data.pagination.total_items}
            {data.pagination.total_pages > 1 && (
              <>
                {' '}
                (Page {data.pagination.page} of {data.pagination.total_pages})
              </>
            )}
          </div>

          <div className="flex items-center gap-8">
            <div className="flex flex-nowrap items-center">
              <div className="flex items-center gap-2">
                <label
                  htmlFor="limit-select"
                  className="text-muted-foreground text-sm text-nowrap"
                >
                  Size
                </label>
                <select
                  id="limit-select"
                  className="border-input bg-background h-8 rounded-md border px-3 py-1 text-sm"
                  value={limit}
                  onChange={(e) => {
                    setIsChangingPage(true);
                    setLimit(Number(e.target.value));
                    setPage(1); // Reset to first page when changing limit
                  }}
                >
                  {PAGE_SIZES.map((size) => (
                    <option key={size} value={size}>
                      {size}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            {totalPages > 0 && (
              <Pagination>
                <PaginationContent className="flex-wrap">
                  <PaginationItem>
                    <PaginationPrevious
                      size="default"
                      onClick={() => {
                        setIsChangingPage(true);
                        handlePageChange(Math.max(1, page - 1));
                      }}
                      className={
                        page === 1 ? 'pointer-events-none opacity-50' : ''
                      }
                    />
                  </PaginationItem>

                  {getPageNumbers().map((pageNum, i) => (
                    <PaginationItem key={i}>
                      {pageNum === 'ellipsis1' || pageNum === 'ellipsis2' ? (
                        <PaginationEllipsis />
                      ) : (
                        <PaginationLink
                          size="default"
                          isActive={page === pageNum}
                          onClick={() => {
                            setIsChangingPage(true);
                            handlePageChange(pageNum as number);
                          }}
                        >
                          {pageNum}
                        </PaginationLink>
                      )}
                    </PaginationItem>
                  ))}

                  <PaginationItem>
                    <PaginationNext
                      size="default"
                      onClick={() => {
                        setIsChangingPage(true);
                        handlePageChange(Math.min(totalPages, page + 1));
                      }}
                      className={
                        page === totalPages
                          ? 'pointer-events-none opacity-50'
                          : ''
                      }
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
