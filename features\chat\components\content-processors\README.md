# Content Processor System

This module provides a modular and extensible system for processing different types of code blocks in Markdown content.

## Architecture

The system uses a combination of the Strategy and Chain of Responsibility patterns:

1. **ContentProcessor Interface**: Each processor implements this interface to handle a specific type of content.
2. **ContentProcessorRegistry**: Maintains a registry of processors and delegates processing to the appropriate one.
3. **ArtifactCollector**: Collects artifacts (like HTML content) that need to be displayed in a separate panel.

## Two-Phase Rendering Process

The content processor system operates in two distinct phases:

1. **Processing Phase**:

   - Converts Markdown code blocks into appropriate HTML structures
   - Executed during Markdown parsing via the custom Marked.js renderer
   - Happens before the content is inserted into the DOM

2. **Rendering Phase**:
   - Initializes and renders the dynamic content after it's in the DOM
   - Uses specialized renderer modules (e.g., `renderers/mermaid`, `renderers/chartjs`)
   - Requires useEffect hooks in components like ChatMessageContentRender.tsx

Why we need both phases:

- Libraries like Mermaid and Chart.js require elements to be in the DOM before they can render
- The rendering needs to respond to changes in app state (e.g., theme changes)
- We need to properly cleanup resources when components unmount

This is why even with the processor system, we still need separate renderer modules and useEffect hooks in the ChatMessageContentRender component that call functions like `renderMermaidDiagrams` and `renderCharts`.

## Available Processors

- **MermaidProcessor**: Renders Mermaid diagrams
- **HtmlArtifactProcessor**: Processes HTML code blocks, providing a preview button
- **ChartJsProcessor**: Renders Chart.js visualizations
- **SyntaxHighlightProcessor**: Applies syntax highlighting to code blocks

## How to Add a New Processor

1. Create a new class that implements the `ContentProcessor` interface:

```typescript
import {
  ContentProcessor,
  ContentProcessorContext,
  ProcessedContent,
} from './types';

export class MyCustomProcessor implements ContentProcessor {
  canProcess(ctx: ContentProcessorContext): boolean {
    // Determine if this processor can handle the given content
    return ctx.lang === 'custom-lang';
  }

  process(ctx: ContentProcessorContext): ProcessedContent {
    // Process the content and return HTML
    return {
      html: `<div class="custom-output">${ctx.code}</div>`,
      skipNextProcessors: true,
    };
  }
}
```

2. Register your processor in `index.ts`:

```typescript
import { MyCustomProcessor } from './my-custom-processor';

// Register your processor (order matters - most specific first)
contentProcessorRegistry.register(new MyCustomProcessor());
```

3. If your processor requires DOM manipulation after rendering, create a renderer module:

```typescript
// components/chat/renderers/custom/index.ts
export function renderCustomContent({ container, theme }) {
  // Initialize your library with the DOM elements
  // Return a cleanup function
  return () => {
    // Cleanup resources
  };
}
```

4. Add a useEffect in the component that uses your processor:

```typescript
// In ChatMessageContentRender.tsx
useEffect(() => {
  if (!contentRef.current) return;

  return renderCustomContent({
    container: contentRef.current,
    theme,
  });
}, [contentRef, theme, innerContent]);
```

## Integration with Marked.js

The system integrates with Marked.js using a custom renderer. Use the `createCustomMarkedRenderer` function to create a renderer that leverages the processor system:

```typescript
import { createCustomMarkedRenderer } from '@/lib/utils/content-processors/marked-renderer';
import { marked } from 'marked';

const renderer = createCustomMarkedRenderer({
  theme: 'dark',
  isStreaming: false,
});

const html = marked.parse(markdownContent, {
  renderer,
  breaks: true,
  gfm: true,
});
```

## Artifact Collection

Some processors may generate artifacts (like HTML content) that need to be displayed separately. The `ArtifactCollector` class is used to collect these artifacts:

```typescript
import { createArtifactCollector } from '@/lib/utils/content-processors/artifact-collector';

const collector = createArtifactCollector();
// Pass to the renderer
const renderer = createCustomMarkedRenderer({
  artifactCollector: collector,
});

// Later, retrieve all collected artifacts
const artifacts = collector.getAll();
```
