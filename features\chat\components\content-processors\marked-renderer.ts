import { marked } from 'marked';
import { processCodeBlock } from './index';
import { ArtifactCollector } from './artifact-collector';

interface MarkedCodeOptions {
  text: string;
  lang?: string;
  escaped?: boolean;
}

type CodeRendererFn = (opts: MarkedCodeOptions) => string;

/**
 * Create a custom renderer for marked that uses our content processors
 */
export function createCustomMarkedRenderer(options?: {
  theme?: string;
  isStreaming?: boolean;
  artifactCollector?: ArtifactCollector;
}) {
  const renderer = new marked.Renderer();

  (renderer.code as unknown as CodeRendererFn) = function ({
    text: code,
    lang,
  }: MarkedCodeOptions): string {
    const result = processCodeBlock(code, lang, options);

    if (result.artifacts && options?.artifactCollector) {
      options.artifactCollector.addAll(result.artifacts);
    }

    return result.html;
  };

  return renderer;
}

/**
 * Processes HTML content to enhance tables, inline code, etc.
 */
export function enhanceHtmlContent(htmlContent: string): string {
  let processedHtml = htmlContent.replace(
    /<table>/g,
    '<div class="table-wrapper"><table>'
  );
  processedHtml = processedHtml.replace(/<\/table>/g, '</table></div>');

  processedHtml = processedHtml.replace(
    /<code(?!\s+class="(hljs|tool))(?:\s+class="([^"]*)")?>([\s\S]*?)<\/code>/g,
    (_match, _hljs, existingClass, content) => {
      const classNames = existingClass
        ? `copyable ${existingClass}`
        : 'copyable';
      return `<code class="${classNames}"><span class="code-content">${content}</span></code>`;
    }
  );

  processedHtml = processedHtml.replace(
    /<div class="table-wrapper"><table>([\s\S]*?)<\/table><\/div>/g,
    '<div class="table-wrapper copyable"><table>$1</table></div>'
  );

  return processedHtml;
}
