'use client';

import React, { useState, useRef, useEffect } from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { X, GripVertical, MoveDiagonal2 } from 'lucide-react';

interface PromptHelpPanelProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function PromptHelpPanel({
  isOpen,
  onClose,
}: PromptHelpPanelProps) {
  const [position, setPosition] = useState({ x: 100, y: 100 });
  const [size, setSize] = useState({ width: 500, height: 600 });
  const [isDragging, setIsDragging] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [resizeStart, setResizeStart] = useState({
    x: 0,
    y: 0,
    width: 0,
    height: 0,
  });
  const panelRef = useRef<HTMLDivElement>(null);

  const handleMouseDown = (e: React.MouseEvent) => {
    if (panelRef.current) {
      const rect = panelRef.current.getBoundingClientRect();
      setDragOffset({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      });
      setIsDragging(true);
    }
  };

  const handleResizeMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setResizeStart({
      x: e.clientX,
      y: e.clientY,
      width: size.width,
      height: size.height,
    });
    setIsResizing(true);
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging) {
      const newX = e.clientX - dragOffset.x;
      const newY = e.clientY - dragOffset.y;

      // Keep panel within viewport bounds
      const maxX = window.innerWidth - size.width;
      const maxY = window.innerHeight - size.height;

      setPosition({
        x: Math.max(0, Math.min(newX, maxX)),
        y: Math.max(0, Math.min(newY, maxY)),
      });
    }

    if (isResizing) {
      const deltaX = e.clientX - resizeStart.x;
      const deltaY = e.clientY - resizeStart.y;

      const newWidth = Math.max(400, resizeStart.width + deltaX); // Min width 400px
      const newHeight = Math.max(300, resizeStart.height + deltaY); // Min height 300px

      // Keep panel within viewport bounds
      const maxWidth = window.innerWidth - position.x;
      const maxHeight = window.innerHeight - position.y;

      setSize({
        width: Math.min(newWidth, maxWidth),
        height: Math.min(newHeight, maxHeight),
      });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
    setIsResizing(false);
  };

  useEffect(() => {
    if (isDragging || isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, isResizing, dragOffset, resizeStart, size, position]);

  // Handle escape key to close panel
  useEffect(() => {
    const handleEscapeKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscapeKey);
      return () => {
        document.removeEventListener('keydown', handleEscapeKey);
      };
    }
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <>
      {/* Draggable Panel */}
      <div
        ref={panelRef}
        className="fixed z-50 overflow-hidden rounded-lg border-2 border-gray-800 bg-white shadow-2xl"
        style={{
          left: position.x,
          top: position.y,
          width: size.width,
          height: size.height,
        }}
      >
        <div
          className="flex cursor-move items-center justify-between border-b bg-gray-50 px-4 py-3 dark:bg-gray-800"
          onMouseDown={handleMouseDown}
        >
          <div className="flex items-center gap-2">
            <GripVertical className="h-4 w-4 text-gray-400" />
            <h4 className="text-lg font-semibold text-white">
              Prompt Writing Cheat Sheet
            </h4>
          </div>
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 rounded-full text-white"
            onClick={onClose}
          >
            <X size={16} />
          </Button>
        </div>
        <ScrollArea className="h-[calc(100%-60px)]">
          <div className="space-y-6 p-4">
            {/* Variables Section */}
            <section>
              <h5 className="mb-3 text-base font-semibold text-blue-600">
                📝 Variables
              </h5>
              <p className="mb-3 text-sm text-gray-600">
                Here are the variables that would be replaced by the system:
              </p>
              <div className="space-y-2">
                <div className="rounded-md bg-gray-50 p-3">
                  <div className="mb-1 flex items-center gap-2">
                    <code className="rounded bg-blue-100 px-2 py-1 font-mono text-sm text-blue-800">
                      {`{{CURRENT_DATE}}`}
                    </code>
                    <span className="text-gray-500">→</span>
                    <span className="text-sm text-gray-600">
                      It will replace it with the current date
                    </span>
                  </div>
                </div>
                <div className="rounded-md bg-gray-50 p-3">
                  <div className="mb-1 flex items-center gap-2">
                    <code className="rounded bg-blue-100 px-2 py-1 font-mono text-sm text-blue-800">
                      {`{{NAME}}`}
                    </code>
                    <span className="text-gray-500">→</span>
                    <span className="text-sm text-gray-600">
                      It will replace it with the current user's name
                    </span>
                  </div>
                </div>
                 <div className="rounded-md bg-gray-50 p-3">
                  <div className="mb-1 flex items-center gap-2">
                    <code className="rounded bg-blue-100 px-2 py-1 font-mono text-sm text-blue-800">
                      {`{{EMAIL}}`}
                    </code>
                    <span className="text-gray-500">→</span>
                    <span className="text-sm text-gray-600">
                      It will replace it with the current user's email
                    </span>
                  </div>
                </div>
              </div>
              <div className="mt-3 border-l-4 border-green-400 bg-green-50 p-3">
                <p className="text-sm text-gray-600">
                  <strong>Example:</strong> "When you search please use{' '}
                  {`{{CURRENT_DATE}}`}" would translate to "When you search
                  please use May 21, 2025."
                </p>
              </div>
            </section>

            {/* Tool Control Section */}
            <section>
              <h5 className="mb-3 text-base font-semibold text-purple-600">
                🔧 Tool Control
              </h5>
              <p className="mb-3 text-sm text-gray-600">
                By default, the LLM will decide whether to call tool or not.
                However, if you want to make sure the tool is called, please use
                the following examples below to make sure your tool is called.
              </p>
            </section>

            {/* Collection Access Section */}
            <section>
              <h5 className="mb-3 text-base font-semibold text-orange-600">
                📚 Collection Access
              </h5>
              <div className="mb-3 rounded-md bg-gray-50 p-3">
                <div className="mb-2 flex items-center gap-2">
                  <code className="rounded bg-orange-100 px-2 py-1 font-mono text-sm text-orange-800">
                    file_search the collection name
                  </code>
                  <span className="text-gray-500">→</span>
                  <span className="text-sm text-gray-600">
                    This will make sure the LLM will search a particular
                    collection
                  </span>
                </div>
              </div>
              <div className="space-y-3">
                <div className="border-l-4 border-green-400 bg-green-50 p-3">
                  <p className="mb-1 text-sm font-medium text-gray-600">
                    Example 1:
                  </p>
                  <p className="text-sm text-gray-600">
                    "Please do a{' '}
                    <code className="rounded bg-gray-200 px-1">
                      file_search
                    </code>{' '}
                    on Wilson's Private Collection to get at Wilson's document"
                  </p>
                  <p className="mt-1 text-xs text-gray-600">
                    This will make sure it retrieves Wilson's document.
                  </p>
                </div>
                <div className="border-l-4 border-green-400 bg-green-50 p-3">
                  <p className="mb-1 text-sm font-medium text-gray-600">
                    Example 2:
                  </p>
                  <p className="text-sm text-gray-600">
                    "Please do a{' '}
                    <code className="rounded bg-gray-200 px-1">
                      file_search
                    </code>{' '}
                    first on Wilson's Private Collection"
                  </p>
                  <p className="mt-1 text-xs text-gray-600">
                    This will make the LLM do a search in the specified
                    collections.
                  </p>
                </div>
              </div>
            </section>

            {/* Tool Access Section */}
            <section>
              <h5 className="mb-3 text-base font-semibold text-red-600">
                🛠️ Tool Access
              </h5>

              {/* browse_website */}
              <div className="mb-4">
                <div className="mb-2 rounded-md bg-gray-50 p-3">
                  <div className="mb-1 flex items-center gap-2">
                    <code className="rounded bg-red-100 px-2 py-1 font-mono text-sm text-red-800">
                      browse_website
                    </code>
                    <span className="text-gray-500">→</span>
                    <span className="text-sm text-gray-600">
                      Make the LLM go to a particular site to retrieve data
                    </span>
                  </div>
                </div>
                <div className="border-l-4 border-green-400 bg-green-50 p-3">
                  <p className="mb-1 text-sm font-medium text-gray-600">
                    Example:
                  </p>
                  <p className="text-sm text-gray-600">
                    "Please{' '}
                    <code className="rounded bg-gray-200 px-1">
                      browse_website
                    </code>{' '}
                    to www.mawer.com to understand what this site is about"
                  </p>
                  <p className="mt-1 text-xs text-gray-600">
                    This will make the LLM go to www.mawer.com
                  </p>
                </div>
              </div>

              {/* google_search */}
              <div className="mb-4">
                <div className="mb-2 rounded-md bg-gray-50 p-3">
                  <div className="mb-1 flex items-center gap-2">
                    <code className="rounded bg-red-100 px-2 py-1 font-mono text-sm text-red-800">
                      google_search
                    </code>
                    <span className="text-gray-500">→</span>
                    <span className="text-sm text-gray-600">
                      Make the LLM do a web search
                    </span>
                  </div>
                </div>
                <div className="border-l-4 border-green-400 bg-green-50 p-3">
                  <p className="mb-1 text-sm font-medium text-gray-600">
                    Example:
                  </p>
                  <p className="text-sm text-gray-600">
                    "Please do a{' '}
                    <code className="rounded bg-gray-200 px-1">
                      google_search
                    </code>{' '}
                    for the topic"
                  </p>
                  <p className="mt-1 text-xs text-gray-600">
                    This will make sure the LLM does a web search on what was
                    asked in the prompt
                  </p>
                </div>
              </div>
            </section>

            {/* m42_notes Section */}
            <section>
              <div className="mb-4">
                <div className="mb-2 rounded-md bg-gray-50 p-3">
                  <div className="mb-1 flex items-center gap-2">
                    <code className="rounded bg-red-100 px-2 py-1 font-mono text-sm text-red-800">
                      m42_notes
                    </code>
                    <span className="text-gray-500">→</span>
                    <span className="text-sm text-gray-600">
                      Do a RAG search for m42_notes. It also includes filter
                      capabilities
                    </span>
                  </div>
                </div>

                <div className="mb-3">
                  <h6 className="mb-2 text-sm font-medium">
                    Filter Capabilities:
                  </h6>
                  <div className="space-y-2 px-3 text-sm">
                    <div className="flex items-center gap-2">
                      <code className="rounded bg-blue-100 px-2 py-1 font-mono text-xs text-blue-800">
                        m42NoteId
                      </code>
                      <span className="text-gray-500">→</span>
                      <span className="text-sm text-gray-600">
                        Note Id from m42 eg: 391478
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <code className="rounded bg-blue-100 px-2 py-1 font-mono text-xs text-blue-800">
                        m42Category
                      </code>
                      <span className="text-gray-500">→</span>
                      <span className="text-sm text-gray-600">
                        Notes category in M42
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <code className="rounded bg-blue-100 px-2 py-1 font-mono text-xs text-blue-800">
                        m42CompanyName
                      </code>
                      <span className="text-gray-500">→</span>
                      <span className="text-sm text-gray-600">
                        M42 Company Name
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <code className="rounded bg-blue-100 px-2 py-1 font-mono text-xs text-blue-800">
                        m42Author
                      </code>
                      <span className="text-gray-500">→</span>
                      <span className="text-sm text-gray-600">
                        the username without http://mawer.com
                      </span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="border-l-4 border-green-400 bg-green-50 p-3">
                    <p className="mb-1 text-sm font-medium text-gray-600">
                      Example 1:
                    </p>
                    <p className="text-sm text-gray-600">
                      "Please give me the latest 5 notes on{' '}
                      <code className="rounded bg-gray-200 px-1">
                        m42Category:ESG
                      </code>{' '}
                      and summarize what they are."
                    </p>
                    <p className="mt-1 text-xs text-gray-600">
                      Which will give me the latest 5 notes on ESG and also
                      summarize the notes.
                    </p>
                  </div>
                  <div className="border-l-4 border-green-400 bg-green-50 p-3">
                    <p className="mb-1 text-sm font-medium text-gray-600">
                      Example 2:
                    </p>
                    <p className="text-sm text-gray-600">
                      "Can you give me{' '}
                      <code className="rounded bg-gray-200 px-1">
                        m42CompanyName:Fortis
                      </code>{' '}
                      and give me{' '}
                      <code className="rounded bg-gray-200 px-1">
                        m42Author:mrutherford
                      </code>{' '}
                      sentiment on it?"
                    </p>
                  </div>
                  <div className="border-l-4 border-green-400 bg-green-50 p-3">
                    <p className="mb-1 text-sm font-medium text-gray-600">
                      Example 3:
                    </p>
                    <p className="text-sm text-gray-600">
                      "Please do a{' '}
                      <code className="rounded bg-gray-200 px-1">
                        m42_notes
                      </code>{' '}
                      search for my question."
                    </p>
                  </div>
                </div>
              </div>
            </section>

            {/* email_tool Section */}
            <section>
              <div className="mb-4">
                <div className="mb-2 rounded-md bg-gray-50 p-3">
                  <div className="mb-1 flex items-center gap-2">
                    <code className="rounded bg-red-100 px-2 py-1 font-mono text-sm text-red-800">
                      email_tool
                    </code>
                    <span className="text-gray-500">→</span>
                    <span className="text-sm text-gray-600">
                      This will allow you to email the result of your
                      conversation
                    </span>
                  </div>
                </div>

                <div className="mb-3 border-l-4 border-yellow-400 bg-yellow-50 p-3">
                  <p className="mb-1 text-sm font-medium text-gray-600">
                    To use this you need to add the following prompt:
                  </p>
                  <code className="mt-1 block rounded bg-gray-200 px-2 py-1 font-mono text-xs text-gray-800">
                    {`{'emails': [{'email': 'an email address'}], 'subject': 'subject of email', 'message': 'message of email'}`}
                  </code>
                </div>

                <div className="border-l-4 border-green-400 bg-green-50 p-3">
                  <p className="mb-1 text-sm font-medium text-gray-600">
                    Example:
                  </p>
                  <p className="text-sm text-gray-600">
                    "You also have access to the email Tool which has the
                    following prompt:
                    <code className="rounded bg-gray-200 px-1 text-xs">
                      {`{'emails': [{'email': 'an email address'}], 'subject': 'subject of email', 'message': 'message of email'}`}
                    </code>
                    . If the user wants to send an email out do not call the
                    query tool. You will be helping the user to collect all the
                    information prior to calling the email tool. For the
                    message, please return in html format so that styles can be
                    persisted to the message."
                  </p>
                </div>
              </div>
            </section>

            {/* doc_gen_tool Section */}
            <section>
              <div className="mb-4">
                <div className="mb-2 rounded-md bg-gray-50 p-3">
                  <div className="mb-1 flex items-center gap-2">
                    <code className="rounded bg-red-100 px-2 py-1 font-mono text-sm text-red-800">
                      doc_gen_tool
                    </code>
                    <span className="text-gray-500">→</span>
                    <span className="text-sm text-gray-600">
                      Generate a Word, Excel, or PowerPoint
                    </span>
                  </div>
                </div>

                <div className="border-l-4 border-green-400 bg-green-50 p-3">
                  <p className="mb-1 text-sm font-medium text-gray-600">
                    Example:
                  </p>
                  <p className="text-sm text-gray-600">
                    "Can you use{' '}
                    <code className="rounded bg-gray-200 px-1">
                      doc_gen_tool
                    </code>{' '}
                    to generate file for the result."
                  </p>
                </div>
              </div>
            </section>
          </div>
        </ScrollArea>

        {/* Resize Handle */}
        <div
          className="absolute right-[4px] bottom-[4px] h-4 w-4 cursor-se-resize"
          onMouseDown={handleResizeMouseDown}
        >
          <MoveDiagonal2 className="h-4 w-4 text-gray-400 hover:text-gray-600" />
        </div>
      </div>
    </>
  );
}
