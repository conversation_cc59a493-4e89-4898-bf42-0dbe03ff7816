'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useCreateSupportTicket } from '@/features/support/api/useCreateSupportTicket';
import { ISupportTicketData } from '@/types/support';
import { useMsal } from '@azure/msal-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { ExternalLink, Loader2 } from 'lucide-react';
import Link from 'next/link';

const APP_NAME = 'Chuck';

interface SupportFormProps {
  onClose: () => void;
}

// Define ticket types
const TICKET_TYPES = [
  { value: 'Bug', label: 'Bug' },
  { value: 'Feature Request', label: 'Feature Request' },
  { value: 'Feedback', label: 'Feedback' },
  { value: 'Support', label: 'Support' },
];

// Zod schema for validation
const supportFormSchema = z.object({
  type: z.string().min(1, 'Type is required'),
  subject: z.string().min(1, 'Subject is required'),
  message: z.string().min(1, 'Message is required'),
});

type FormData = z.infer<typeof supportFormSchema>;

export default function SupportForm({ onClose }: SupportFormProps) {
  const { instance } = useMsal();
  const account = instance.getActiveAccount();

  const [app] = useState(APP_NAME);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
  } = useForm<FormData>({
    resolver: zodResolver(supportFormSchema),
    defaultValues: {
      type: 'Support',
      subject: '',
      message: '',
    },
  });

  const { mutate: submitSupportTicket, isPending } = useCreateSupportTicket({
    onSuccessCallback: () => {
      // Reset the form
      reset();
      // Close the popover
      setTimeout(() => {
        onClose();
      }, 500); // Add a small delay to allow the toast to be seen
    },
  });

  const onSubmit = (data: FormData) => {
    if (!account) {
      return;
    }

    const supportTicketData: ISupportTicketData = {
      name: account.name || 'Unknown User',
      email: account.username || account.localAccountId || '',
      app,
      type: data.type,
      subject: data.subject,
      message: data.message,
    };

    submitSupportTicket(supportTicketData);
  };

  return (
    <div className="flex flex-col gap-4 p-2">
      <div>
        <div className="text-primary text-xl font-bold">Mawer Support</div>
        <div className="text-muted-foreground text-sm">
          Please let us know your thoughts and feedback.
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="type">Type</Label>
        <div className="w-full">
          <Select
            defaultValue="Support"
            onValueChange={(value) => setValue('type', value)}
          >
            <SelectTrigger
              id="type"
              className={`w-full ${errors.type ? 'border-destructive' : ''}`}
            >
              <SelectValue placeholder="Select type" />
            </SelectTrigger>
            <SelectContent>
              {TICKET_TYPES.map((type) => (
                <SelectItem key={type.value} value={type.value}>
                  {type.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        {errors.type && (
          <p className="text-destructive text-sm">{errors.type.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="subject">Subject</Label>
        <Input
          id="subject"
          placeholder="Subject"
          {...register('subject')}
          className={errors.subject ? 'border-destructive' : ''}
        />
        {errors.subject && (
          <p className="text-destructive text-sm">{errors.subject.message}</p>
        )}
      </div>

      <div className="space-y-2">
        <Label htmlFor="message">Message</Label>
        <Textarea
          id="message"
          placeholder="Message"
          outline
          {...register('message')}
          className={`min-h-[150px] ${errors.message ? 'border-destructive' : ''}`}
        />
        {errors.message && (
          <p className="text-destructive text-sm">{errors.message.message}</p>
        )}
      </div>

      <div className="mt-4 flex items-center justify-between">
        <Link
          className="text-primary flex items-center gap-2 text-sm underline-offset-4 hover:underline"
          target="_blank"
          href={
            process.env.NEXT_PUBLIC_SUPPORT_PLATFORM_URL ||
            'https://trade.mawer.com/support'
          }
        >
          Support Platform
          <ExternalLink size={16} />
        </Link>
        <Button onClick={handleSubmit(onSubmit)} disabled={isPending} size="sm">
          {isPending ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Submitting...
            </>
          ) : (
            'Submit Ticket'
          )}
        </Button>
      </div>
    </div>
  );
}
