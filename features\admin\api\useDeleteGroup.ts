import { useMutation, useQueryClient, QueryKey } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import axiosInstanceUi from '@/lib/axiosInstance.ui';
import { API_CONFIG } from '@/config/api';
import { toast } from 'sonner';

// Function to delete a group by its ID
const deleteGroupById = async (groupId: string): Promise<void> => {
  await axiosInstanceUi.delete(`${API_CONFIG.admin.group}/${groupId}`);
};

// Define the query key for the group list
const groupListQueryKey: QueryKey = [API_CONFIG.admin.group];

export function useDeleteGroup({
  onSuccessCallback,
}: {
  onSuccessCallback?: () => void;
} = {}) {
  const queryClient = useQueryClient();
  const router = useRouter();

  return useMutation<void, Error, string>({
    mutationFn: (groupId) => deleteGroupById(groupId),
    onSuccess: (_, groupId) => {
      console.log(`Group with ID ${groupId} deleted successfully.`);
      toast.success('Group deleted successfully!');

      // Invalidate the group list query to refresh the UI
      queryClient.invalidateQueries({ queryKey: groupListQueryKey });

      // Remove the specific group query data from the cache
      queryClient.removeQueries({
        queryKey: [API_CONFIG.admin.group, groupId],
      });

      // Call the success callback if provided
      onSuccessCallback?.();
    },
    onError: (error) => {
      console.error('Error deleting group:', error);
      toast.error(`Failed to delete group: ${error.message}`);
    },
  });
}
