'use client';

import { Menu } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useSidebar } from '@/contexts/SidebarContext';
import { cn } from '@/lib/utils';

export default function MobileMenuButton() {
  const { store: sidebarStore } = useSidebar();

  return (
    <Button
      type="button"
      variant="ghost"
      size="icon"
      className={cn(
        'bg-accent tw-border fixed top-2 left-2 z-30 h-10 w-10 rounded-md p-0 shadow',
        sidebarStore.isMobile ? 'flex' : 'hidden',
        sidebarStore.isOpen && 'bg-accent'
      )}
      onClick={sidebarStore.toggleSidebar}
      aria-label="Toggle menu"
    >
      <Menu className="h-8 w-8" />
    </Button>
  );
}
