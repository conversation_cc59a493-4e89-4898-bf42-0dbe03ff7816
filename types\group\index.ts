import { Permission } from '@/lib/permissions/types';

export interface IGroupDataBase {
  id: string;
  name: string;
  description: string | null;
  meta?: {
    personal?: boolean;
    perms: Permission[];
  };
  owner_id?: string;
}

// Interface for user data in group response
export interface IGroupUser {
  id: string;
  name: string;
}

export interface IGroupData extends IGroupDataBase {
  created_at: string;
  modified_at: string;
  owner_id: string;
  owner_name?: string;
  user_count: number;
  userIds?: string[];
  users?: IGroupUser[];
}

export interface IGroupCreate {
  name: string;
  description: string;
  userIds?: string[];
}
