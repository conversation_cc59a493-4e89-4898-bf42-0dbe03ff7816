import { useState } from 'react';
import { toast } from 'sonner';

/**
 * Hook for downloading a document from a collection
 */
export const useDocumentDownload = () => {
  const [isDownloading, setIsDownloading] = useState<string | null>(null);

  /**
   * Download a document from a collection
   * @param collectionId - The ID of the collection
   * @param documentId - The ID of the document
   * @param documentName - The name of the document (for toast messages)
   */
  const downloadDocument = async (
    collectionId: string,
    documentId: string,
    documentName: string
  ) => {
    if (!collectionId || !documentId) {
      toast.error('Collection ID and Document ID are required');
      return;
    }

    try {
      setIsDownloading(documentId);

      // Create a toast notification
      toast.loading(`Downloading ${documentName}...`);

      // Make the request to the API
      const response = await fetch(
        `/api/download-document/${collectionId}/${documentId}`,
        {
          method: 'GET',
        }
      );

      if (!response.ok) {
        throw new Error(`Error downloading document: ${response.statusText}`);
      }

      // Get the blob from the response
      const blob = await response.blob();

      // Get the filename from the Content-Disposition header if available
      const contentDisposition = response.headers.get('Content-Disposition') || '';
      const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/);
      const filename = filenameMatch ? filenameMatch[1] : documentName;

      // Create a URL for the blob
      const url = window.URL.createObjectURL(blob);

      // Create a link element and click it to download the file
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();

      // Clean up
      window.URL.revokeObjectURL(url);
      document.body.removeChild(link);

      // Show success toast
      toast.success(`Downloaded ${documentName}`);
    } catch (error) {
      console.error('Error downloading document:', error);
      toast.error(`Failed to download ${documentName}`);
    } finally {
      setIsDownloading(null);
    }
  };

  return {
    downloadDocument,
    isDownloading,
  };
};
