import { useMutation, useQueryClient, QueryKey } from '@tanstack/react-query';
import axiosInstanceUi from '@/lib/axiosInstance.ui';
import { API_CONFIG } from '@/config/api';
import { ICollectionData, ICollectionCreate } from '@/types/collection'; // Assuming update payload matches create
import { toast } from 'sonner';

// Function to update an existing collection via PUT/PATCH request
const updateCollection = async ({
  collectionId,
  payload,
}: {
  collectionId: string;
  payload: ICollectionCreate;
}): Promise<void> => {
  // Assuming API returns the updated collection
  // Using PUT - change to PATCH if your API requires it
  await axiosInstanceUi.patch<void>(
    // Use the client-side proxy route
    `${API_CONFIG.workspace.collection}/${collectionId}`,
    payload
  );
};

// Define query keys
const collectionListQueryKey: QueryKey = [API_CONFIG.workspace.collection];
const collectionDetailQueryKey = (collectionId: string): QueryKey => [
  API_CONFIG.workspace.collection,
  collectionId,
];

export function useUpdateCollection({
  onSuccessCallback,
}: {
  onSuccessCallback?: () => void;
} = {}) {
  const queryClient = useQueryClient();

  return useMutation<
    void, // Return type
    Error, // Error type
    { collectionId: string; payload: ICollectionCreate } // Input type for mutationFn
  >({
    mutationFn: updateCollection,
    onSuccess: (updatedCollection, variables) => {
      console.log('Collection updated successfully:', updatedCollection);
      toast.success(`Collection updated successfully!`);

      // Invalidate the collection list query
      queryClient.invalidateQueries({ queryKey: collectionListQueryKey });
      // Invalidate the specific collection query
      queryClient.invalidateQueries({
        queryKey: collectionDetailQueryKey(variables.collectionId),
      });

      // Optionally, update the cache directly
      // queryClient.setQueryData(collectionDetailQueryKey(variables.collectionId), updatedCollection);

      // Call the success callback if provided
      onSuccessCallback?.();
    },
    onError: (error, variables) => {
      console.error(
        `Error updating collection ${variables.collectionId}:`,
        error
      );
      toast.error(`Error updating collection: ${error.message}`);
    },
  });
}
