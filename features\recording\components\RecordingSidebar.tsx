'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Plus, Play, Trash2, Edit2 } from 'lucide-react';
import {
  useRecordings,
  useDeleteRecording,
  useUpdateRecording,
} from '../api/useRecordings';
import { Input } from '@/components/ui/input';

interface RecordingSidebarProps {
  selectedRecordingId: string | null;
  onSelectRecording: (recordingId: string) => void;
  onNewRecording: () => void;
}

export function RecordingSidebar({
  selectedRecordingId,
  onSelectRecording,
  onNewRecording,
}: RecordingSidebarProps) {
  const { data: recordings, isLoading } = useRecordings();
  const deleteRecording = useDeleteRecording();
  const updateRecording = useUpdateRecording();
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editName, setEditName] = useState('');

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleDateString();
  };

  const handleEdit = (recording: any) => {
    setEditingId(recording.id);
    setEditName(recording.name);
  };

  const handleSaveEdit = async (recordingId: string) => {
    await updateRecording.mutateAsync({
      recordingId,
      data: { name: editName },
    });
    setEditingId(null);
  };

  const handleCancelEdit = () => {
    setEditingId(null);
    setEditName('');
  };

  const handleDelete = async (recordingId: string) => {
    if (confirm('Are you sure you want to delete this recording?')) {
      await deleteRecording.mutateAsync(recordingId);
    }
  };

  return (
    <div className="z-50 flex h-full w-80 flex-col overflow-y-auto border-r border-gray-200 bg-gray-50 sm:bg-white sm:shadow-lg md:bg-gray-50">
      <div className="border-b border-gray-200 p-4">
        <Button
          onClick={onNewRecording}
          className="flex w-full items-center gap-2"
        >
          <Plus size={16} />
          New Recording
        </Button>
      </div>

      <ScrollArea className="flex-1 p-4">
        {isLoading ? (
          <div className="text-center text-gray-500">Loading recordings...</div>
        ) : recordings?.length === 0 ? (
          <div className="text-center text-gray-500">No recordings yet</div>
        ) : (
          <div className="space-y-2">
            {recordings?.map((recording) => (
              <div
                key={recording.id}
                className={`cursor-pointer rounded-lg border p-3 pr-8 transition-colors ${
                  selectedRecordingId === recording.id
                    ? 'border-blue-200 bg-blue-50'
                    : 'border-gray-200 bg-white hover:bg-gray-50'
                }`}
                onClick={() => onSelectRecording(recording.id)}
                // Add min-w-0 to prevent overflow
                style={{ minWidth: 0 }}
              >
                <div className="flex items-start justify-between">
                  <div className="min-w-0 flex-1">
                    {editingId === recording.id ? (
                      <div className="space-y-2">
                        <Input
                          value={editName}
                          onChange={(e) => setEditName(e.target.value)}
                          className="text-sm"
                          autoFocus
                        />
                        <div className="flex gap-1">
                          <Button
                            size="sm"
                            onClick={() => handleSaveEdit(recording.id)}
                            className="text-xs"
                          >
                            Save
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={handleCancelEdit}
                            className="text-xs"
                          >
                            Cancel
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <>
                        <div className="flex items-center gap-2">
                          <Play size={12} className="text-gray-400" />
                          <h3 className="truncate text-sm font-medium">
                            {recording.name}
                          </h3>
                        </div>
                        <p className="mt-1 text-xs text-gray-500">
                          {formatDuration(recording.duration)} •{' '}
                          {formatDate(recording.created)}
                        </p>
                        <p className="text-xs text-gray-400 capitalize">
                          {recording.status}
                        </p>
                      </>
                    )}
                  </div>
                  {editingId !== recording.id && (
                    <div className="ml-2 flex gap-1">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEdit(recording);
                        }}
                        className="h-6 w-6 flex-shrink-0 p-0 sm:h-8 sm:w-8"
                        style={{
                          color: '#2563eb', // blue-600 for visibility
                        }}
                      >
                        {/* Responsive icon size */}

                        <span>
                          <Edit2 size={18} />
                        </span>
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDelete(recording.id);
                        }}
                        className="h-6 w-6 flex-shrink-0 p-0 sm:h-8 sm:w-8"
                        style={{
                          color: '#ef4444', // red-500 for visibility
                        }}
                      >
                        {/* Responsive icon size */}

                        <span>
                          <Trash2 size={18} />
                        </span>
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </ScrollArea>
    </div>
  );
}
