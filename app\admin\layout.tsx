'use client';

import { ReactNode } from 'react';
import { ProtectedRoute } from '@/contexts/GlobalContext';
import MainLayout from '@/components/MainLayout';
import AdminLayout from '@/features/admin/components/AdminLayout';

export default function RootLayout({ children }: { children: ReactNode }) {
  return (
    <ProtectedRoute>
      <MainLayout>
        <AdminLayout>{children}</AdminLayout>
      </MainLayout>
    </ProtectedRoute>
  );
}
