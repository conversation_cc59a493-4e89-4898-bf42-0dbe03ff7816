# Shared Components

This directory contains shared components that are used across multiple features in the application. These components are not specific to any particular feature and can be imported and used anywhere in the application.

## Structure

```
components/
  ├── ui/              # UI components (buttons, inputs, etc.)
  ├── LoadingSpinner.tsx
  ├── MainLayout.tsx
  ├── MobileMenuButton.tsx
  ├── sidebar.tsx
  ├── SidebarLink.tsx
  └── theme-provider.tsx
```

## Import Examples

```tsx
// Import directly from the component file
import Button from '@/components/ui/button';
import MainLayout from '@/components/MainLayout';
```

## Guidelines

1. Components in this directory should be truly shared and reusable across multiple features
2. If a component is only used within a single feature, it should be placed in that feature's components directory
3. UI components (buttons, inputs, etc.) should be placed in the `ui` directory
4. Layout components should be placed at the root of the components directory
