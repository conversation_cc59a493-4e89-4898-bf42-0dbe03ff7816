'use client';

import React from 'react';
import {
  Sheet,
  She<PERSON><PERSON>onte<PERSON>,
  Sheet<PERSON>eader,
  SheetTitle,
} from '@/components/ui/sheet';
import { ScrollArea } from '@/components/ui/scroll-area';
import { IToolData } from '@/types/model';
import ToolForm from './ToolForm';

interface ToolSheetProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  tool: IToolData;
}

export default function ToolSheet({
  isOpen,
  onOpenChange,
  tool,
}: ToolSheetProps) {
  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetContent
        className="sm:max-w-[500px]"
        onEscapeKeyDown={(e: KeyboardEvent) => e.preventDefault()}
      >
        <ScrollArea className="h-full w-full pr-6">
          <SheetHeader className="pt-4 pl-4">
            <SheetTitle className="flex items-center gap-2">
              Edit Tool
            </SheetTitle>
          </SheetHeader>
          <ToolForm onOpenChange={onOpenChange} tool={tool} />
        </ScrollArea>
      </SheetContent>
    </Sheet>
  );
}
