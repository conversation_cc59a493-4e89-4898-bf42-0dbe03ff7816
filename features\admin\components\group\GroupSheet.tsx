'use client';

import React from 'react';

import {
  Sheet,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTitle,
} from '@/components/ui/sheet';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useFetchGroupById } from '@/features/admin/api/useFetchGroupById';
import LoadingSpinner from '@/components/LoadingSpinner';
import GroupForm from './GroupForm';
import { IGroupData } from '@/types/group';

interface GroupSheetProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  group?: IGroupData | null; // Input group for editing
}

export default function GroupSheet({
  isOpen,
  onOpenChange,
  group,
}: GroupSheetProps) {
  const isEditMode = !!group?.id;

  // Fetch detailed data only in edit mode when the sheet is open
  const {
    data: detailedGroupData,
    isLoading: isFetchingDetailedData,
    isError,
  } = useFetchGroupById(group?.id, {
    enabled: isOpen && isEditMode,
  });

  // Set the sheet title
  const sheetTitle = isEditMode ? 'Edit Group' : 'Create Group';

  // Determine the data to pass to the form
  // In edit mode, wait for detailed data to load, otherwise pass null
  const formGroupData = isEditMode ? detailedGroupData : null;

  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetContent
        className="sm:max-w-[600px]"
        onEscapeKeyDown={(e: KeyboardEvent) => e.preventDefault()} // Keep ESC disabled
      >
        <ScrollArea className="h-full w-full pr-6">
          <SheetHeader className="pt-4 pl-4">
            <SheetTitle className="flex items-center gap-2">
              {sheetTitle}
            </SheetTitle>
          </SheetHeader>
          {/* Show loading spinner only in edit mode while fetching */}
          {isEditMode && isFetchingDetailedData ? (
            <div className="flex h-[200px] items-center justify-center p-8">
              <LoadingSpinner />
            </div>
          ) : isEditMode && isError ? ( // Handle fetch error in edit mode
            <div className="text-destructive p-4">
              Failed to load group details.
            </div>
          ) : (
            // Render form in create mode, or after data loads/fails in edit mode
            <GroupForm onOpenChange={onOpenChange} group={formGroupData} />
          )}
        </ScrollArea>
      </SheetContent>
    </Sheet>
  );
}
