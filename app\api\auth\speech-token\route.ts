import { NextRequest, NextResponse } from 'next/server';
import axiosInstanceApi from '@/lib/axiosInstance.api';

export async function GET(request: NextRequest) {
  try {
    const apiResponse = await axiosInstanceApi.get('/auth/speech-token');

    return NextResponse.json(apiResponse.data, { status: apiResponse.status });
  } catch (error: any) {
    console.error(error);
    const status = error.response?.status || 500;
    const message = error.response?.data?.message || 'Internal Server Error';
    return NextResponse.json({ message }, { status });
  }
}
