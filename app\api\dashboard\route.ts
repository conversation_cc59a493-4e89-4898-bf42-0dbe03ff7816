import { NextRequest, NextResponse } from 'next/server';
import axiosInstanceApi from '@/lib/axiosInstance.api';
import { API_CONFIG } from '@/config/api';

export async function GET(request: NextRequest) {
  try {
    const authorization = request.headers.get('Authorization');
    const targetPath = API_CONFIG.admin.dashboard;

    console.log(
      `[API Route Proxy] Forwarding GET ${targetPath} with Auth: ${!!authorization}`
    );

    const apiResponse = await axiosInstanceApi.get(targetPath, {
      headers: {
        ...(authorization && { Authorization: authorization }),
      },
    });

    return NextResponse.json(apiResponse.data, { status: apiResponse.status });
  } catch (error: any) {
    console.error(
      `[API Route Proxy Error] GET dashboard:`,
      error.response?.data || error.message
    );
    const status = error.response?.status || 500;
    const message = error.response?.data?.message || 'Error fetching dashboard data';
    return NextResponse.json({ message }, { status });
  }
}
