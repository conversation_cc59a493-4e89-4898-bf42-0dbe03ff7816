import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';

export async function GET(
  request: NextRequest,
  props: { params: Promise<{ collectionId: string; documentId: string }> }
) {
  try {
    const authorization = request.headers.get('Authorization');
    const { collectionId, documentId } = await props.params;

    if (!collectionId || !documentId) {
      return NextResponse.json(
        { message: 'Collection ID and Document ID are required' },
        { status: 400 }
      );
    }

    // Make the request to the backend API
    const apiResponse = await axios.get(
      `${process.env.INGESTION_SERVICE_URL}/api/download-document/${collectionId}/${documentId}?code=${process.env.INGESTION_SERVICE_URL_CODE}`,
      {
        headers: {
          ...(authorization && { Authorization: authorization }),
        },
        responseType: 'arraybuffer', // Important for binary data
      }
    );

    // Get the content type and filename from the response headers
    const contentType =
      apiResponse.headers['content-type'] || 'application/octet-stream';
    const contentDisposition = apiResponse.headers['content-disposition'] || '';
    const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/);
    const filename = filenameMatch
      ? filenameMatch[1]
      : `document-${documentId}.pdf`;

    // Create a new response with the file data
    const response = new NextResponse(apiResponse.data, {
      status: apiResponse.status,
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': `attachment; filename="${filename}"`,
      },
    });

    return response;
  } catch (error: any) {
    console.error('Error downloading document:', error);

    // Handle specific error responses from the backend
    if (error.response) {
      const status = error.response.status || 500;
      const message =
        error.response.data?.message || 'Error downloading document';
      return NextResponse.json({ message }, { status });
    }

    // Handle other errors
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
