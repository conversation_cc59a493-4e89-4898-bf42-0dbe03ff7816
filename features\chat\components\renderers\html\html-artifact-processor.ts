import {
  ContentProcessor,
  ContentProcessorContext,
  ProcessedContent,
} from '../../content-processors/types';
import hljs from 'highlight.js';

export class HtmlArtifactProcessor implements ContentProcessor {
  /**
   * The HtmlArtifactProcessor doesn’t require special initialization like Mermaid.
   * It only generates a button and stores the artifact, which will be handled later.
   *
   * Actual rendering happens in a separate iframe, triggered by
   * ChatMessageContentRender when an artifact is selected—so no setup is needed here.
   */
  initialize(ctx: ContentProcessorContext): void {
    // No initialization needed for HTML artifact processor
  }

  canProcess(ctx: ContentProcessorContext): boolean {
    return ctx.lang === 'html' || ctx.lang === 'html-artifact';
  }

  process(ctx: ContentProcessorContext): ProcessedContent {
    const uniqueId = `html-artifact-${Date.now()}-${Math.floor(
      Math.random() * 10000
    )}`;

    const artifact = {
      type: 'html',
      content: ctx.code,
      title: `HTML Content`,
    };

    // Highlight the code for display
    const highlightedCode = hljs.highlight(ctx.code, {
      language: 'html',
      ignoreIllegals: true,
    }).value;

    const buttonId = `render-html-button-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
    const buttonHtml = ctx.isStreaming
      ? ''
      : `<button type="button" id="${buttonId}" class="render-html-button absolute right-4 bottom-4 inline-flex h-8
         items-center justify-center rounded-md border border-input bg-background
         px-3 text-xs font-medium ring-offset-background transition-colors hover:bg-accent
         hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-2
         focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none
         disabled:opacity-50" data-artifact-index="0"
         >Preview</button>`;

    return {
      html: `<pre class="relative mb-8 hljs github-dark"><code class="language-html">${highlightedCode.trim()}</code>
             ${buttonHtml}
             </pre>`,
      artifacts: [artifact],
      skipNextProcessors: true,
    };
  }
}
