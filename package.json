{"name": "ui", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "start:standalone": "node server.js", "lint": "next lint", "format:check": "prettier --check .", "format:write": "prettier --write .", "test": "vitest run", "test:watch": "vitest watch", "test:coverage": "vitest run --coverage", "test:ci": "vitest run --reporter=junit --outputFile=./test-results.xml", "test:ui": "vitest --ui"}, "dependencies": {"@azure/msal-browser": "^4.11.0", "@azure/msal-react": "^3.0.10", "@hookform/resolvers": "^5.0.1", "@nivo/bar": "^0.98.0", "@nivo/calendar": "^0.98.0", "@nivo/chord": "^0.98.0", "@nivo/core": "^0.98.0", "@nivo/heatmap": "^0.98.0", "@nivo/line": "^0.98.0", "@nivo/network": "^0.98.0", "@nivo/pie": "^0.98.0", "@nivo/radar": "^0.98.0", "@nivo/radial-bar": "^0.98.0", "@nivo/sunburst": "^0.98.0", "@nivo/treemap": "^0.98.0", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-tooltip": "^1.2.6", "@radix-ui/react-visually-hidden": "^1.2.2", "@tanstack/react-form": "^1.8.0", "@tanstack/react-query": "^5.74.4", "@tanstack/react-table": "^8.21.3", "@tanstack/zod-form-adapter": "^0.42.1", "axios": "^1.8.4", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "dayjs": "^1.11.13", "dompurify": "^3.2.5", "framer-motion": "^12.9.2", "highlight.js": "^11.11.1", "js-cookie": "^3.0.5", "lodash-es": "^4.17.21", "lucide-react": "^0.503.0", "marked": "^15.0.10", "mermaid": "^11.6.0", "microsoft-cognitiveservices-speech-sdk": "^1.43.1", "nanoid": "^5.1.5", "next": "15.3.1", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.1", "recharts": "^2.15.3", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "uuid": "^11.1.0", "zod": "^3.24.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/react-hooks": "^8.0.1", "@types/js-cookie": "^3.0.6", "@types/lodash-es": "^4.17.12", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@vitejs/plugin-react": "^4.4.1", "@vitest/ui": "^3.1.3", "dotenv-cli": "^8.0.0", "eslint": "^9", "eslint-config-next": "15.3.1", "jsdom": "^26.1.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4", "tw-animate-css": "^1.2.8", "typescript": "^5", "vitest": "^3.1.3"}, "packageManager": "pnpm@10.9.0+sha512.0486e394640d3c1fb3c9d43d49cf92879ff74f8516959c235308f5a8f62e2e19528a65cdc2a3058f587cde71eba3d5b56327c8c33a97e4c4051ca48a10ca2d5f"}