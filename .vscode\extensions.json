{
  "recommendations": [
    // Core Web Dev
    "dbaeumer.vscode-eslint", // ESLint integration
    "esbenp.prettier-vscode", // Prettier code formatter
    "bradlc.vscode-tailwindcss", // Tailwind CSS IntelliSense
    "formulahendry.auto-rename-tag", // Auto rename paired HTML/XML tag
    "formulahendry.auto-close-tag", // Auto close HTML/XML tag
    "usernamehw.errorlens", // Highlight errors inline

    // Next.js / React
    "vscodeshift.react-codemod", // React refactoring tools
    "dsznajder.es7-react-js-snippets", // React/Redux/GraphQL/React-Native snippets

    // General Utility
    "eamodio.gitlens", // Git superpowers
    "ms-vscode.vscode-typescript-next", // Use nightly TypeScript build (optional, good for TS features)
    "editorconfig.editorconfig", // Maintain consistent coding styles
    "github.vscode-pull-request-github", // GitHub Pull Requests and Issues
    "ms-azuretools.vscode-docker" // Docker integration
  ]
}
