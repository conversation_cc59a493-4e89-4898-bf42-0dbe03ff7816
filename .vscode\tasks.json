{"version": "2.0.0", "tasks": [{"label": "pnpm: dev", "type": "shell", "command": "pnpm dev", "group": {"kind": "build", "isDefault": true}, "problemMatcher": [], "presentation": {"reveal": "always", "panel": "new"}}, {"label": "pnpm: build", "type": "shell", "command": "pnpm build", "group": "build", "problemMatcher": []}, {"label": "pnpm: lint", "type": "shell", "command": "pnpm lint", "group": "test", "problemMatcher": ["$eslint-stylish"]}, {"label": "pnpm: format", "type": "shell", "command": "pnpm format:write", "problemMatcher": []}]}