import { IFeedbackData } from '../feedback';

export enum MessageRoleEnum {
  USER = 'user',
  ASSISTANT = 'assistant',
}

export enum MessageBlockTypeEnum {
  TEXT = 'text',
  IMAGE = 'image',
  AUDIO = 'audio',
  FILE = 'file',
  HTML = 'html',
}

export interface IMessageConfig {
  modelId: string | null;
  webSearch: boolean;
}

export interface IMessageAttachment {
  name: string;
  type: string;
  size: number;
  content?: string;
  file?: File;
  id: string; // For tracking in envelope protocol
}

// Processed message block from the backend
export interface IMessageBlock {
  block_type: MessageBlockTypeEnum;
  text?: string;
  image?: string | null;
  path?: string | null;
  url?: string | null;
  image_mimetype?: string | null;
  detail?: string | null;
  _blob_id?: string | null;
}

export interface IChat {
  id: string;
  ts?: number;
  created: number;
  modified: number;
  messages: IMessage[];
  name: string;
  pk: string;
  pinned?: boolean;
}

export interface IChatGroup {
  name: string;
  data: IChat[];
}

export interface IArtifact {
  type: string; // 'html', 'iframe', etc.
  content: string; // HTML content or URL for iframe
  title?: string; // Optional title for the artifact
}

export interface IMessage {
  id: string;
  ts: number;
  role: MessageRoleEnum;
  content: string;
  attachments: IMessageAttachment[];
  config?: IMessageConfig;
  feedback?: IFeedbackData;
  artifacts?: IArtifact[];
  tool_calls?: {
    tool_call: {
      tool_call_id: string;
      tool_name: string;
      tool_kwargs: {
        query: string;
      };
    };
    tool_call_result: string;
  }[];
}
