import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import axiosInstanceUi from '@/lib/axiosInstance.ui';
import { API_CONFIG } from '@/config/api';

export interface Recording {
  id: string;
  name: string;
  duration: number;
  status: 'created' | 'recording' | 'processing' | 'completed' | 'failed';
  created: number;
  modified: number;
}

export interface RecordingDetail extends Recording {
  transcription: any[];
  download_url?: string;
  transcription_segments: any[];
}

export function useRecordings() {
  return useQuery<Recording[]>({
    queryKey: ['recordings'],
    queryFn: async () => {
      const response = await axiosInstanceUi.get(
        API_CONFIG.recording.recordings
      );
      return response.data;
    },
  });
}

export function useRecording(recordingId: string) {
  return useQuery<RecordingDetail>({
    queryKey: ['recording', recordingId],
    queryFn: async () => {
      const response = await axiosInstanceUi.get(
        `${API_CONFIG.recording.recording}/${recordingId}`
      );
      return response.data;
    },
    enabled: !!recordingId,
  });
}

export function useCreateRecording() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: { name: string }) => {
      const response = await axiosInstanceUi.post(
        API_CONFIG.recording.recordings,
        data
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['recordings'] });
    },
  });
}

export function useDeleteRecording() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (recordingId: string) => {
      const response = await axiosInstanceUi.delete(
        `${API_CONFIG.recording.recording}/${recordingId}`
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['recordings'] });
    },
  });
}

export function useUpdateRecording() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      recordingId,
      data,
    }: {
      recordingId: string;
      data: { name: string };
    }) => {
      const response = await axiosInstanceUi.patch(
        `${API_CONFIG.recording.recording}/${recordingId}`,
        data
      );
      return response.data;
    },
    onSuccess: (_, { recordingId }) => {
      queryClient.invalidateQueries({ queryKey: ['recordings'] });
      queryClient.invalidateQueries({ queryKey: ['recording', recordingId] });
    },
  });
}

export function useSendRecordingEmail() {
  return useMutation({
    mutationFn: async ({
      recordingId,
      email,
    }: {
      recordingId: string;
      email: string;
    }) => {
      const response = await axiosInstanceUi.post(
        `${API_CONFIG.recording.email}/${recordingId}/email`,
        {
          email,
        }
      );
      return response.data;
    },
  });
}
