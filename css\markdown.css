.chat {
  @apply bg-background flex h-screen flex-col;

  @media (max-width: 768px) {
    padding-bottom: env(
      safe-area-inset-bottom,
      0
    ); /* Handle mobile safe areas */
  }
}

.scrollAreaRoot {
  height: 100%;

  @media (max-width: 768px) {
    padding-bottom: 10px;
  }
}

.message {
  @apply text-foreground text-[17px] break-words whitespace-pre-wrap;

  @media (max-width: 768px) {
    font-size: 15px;
  }
}

.messageContent {
  width: 100%;
}

.userMessage {
  @apply bg-muted text-foreground rounded-3xl dark:bg-neutral-800;

  @media (max-width: 768px) {
    border-radius: 1rem;
    max-width: 85%;
    padding: 8px 12px;
  }
}

.chatInput {
  @apply bg-muted border-border mb-[5px] rounded-3xl border dark:bg-neutral-900;

  @media (max-width: 768px) {
    border-radius: 1rem;
    margin: 0 5px 5px 5px;
    width: calc(100% - 10px);
  }

  .textArea {
    @apply text-foreground border-none bg-transparent px-4 py-2 text-base;

    @media (max-width: 768px) {
      font-size: 14px;
      padding: 6px 12px;
    }

    /* Scrollbar styles */
    &::-webkit-scrollbar {
      width: 8px;
    }
    &::-webkit-scrollbar-track {
      background: transparent;
    }
    &::-webkit-scrollbar-thumb {
      @apply rounded bg-black/20 dark:bg-white/20;
    }
    &::-webkit-scrollbar-thumb:hover {
      @apply bg-black/30 dark:bg-white/30;
    }
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) transparent;

    .dark & {
      scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
    }
  }
}

.markdown {
  width: 100%;
  overflow-x: auto;
}

/* Media query for screens smaller than 1200px */
@media (max-width: 1200px) {
  .markdown {
    overflow-x: auto;
    max-width: 100%;
  }
}

/* Style for code blocks */
.markdown pre {
  background-color: #0c1527 !important;
  color: #f8f8f2; /* Light text color for contrast */
  padding-top: 1em !important; /* Add padding around the code */
  padding-left: 1em !important; /* Add padding around the code */
  padding-right: 1em !important; /* Add padding around the code */
  border-radius: 0.6em; /* Slightly rounded corners */
  overflow-x: auto; /* Add horizontal scrollbar if code is wide */
  font-family:
    Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace; /* Monospace font stack */
  font-size: 0.9em; /* Slightly smaller font size for code blocks */
  line-height: 1.5; /* Adjust line spacing */
  white-space: pre-wrap; /* Preserve whitespace but allow wrapping */
  max-width: 100%;
  width: 100%;
  box-sizing: border-box; /* Include padding in width calculation */
  word-wrap: break-word; /* Break long words to prevent overflow */
  word-break: break-words; /* More aggressive breaking for very long strings */
  position: relative; /* For copy button positioning */
}

/* Syntax highlighting styles */
.markdown .hljs {
  display: block;
  overflow-x: auto;
  padding: 0;
  background: transparent;
  color: inherit;
}

/* Language badge */
.markdown pre::before {
  content: attr(data-language);
  position: absolute;
  top: 0;
  right: 0;
  padding: 0.2em 0.5em;
  font-size: 0.7em;
  color: #f8f8f2;
  background-color: rgba(0, 0, 0, 0.3);
  border-bottom-left-radius: 0.3em;
  border-top-right-radius: 0.3em;
  font-family: sans-serif;
  text-transform: uppercase;
}

/* Highlight.js theme overrides */
.markdown .hljs-keyword,
.markdown .hljs-selector-tag,
.markdown .hljs-built_in,
.markdown .hljs-name,
.markdown .hljs-tag {
  color: #ff79c6;
}

.markdown .hljs-string,
.markdown .hljs-title,
.markdown .hljs-section,
.markdown .hljs-attribute,
.markdown .hljs-literal,
.markdown .hljs-template-tag,
.markdown .hljs-template-variable,
.markdown .hljs-type {
  color: #f1fa8c;
}

.markdown .hljs-comment,
.markdown .hljs-quote,
.markdown .hljs-deletion {
  color: #6272a4;
}

.markdown .hljs-meta,
.markdown .hljs-operator {
  color: #ff79c6;
}

.markdown .hljs-bullet,
.markdown .hljs-link,
.markdown .hljs-params {
  color: #f8f8f2;
}

.markdown .hljs-number,
.markdown .hljs-symbol,
.markdown .hljs-variable,
.markdown .hljs-template-variable,
.markdown .hljs-attr,
.markdown .hljs-selector-attr,
.markdown .hljs-selector-class,
.markdown .hljs-selector-id {
  color: #bd93f9;
}

.markdown .hljs-function,
.markdown .hljs-title.function {
  color: #50fa7b;
}

.markdown .hljs-addition,
.markdown .hljs-attribute,
.markdown .hljs-meta-string,
.markdown .hljs-regexp,
.markdown .hljs-string {
  color: #f1fa8c;
}

/* Media query for screens smaller than 1200px */
@media (max-width: 1200px) {
  .markdown pre {
    max-width: 1000px;
    width: 1000px;
    overflow-x: auto;
  }

  /* Add horizontal scrolling container for pre tags */
  .markdown {
    overflow-x: auto;
    padding-bottom: 10px; /* Add some space for the scrollbar */
  }
}

/* Media query for even smaller screens */
@media (max-width: 1000px) {
  .markdown pre {
    max-width: 100%;
    width: 100%;
  }
}

/* .markdown code {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 85%;
  @apply bg-muted/50 dark:bg-muted/40 rounded-md;
} */

.markdown pre code {
  font-family: inherit; /* Inherit font family from pre */
  background: none; /* Ensure code tag itself has no background override */
  color: inherit; /* Inherit color from pre */
  padding: 0; /* Reset padding if needed */
  font-size: inherit; /* Inherit font size */
  line-height: inherit; /* Inherit line height */
  display: inline-block;
  max-width: 100%;
  overflow-wrap: break-word;
}

/* Media query for screens smaller than 1200px */
@media (max-width: 1200px) {
  .markdown pre code {
    max-width: 1000px;
  }
}

/* Media query for even smaller screens */
@media (max-width: 1000px) {
  .markdown pre code {
    max-width: 100%;
  }
}

/* Style for tables */
.markdown table {
  border-collapse: collapse;
  width: 100%;
  margin: 16px 0;
}

.markdown th,
.markdown td {
  @apply border-border border p-2;
}

.markdown th {
  @apply bg-muted dark:bg-background font-semibold;
}

.markdown tr:nth-child(even) {
  @apply bg-muted/50 dark:bg-background;
}

/* Blockquotes */
.markdown blockquote {
  padding: 0 1em;
  @apply text-muted-foreground border-l-border my-4 border-l-4;
}

/* Links */
.markdown a {
  @apply text-muted-foreground underline;
}

.markdown a:hover {
  @apply underline;
}

/* Images */
.markdown img {
  max-width: 100%;
  height: auto;
  border-radius: 6px;
  margin: 16px 0;
}

/* Lists */
.markdown ul,
.markdown ol {
  padding-left: 2em;
  margin: 16px 0;
}

.markdown li + li {
  margin-top: 0.25em;
}

/* Headers */
.markdown h1,
.markdown h2,
.markdown h3,
.markdown h4,
.markdown h5,
.markdown h6 {
  margin-top: 24px;
  margin-bottom: 16px;
  font-weight: 600;
  line-height: 1.25;
}

.markdown h1 {
  font-size: 2em;
}
.markdown h2 {
  font-size: 1.5em;
  @apply border-border border-b pb-[0.3em];
}
.markdown h3 {
  font-size: 1.25em;
}
.markdown h4 {
  font-size: 1em;
}
.markdown h5 {
  font-size: 0.875em;
}
.markdown h6 {
  font-size: 0.85em;
}

/* Paragraphs */
.markdown p {
  margin: 16px 0;
}
