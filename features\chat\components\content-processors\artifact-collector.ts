import { IArtifact } from '@/types/chat';

export class ArtifactCollector {
  private artifacts: IArtifact[] = [];

  add(artifact: IArtifact): void {
    this.artifacts.push(artifact);
  }

  addAll(artifacts: IArtifact[]): void {
    this.artifacts.push(...artifacts);
  }

  getAll(): IArtifact[] {
    return [...this.artifacts];
  }

  clear(): void {
    this.artifacts = [];
  }
}

export function createArtifactCollector(): ArtifactCollector {
  return new ArtifactCollector();
}
