'use client';

import { useEffect, useState, PropsWithChildren } from 'react';
import { msalInstance } from '@/lib/msalInstance';
import { MsalProvider } from '@azure/msal-react';

export default function MsalBootstrap({ children }: PropsWithChildren) {
  const [ready, setReady] = useState(false);

  useEffect(() => {
    msalInstance.initialize().then(() => setReady(true));
  }, []);

  return ready ? (
    <MsalProvider instance={msalInstance}>{children}</MsalProvider>
  ) : null;
}
