import {
  NegativeFeedbackReason,
  PositiveFeedbackReason,
} from '@/types/feedback';

export const positiveFeedbackReasons: {
  value: PositiveFeedbackReason;
  label: string;
}[] = [
  { value: 'accurate_information', label: 'Accurate information' },
  { value: 'followed_instructions', label: 'Followed instructions perfectly' },
  { value: 'showcased_creativity', label: 'Showcased creativity' },
  { value: 'positive_attitude', label: 'Positive attitude' },
  { value: 'attention_to_detail', label: 'Attention to detail' },
  { value: 'thorough_explanation', label: 'Thorough explanation' },
  { value: 'other', label: 'Other' },
];

export const negativeFeedbackReasons: {
  value: NegativeFeedbackReason;
  label: string;
}[] = [
  { value: 'dont_like_style', label: "Don't like the style" },
  { value: 'too_verbose', label: 'Too verbose' },
  { value: 'not_helpful', label: 'Not helpful' },
  { value: 'not_factually_correct', label: 'Not factually correct' },
  {
    value: 'didnt_follow_instructions',
    label: "Didn't fully follow instructions",
  },
  { value: 'refused_when_shouldnt', label: "Refused when it shouldn't have" },
  { value: 'being_lazy', label: 'Being lazy' },
  { value: 'other', label: 'Other' },
];
