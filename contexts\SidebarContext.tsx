'use client';

import { createContext, useContext, useState, useEffect } from 'react';

const MOBILE_BREAKPOINT = 1280;

interface ISidebarStore {
  isOpen: boolean;
  isMobile: boolean;
  toggleSidebar: () => void;
  closeSidebar: () => void;
  openSidebar: () => void;
}

interface ISidebarContext {
  store: ISidebarStore;
  setStore: React.Dispatch<React.SetStateAction<ISidebarStore>>;
}

const SidebarContext = createContext<ISidebarContext | null>(null);

export const SidebarContextProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [store, setStore] = useState<ISidebarStore>({
    isOpen: true,
    isMobile: false,
    toggleSidebar: () => {
      setStore((prevStore) => ({
        ...prevStore,
        isOpen: !prevStore.isOpen,
      }));
    },
    closeSidebar: () => {
      setStore((prevStore) => ({
        ...prevStore,
        isOpen: false,
      }));
    },
    openSidebar: () => {
      setStore((prevStore) => ({
        ...prevStore,
        isOpen: true,
      }));
    },
  });

  // Check for mobile viewport on mount and window resize
  useEffect(() => {
    const checkMobile = () => {
      const isMobileView = window.innerWidth < MOBILE_BREAKPOINT; // Standard md breakpoint

      setStore((prevStore) => ({
        ...prevStore,
        isMobile: isMobileView,
        // Auto-close sidebar on mobile by default
        isOpen: isMobileView ? false : prevStore.isOpen,
      }));
    };

    // Initial check
    checkMobile();

    // Add resize listener
    window.addEventListener('resize', checkMobile);

    // Cleanup
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  return (
    <SidebarContext.Provider value={{ store, setStore }}>
      {children}
    </SidebarContext.Provider>
  );
};

export const useSidebar = () => {
  return useContext(SidebarContext) as ISidebarContext;
};

export default SidebarContextProvider;
