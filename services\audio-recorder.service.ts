export class AudioRecorderService {
  private mediaRecorder: MediaRecorder | null = null;
  private stream: MediaStream | null = null;
  private wsUrl: string;
  private onAudioData?: (data: ArrayBuffer) => void;

  constructor(wsUrl: string, onAudioData?: (data: ArrayBuffer) => void) {
    this.wsUrl = wsUrl;
    this.onAudioData = onAudioData;
  }

  public async startRecording() {
    try {
      // Get user media
      this.stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          channelCount: 1,
          sampleRate: 16000,
        },
      });

      // Create MediaRecorder
      this.mediaRecorder = new MediaRecorder(this.stream, {
        mimeType: 'audio/webm;codecs=opus',
      });

      // Handle data available
      this.mediaRecorder.ondataavailable = async (event) => {
        if (event.data.size > 0) {
          const arrayBuffer = await event.data.arrayBuffer();

          // If we have a callback, use it (for integration with RecordingService)
          if (this.onAudioData) {
            this.onAudioData(arrayBuffer);
          } else {
            // Fallback to direct WebSocket sending (legacy behavior)
            this.sendAudioData(arrayBuffer);
          }
        }
      };

      // Start recording with small time slices for real-time streaming
      this.mediaRecorder.start(1000); // 250ms chunks

      console.log('Audio recording started');
    } catch (error) {
      console.error('Failed to start audio recording:', error);
      throw error;
    }
  }

  public stopRecording() {
    if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
      this.mediaRecorder.stop();
    }

    if (this.stream) {
      this.stream.getTracks().forEach((track) => track.stop());
      this.stream = null;
    }

    this.mediaRecorder = null;
    console.log('Audio recording stopped');
  }

  private async sendAudioData(audioData: ArrayBuffer) {
    // This is the legacy method for direct WebSocket communication
    // In the new pattern, this would be handled by RecordingService
    try {
      const ws = new WebSocket(this.wsUrl);
      await new Promise((resolve, reject) => {
        ws.onopen = resolve;
        ws.onerror = reject;
      });
      ws.send(audioData);
      ws.close();
    } catch (error) {
      console.error('Failed to send audio data:', error);
    }
  }

  public dispose() {
    this.stopRecording();
  }
}
